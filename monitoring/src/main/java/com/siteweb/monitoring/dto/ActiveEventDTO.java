package com.siteweb.monitoring.dto;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.siteweb.common.serializer.DoubleAllowNullSerializer;
import com.siteweb.common.serializer.DoubleNonNullSerializer;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description ActiveEventDTO
 * @createTime 2022-04-11 14:39:54
 */
@Data
@NoArgsConstructor
public class ActiveEventDTO {
    public ActiveEventDTO(ActiveEvent activeEvent) {
        this.setSequenceId(activeEvent.getSequenceId());
        this.setStationId(activeEvent.getStationId());
        this.setEquipmentId(activeEvent.getEquipmentId());
        this.setEventId(activeEvent.getEventId());
        this.setEventConditionId(activeEvent.getEventConditionId());
        this.setEventLevel(activeEvent.getEventLevel());
        this.setEventSeverity(activeEvent.getEventSeverity());
        this.setEquipmentName(activeEvent.getEquipmentName());
        this.setBaseEquipmentId(activeEvent.getBaseEquipmentId());
        this.setBaseTypeId(activeEvent.getBaseTypeId());
        this.setBaseEquipmentName(activeEvent.getBaseEquipmentName());
        this.setBaseTypeName(activeEvent.getBaseTypeName());
        this.setEventName(activeEvent.getEventName());
        this.setMeanings(activeEvent.getMeanings());
        this.setEventValue(activeEvent.getEventValue());
        this.setEndValue(activeEvent.getEndValue());
        this.setStartTime(activeEvent.getStartTime());
        this.setConfirmTime(activeEvent.getConfirmTime());
        this.setConfirmerName(activeEvent.getConfirmerName());
        this.setEndTime(activeEvent.getEndTime());
        this.setResourceStructureId(activeEvent.getResourceStructureId());
        this.setStandardAlarmNameId(activeEvent.getStandardAlarmNameId());
        this.setStandardAlarmName(activeEvent.getStandardAlarmName());
        this.setStationCategoryId(activeEvent.getStationCategoryId());
        this.setEventCategoryId(activeEvent.getEventCategoryId());
        this.setDescription(activeEvent.getDescription());
        this.setInstructionId(activeEvent.getInstructionId());
        this.setInstructionStatus(activeEvent.getInstructionStatus());
        this.setEquipmentCategory(activeEvent.getEquipmentCategory());
        this.setEquipmentCategoryName(activeEvent.getEquipmentCategoryName());
    }

    /**
     * 流水号
     */
    private String sequenceId;

    /**
     * 局站ID
     */
    private Integer stationId;

    /**
     * 告警等级ID
     */
    private Integer eventLevel;

    /**
     * 告警等级名称
     */
    private String eventSeverity;

    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备名
     */
    private String equipmentName;

    /**
     * 设备位置
     */
    private String equipmentPosition;

    /**
     * 设备基类ID
     */
    private Integer baseEquipmentId;

    /**
     * 设备基类名称
     */
    private String baseEquipmentName;

    /**
     * 设备类型
     */
    private Integer equipmentCategory;
    /**
     * 设备类型名称
     */
    private String equipmentCategoryName;

    /**
     * 事件基类ID
     */
    private Long baseTypeId;

    /**
     * 事件基类名称
     */
    private String baseTypeName;

    /**
     * 事件ID
     */
    private Integer eventId;

    /**
     * 告警名称
     */
    private String eventName;

    /**
     * 告警含义
     */
    private String meanings;

    /**
     * 触发值
     */
    @JsonSerialize(using = DoubleNonNullSerializer.class)
    private Double eventValue;

    /**
     * 结束触发值
     */
    @JsonSerialize(using = DoubleAllowNullSerializer.class)
    private Double endValue;

    /**
     * 条件ID
     */
    private Integer eventConditionId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 确认人
     */
    private String confirmerName;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 层级ID
     */
    private Integer resourceStructureId;

    /**
     * 告警备注数
     */
    private int remarkCount;

    /**
     * 局站类型
     */
    private Integer stationCategoryId;
    /**
     * 告警标准化Id
     */
    private Integer standardAlarmNameId;

    private Integer eventCategoryId;

    /**
     * 告警注释
     */
    private String description;

    /**
     * 告警标准名
     */
    private String standardAlarmName;
    private String standardTypeName;
    private String stdSignalDescription;
    private String stdSignalMeanings;
    private String stdNote;
    private String equipmentLogicCategory;
    private String alarmLogicCategory;
    /**
     * 翻转次数
     */
    private Integer reversalNum;
    /**
     * 开始比较值
     */
    private Double startCompareValue;

    //  告警工单号
    private String InstructionId;

    //  告警工单生成状态
    private Integer InstructionStatus;
    /**
     * 工程状态
     */
    private Integer maintainState;

    public ActiveEventDTO from(ActiveEvent activeEvent) {
        this.setSequenceId(activeEvent.getSequenceId());
        this.setStationId(activeEvent.getStationId());
        this.setEquipmentId(activeEvent.getEquipmentId());
        this.setEventId(activeEvent.getEventId());
        this.setEventConditionId(activeEvent.getEventConditionId());
        this.setEventLevel(activeEvent.getEventLevel());
        this.setEventSeverity(activeEvent.getEventSeverity());
        this.setEquipmentName(activeEvent.getEquipmentName());
        this.setBaseEquipmentId(activeEvent.getBaseEquipmentId());
        this.setBaseTypeId(activeEvent.getBaseTypeId());
        this.setBaseEquipmentName(activeEvent.getBaseEquipmentName());
        this.setBaseTypeName(activeEvent.getBaseTypeName());
        this.setEventName(activeEvent.getEventName());
        this.setMeanings(activeEvent.getMeanings());
        this.setEventValue(activeEvent.getEventValue());
        this.setEndValue(activeEvent.getEndValue());
        this.setStartTime(activeEvent.getStartTime());
        this.setConfirmTime(activeEvent.getConfirmTime());
        this.setEndTime(activeEvent.getEndTime());
        this.setResourceStructureId(activeEvent.getResourceStructureId());
        this.setStandardAlarmNameId(activeEvent.getStandardAlarmNameId());
        this.setStandardAlarmName(activeEvent.getStandardAlarmName());
        this.setStationCategoryId(activeEvent.getStationCategoryId());
        this.setEventCategoryId(activeEvent.getEventCategoryId());
        this.setDescription(activeEvent.getDescription());
        this.setReversalNum(activeEvent.getReversalNum());
        this.setInstructionId(activeEvent.getInstructionId());
        this.setInstructionStatus(activeEvent.getInstructionStatus());
        this.setMaintainState(activeEvent.getMaintainState());
        this.setConfirmerName(activeEvent.getConfirmerName());
        this.setEquipmentCategory(activeEvent.getEquipmentCategory());
        this.setEquipmentCategoryName(activeEvent.getEquipmentCategoryName());
        return this;
    }
    public AlarmChange alarmChange(){
        AlarmChange alarmChange = BeanUtil.copyProperties(this, AlarmChange.class);
        alarmChange.setOperationType(AlarmOperationTypeEnum.START.getValue());
        return alarmChange;
    }
}
