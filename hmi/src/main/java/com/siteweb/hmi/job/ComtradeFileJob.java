package com.siteweb.hmi.job;

import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import com.siteweb.hmi.manager.ComtradeFileManager;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.quartz.job.BaseJob;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * Description: COMTRADE文件管理定时任务
 * Author: <EMAIL>
 * Creation Date: 2025/1/23
 */
@Slf4j
public class ComtradeFileJob implements BaseJob {
    
    // 定义占位符常量，与ComtradeFileManager中的保持一致
    private static final String NULL_VALUE_PLACEHOLDER = "__COMTRADE_NULL_PLACEHOLDER__";

    @Autowired
    private ActiveSignalManager activeSignalManager;

    @Autowired
    private ComtradeFtpService comtradeFtpService;

    @Autowired
    private SystemConfigService systemConfigService;
    
    @Autowired
    private ComtradeFileCleanupService comtradeFileCleanupService;

    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private ComtradeFileManager comtradeFileManager;

    @Autowired
    @Qualifier("longTimeThreadPool")
    private ExecutorService longTimeThreadPool;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 检查初始化是否完成
            if (!comtradeFileManager.isInitializationCompleted()) {
                log.debug("COMTRADE初始化尚未完成，跳过定时任务");
                return;
            }
            
            // 主备双机判断，只有主机才执行
            if (!haStatusService.isMasterHost()) {
                log.debug("HAStatus is BACKUP，COMTRADE文件管理定时任务退出");
                return;
            }
            
            // 检查COMTRADE功能是否启用
            if (!isComtradeEnabled()) {
                log.debug("COMTRADE功能未启用，COMTRADE文件管理定时任务退出");
                return;
            }
            
            // 获取设备列表和信号缓存
            CopyOnWriteArrayList<EquipmentDTO> equipmentList = comtradeFileManager.getEquipmentList();
            Map<String, String> signalCache = comtradeFileManager.getSignalCache();
            
            if (equipmentList.isEmpty()) {
                log.debug("设备列表为空，未找到任何设备。");
                return;
            }
            
            log.debug("开始为 {} 个设备获取活动信号...", equipmentList.size());
            
            for (EquipmentDTO equipment : equipmentList) {
                try {
                    log.debug("正在获取设备ID: {}, 名称: {} 的活动信号", 
                            equipment.getEqId(), equipment.getEqName());
                    List<Long> baseTypeIds = getComtradeSignalBaseTypeIds();
            
                    List<SimpleActiveSignal> activeSignals = 
                            activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipment.getEqId(), baseTypeIds);
                            
                    if (activeSignals == null || activeSignals.isEmpty()) {
                        log.debug("未找到设备ID: {} 的活动信号", equipment.getEqId());
                        continue;
                    }
                    
                    log.debug("设备ID: {} 找到 {} 个活动信号", equipment.getEqId(), activeSignals.size());
                    boolean hasSignalChanged = false;
                    for (SimpleActiveSignal signal : activeSignals) {
                        String cacheKey = equipment.getEqId() + "_" + signal.getSignalId();
                        String currentValue = signal.getCurrentValue();
                        // 如果当前值为null，使用占位符替代
                        String normalizedCurrentValue = currentValue != null ? currentValue : NULL_VALUE_PLACEHOLDER;
                        
                        String cachedValue = signalCache.get(cacheKey);
                        
                        if (!signalCache.containsKey(cacheKey) || !cachedValue.equals(normalizedCurrentValue)) {
                            log.info("设备ID: {}, 信号ID: {} 的值发生变化，旧值: {}, 新值: {}", 
                                    equipment.getEqId(), signal.getSignalId(), 
                                    NULL_VALUE_PLACEHOLDER.equals(cachedValue) ? "null" : cachedValue, 
                                    NULL_VALUE_PLACEHOLDER.equals(normalizedCurrentValue) ? "null" : normalizedCurrentValue);
                            signalCache.put(cacheKey, normalizedCurrentValue);
                            hasSignalChanged = true;
                        }
                    }
                    
                    // 如果有任何信号值发生变化，调用一次FTP服务获取最新的comtrade文件
                    if (hasSignalChanged) {
                        try {
                            String ipAddress = comtradeFtpService.getIpAddressByEquipmentId(equipment.getEqId());
                            if (ipAddress != null && !ipAddress.isEmpty()) {
                                String finalIpAddress = ipAddress;
                                Integer equipmentId = equipment.getEqId();
                                longTimeThreadPool.submit(() -> {
                                    try {
                                        comtradeFtpService.downloadLatestComtradeFiles(finalIpAddress, equipmentId);
                                        log.info("已为设备ID: {} 异步调用FTP服务获取最新comtrade文件", equipmentId);
                                    } catch (Exception e) {
                                        log.error("异步下载COMTRADE文件时出错，设备ID: " + equipmentId, e);
                                    }
                                });
                            } else {
                                log.warn("未找到设备ID: {} 的IP地址，无法下载comtrade文件", equipment.getEqId());
                            }
                        } catch (Exception ftpException) {
                            log.error("调用FTP服务获取最新comtrade文件时出错，设备ID: " + equipment.getEqId(), ftpException);
                        }
                    }
                    
                } catch (Exception e) {
                    log.error("获取设备ID: " + equipment.getEqId() + " 的活动信号时出错", e);
                }
            }
            
            log.debug("所有设备的主动信号获取完成。");
            
            // 可选：定期执行文件清理检查（每10次信号检查执行一次文件清理检查）
            try {
                var cleanupConfig = systemConfigService.findBySystemConfigKey("comtrade.cleanup.enablePeriodic");
                String enablePeriodicCleanup = cleanupConfig != null ? cleanupConfig.getSystemConfigValue() : null;
                if ("true".equalsIgnoreCase(enablePeriodicCleanup)) {
                    // 使用静态计数器确保不会过于频繁地执行清理
                    if (System.currentTimeMillis() % 10 == 0) { // 简单的频率控制
                        log.info("执行定期COMTRADE文件清理检查...");
                        for (EquipmentDTO equipment : equipmentList) {
                            try {
                                comtradeFileCleanupService.cleanupComtradeFiles(equipment.getEqId());
                            } catch (Exception e) {
                                log.error("定期清理设备ID: {} 的文件时出错", equipment.getEqId(), e);
                            }
                        }
                        log.info("定期COMTRADE文件清理检查完成。");
                    }
                }
            } catch (Exception e) {
                log.debug("获取定期清理配置失败或定期清理功能未启用", e);
            }
            
        } catch (Exception e) {
            log.error("ComtradeFileJob执行过程中发生错误", e);
            throw new JobExecutionException(e);
        }
    }

    /**
     * 检查COMTRADE功能是否启用
     * @return true表示功能启用，false表示功能未启用
     */
    private boolean isComtradeEnabled() {
        try {
            var config = systemConfigService.findBySystemConfigKey("comtrade.enable");
            if (config == null || config.getSystemConfigValue() == null) {
                log.debug("未找到COMTRADE启用配置");
                return false;
            }
            
            String comtradeEnable = config.getSystemConfigValue();
            return comtradeEnable != null && !comtradeEnable.trim().isEmpty() && "true".equalsIgnoreCase(comtradeEnable);
        } catch (Exception e) {
            log.debug("获取COMTRADE启用状态失败，默认不启用", e);
            return false;
        }
    }

    /**
     * 从系统配置中获取baseTypeIds
     * @return baseTypeIds列表
     */
    private List<Long> getComtradeSignalBaseTypeIds() {
        try {
            var config = systemConfigService.findBySystemConfigKey("comtrade.signal.basetypeids");
            if (config == null || config.getSystemConfigValue() == null) {
                log.warn("未找到COMTRADE信号基类ID配置或配置值为空，使用默认值");
                return Arrays.asList(1L, 2L, 3L);
            }
            
            String baseTypeIdsConfig = config.getSystemConfigValue();
            if (StringUtils.hasText(baseTypeIdsConfig)) {
                return Arrays.stream(baseTypeIdsConfig.split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("从系统配置获取COMTRADE信号基类ID失败，使用默认值", e);
        }
        // 如果获取失败，返回默认值
        return Arrays.asList(1L, 2L, 3L);
    }
}