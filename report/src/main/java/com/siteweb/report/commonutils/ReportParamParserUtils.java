package com.siteweb.report.commonutils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.*;
import java.util.stream.Collectors;

public class ReportParamParserUtils {
    public static final String EQUIPMENT_ID = "eqId";
    public static final String EQUIPMENT_NAME = "equipmentName";
    public static final String UNIT = "unit";
    public static final String SIGNALS_ID = "signalId";
    public static final String SIGNALS_NAME = "signalName";
    public static final String COMPLEX_INDEX_NAME = "complexIndexName";
    public static final String EVENT_ID = "eventId";
    public static final String VALUE = "value";
    public static final String VALUES = "values";
    public static final String POSITION = "position";
    public static final String ORIGINAL = "original";
    public static final String DATA = "data";
    public static final String TIME = "time";
    public static final String TYPE = "type";
    public static final String TITLE = "title";
    public static final String MAX = "max";
    public static final String MIN = "min";
    public static final String SUM = "sum";
    public static final String AVG = "avg";
    public static final String DISPLAY_VALUE = "displayValue";
    public static final String RESOURCESTRUCTURE_ID = "objectId";
    public static final String BASE_TYPE_ID = "baseTypeId";
    public static final String STATION_ID = "stationId";
    public static final String EQUIPMENT_CATEGORY_ID = "equipmentCategoryId";
    /**
     * 默认分隔符
     */
    private static final String DEFAULT_SEPARATOR = ",";

    /**
     * 解析json字符获取指定字段值，并返回逗号分隔的字符
     *
     * @return
     */
    public static String jsonToString(String json, String field) {
        if (CharSequenceUtil.isBlank(json)) {
            return "";
        }
        JSONArray jsonArray = JSONUtil.parseArray(json);
        if (CollUtil.isEmpty(jsonArray)) {
            return "";
        }
        return CollUtil.join(jsonArray.stream().map(e -> ((JSONObject) e).getStr(field)).filter(e -> !Objects.isNull(e)).toList(), ",");
    }

    /**
     * 解析json字符(map)获取指定字段值，并返回逗号分隔的字符
     *
     * @return
     */
    public static String mapJsonToString(String json, String field) {
        if (CharSequenceUtil.isBlank(json)) {
            return "";
        }
        JSONObject jsonObj = JSONUtil.parseObj(json);
        if (MapUtil.isEmpty(jsonObj)) {
            return "";
        }
        if (CollUtil.isEmpty(jsonObj.get(field, List.class))) {
            return "";
        }
        return CollUtil.join(jsonObj.get(field, List.class), ",");
    }

    /**
     * 解析json字符获取指定字段值，并返回list
     *
     * @return
     */
    public static List<Integer> jsonToList(String json, String field) {
        if (CharSequenceUtil.isBlank(json)) {
            return new ArrayList<>();
        }
        JSONArray jsonArray = JSONUtil.parseArray(json);
        List<Integer> result = new ArrayList<>();
        if (CollUtil.isEmpty(jsonArray)) {
            return new ArrayList<>();
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            if (jsonArray.getJSONObject(i).getInt(field) != null) {
                result.add(jsonArray.getJSONObject(i).getInt(field));
            }
        }
        return result;
    }

    public static Set<Integer> jsonToSet(String json, String field) {
        return new HashSet<>(jsonToList(json, field));
    }

    public static List<Integer> strToList(String str) {
        return strToList(str, DEFAULT_SEPARATOR);
    }

    public static Integer strToInteger(String str){
        if (CharSequenceUtil.isBlank(str)) {
            return null;
        }
        return Integer.valueOf(str);
    }

    public static Set<Integer> strToSet(String str) {
        return new HashSet<>(strToList(str, DEFAULT_SEPARATOR));
    }

    public static List<Integer> strToList(String str, String separator) {
        return Arrays.stream(str.split(separator)).map(s -> Integer.parseInt(s.trim())).toList();
    }

    /**
     * 获取字符串值
     */
    public static String getStrValue(String str) {
        return Optional.ofNullable(str).orElse("");
    }

    private ReportParamParserUtils() {
    }

    /**
     * 事件组合转list【equipmentId.eventId|equipmentId.eventId】，事件逗号分隔，多个事件|分隔
     *
     * @param str 字符串
     * @return Set[equipmentId.eventId|equipmentId.eventId]
     */
    public static Set<String> eventToList(String str) {
        if (CharSequenceUtil.isEmpty(str)) {
            return new HashSet<>();
        }
        return Arrays.stream(str.split("\\|")).filter(e -> CharSequenceUtil.containsAny(e, ".")).collect(Collectors.toSet());
    }

    /**
     * @param value
     * @return key => equipmentId, value => Set[eventId]
     */
    public static Map<Integer, Set<Integer>> parseEventStrToMap(String value) {
        if (CharSequenceUtil.isEmpty(value)) {
            return new HashMap<>();
        }
        JSONArray eventArr = JSONUtil.parseArray(value);
        if (eventArr.isEmpty()) {
            return new HashMap<>();
        }

        HashMap<Integer, Set<Integer>> eventGroupHashMap = new HashMap<>();
        for (Object o : eventArr) {
            JSONObject event = (JSONObject) o;
            Integer equipmentId = event.getInt(EQUIPMENT_ID);
            Integer eventId = event.getInt(EVENT_ID);
            Set<Integer> eventSet = eventGroupHashMap.get(equipmentId);
            if (CollUtil.isEmpty(eventSet)) {
                eventSet = new HashSet<>();
            }
            eventSet.add(eventId);
            eventGroupHashMap.put(equipmentId, eventSet);
        }
        return eventGroupHashMap;
    }

    /**
     * 构建 (a.EquipmentId=%s and a.EventId=%s) 语句
     *
     * @param eventMap
     * @return
     */
    public static String buildEventSql(Map<Integer, Set<Integer>> eventMap) {
        List<String> entryList = new ArrayList<>();
        for (Map.Entry<Integer, Set<Integer>> entry : eventMap.entrySet()) {
            for (Integer val : entry.getValue()) {
                entryList.add(String.format("(a.EquipmentId=%s and a.EventId=%s)", entry.getKey(), val));
            }
        }
        return CollUtil.join(entryList, " or ");
    }

    public static double getTwoDecimal(Double doubleValue) {
        if (doubleValue != null) {
            String strResult = String.format("%.2f", doubleValue);
            return Double.parseDouble(strResult);
        } else {
            return 0;
        }
    }
}
