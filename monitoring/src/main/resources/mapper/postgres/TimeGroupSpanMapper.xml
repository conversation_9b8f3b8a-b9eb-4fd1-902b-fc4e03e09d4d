<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.TimeGroupSpanMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_timegroupspan (
        TimeSpanId,
        TimeGroupId,
        StartTime,
        EndTime,
        "week",
        TimeSpanChar,
        LastUpdateDate
        ) VALUES
        <foreach collection="timeGroupSpans" item="item" separator=",">
            (
            #{item.timeSpanId},
            #{item.timeGroupId},
            #{item.startTime},
            #{item.endTime},
            #{item.week},
            #{item.timeSpanChar},
            #{item.lastUpdateDate}
            )
        </foreach>
    </insert>
    <update id="updateLastUpdateDateAndTimeSpanCharByTimeGroupIdAndWeek">
        UPDATE tbl_timegroupspan
        SET TimeSpanChar   = #{timeMaskChar},
            LastUpdateDate = #{lastUpdateDate}
        WHERE TimeGroupId = #{timeGroupId}
          AND "week" = #{week}
    </update>
    <delete id="deleteByEventMaksIdentities">
        DELETE FROM TBL_TimeGroupSpan
        WHERE EXISTS (
        SELECT 1 FROM tbl_eventmask b
        WHERE TBL_TimeGroupSpan.TimeGroupId = b.TimeGroupId
        AND (b.StationId, b.EquipmentId, b.EventId) IN
        <foreach collection="eventIdentityDTOList" item="item" separator="," open="(" close=")">
            (#{item.stationId}, #{item.equipmentId}, #{item.eventId})
        </foreach>
        )
    </delete>
    <select id="findByTimeGroupId" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT TimeSpanId, TimeGroupId, StartTime, EndTime, "week", TimeSpanChar, LastUpdateDate
        FROM TBL_TimeGroupSpan WHERE TimeGroupId = #{timeGroupId}
    </select>
    <select id="findEquipmentMaskByWeek" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT a.TimeSpanId, a.TimeGroupId, a.StartTime, a.EndTime, a."week", a.TimeSpanChar, a.LastUpdateDate
        FROM TBL_TimeGroupSpan a
        WHERE "week" = #{week}
          AND LENGTH(TimeSpanChar) = 12
          AND EXISTS(SELECT 1 FROM tbl_Equipmentmask b WHERE a.timegroupId = b.timegroupId)
    </select>
    <select id="findEventMaskByWeekAndEquipmentId" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT a.TimeSpanId, a.TimeGroupId, a.StartTime, a.EndTime, a."week", a.TimeSpanChar, a.LastUpdateDate
        FROM TBL_TimeGroupSpan a
        WHERE "week" = #{week}
          AND LENGTH(TimeSpanChar) = 12
          AND EXISTS(SELECT 1 FROM tbl_eventmask b WHERE a.timegroupId = b.timegroupId AND b.EquipmentId = #{equipmentId})
    </select>
    <select id="findByWeekAndTimeGroupIds" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT a.TimeSpanId, a.TimeGroupId, a.StartTime, a.EndTime, a."week", a.TimeSpanChar, a.LastUpdateDate
        FROM TBL_TimeGroupSpan a
        WHERE "week" = #{week}
          AND LENGTH(TimeSpanChar) = 12
        AND a.timeGroupId IN
        <foreach collection="timeGroupIds" item="timeGroupId" open="(" close=")" separator=",">
            #{timeGroupId}
        </foreach>
    </select>
    <select id="findByWeekAndTimeGroupId" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT TimeSpanId, TimeGroupId, StartTime, EndTime, "week", TimeSpanChar, LastUpdateDate
        FROM TBL_TimeGroupSpan
        WHERE TimeGroupId = #{timeGroupId}
          AND "week" = #{week}
    </select>

    <select id="findByStationId" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT A.* FROM TBL_TimeGroupSpan A
            INNER JOIN TBL_StationMask B ON A.TimeGroupId = B.TimeGroupId AND B.StationId = #{stationId}
    </select>

    <select id="findByStationIdAndEquipmentId" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT A.TimeSpanId FROM TBL_TimeGroupSpan A INNER JOIN TBL_EquipmentMask B ON A.TimeGroupId = B.TimeGroupId
            AND B.EquipmentId = #{equipmentId} AND B.StationId = #{stationId}
    </select>
    <select id="findEventMaskTimeGroup" resultType="com.siteweb.monitoring.entity.TimeGroupSpan">
        SELECT a.TimeSpanId, a.TimeGroupId, a.StartTime, a.EndTime, a."week", a.TimeSpanChar, a.LastUpdateDate
        FROM tbl_timegroupspan a
                 INNER JOIN tbl_eventmask b ON a.TimeGroupId = b.TimeGroupId
        WHERE b.StationId = #{stationId}
          AND b.EquipmentId = #{equipmentId}
          AND b.EventId = #{eventId}
    </select>
    <select id="findTimeSpanIdsByEquipmentIds" resultType="java.lang.Integer">
        SELECT A.TimeSpanId FROM TBL_TimeGroupSpan A INNER JOIN TBL_EquipmentMask B ON A.TimeGroupId = B.TimeGroupId
        WHERE B.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
</mapper>