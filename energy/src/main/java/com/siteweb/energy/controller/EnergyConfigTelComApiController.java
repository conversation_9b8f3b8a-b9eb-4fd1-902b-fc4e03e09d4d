package com.siteweb.energy.controller;

import cn.hutool.core.date.DateTime;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.energy.service.EnergyConfigTelComService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api/energy/energyapi/telcom")
@Api(value = "EnergyConfigTelComApiController",tags = "能耗数据标准接口电信")
public class EnergyConfigTelComApiController {

    @Autowired
    EnergyConfigTelComService energyConfigTelComService;

    @ApiOperation("总用电量及同环比")
    @GetMapping(value = "/totalEnergyUseOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUseOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getTotalElectricity(timeType, startTime, endTime, userId,"electTotal"));
    }
    @ApiOperation("碳排放及同环比")
    @GetMapping(value = "/totalCarbonUseOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> totalCarbonUseOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getTotalElectricity(timeType, startTime, endTime, userId,"carbonTotal"));
    }

    @ApiOperation("各用能类型总量")
    @GetMapping(value = "/electCategoryOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> electCategoryOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getTotalElectricity(timeType, startTime, endTime, userId,"electCategory"));
    }

    @ApiOperation("各用能类型碳排放总量")
    @GetMapping(value = "/carbonCategoryOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> carbonCategoryOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getTotalElectricity(timeType, startTime, endTime, userId,"carbonCategory"));
    }
    @ApiOperation("碳抵消同环比")
    @GetMapping(value = "/greenCarbonUseOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> greenCarbonUseOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getTotalElectricity(timeType, startTime, endTime, userId,"greenCarbon"));
    }


    @ApiOperation("某基站用碳及用能总量")
    @GetMapping(value = "/oneRoomEnergyAndCarbon",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> oneRoomEnergyAndCarbon(
            @ApiParam(name = "resourceStructureId", value = "对象ID", required = true)Integer resourceStructureId
    ){
        Date startTime = setStartTimeByTimeType("d");
        Date endTime = setEndTimeByTimeType(startTime,"d");
        return ResponseHelper.successful(energyConfigTelComService.getRoomEnergyAndCarbon("d", startTime, endTime, resourceStructureId,null,"oneRoomTotal","carbon&energy"));
    }
    @ApiOperation("各用能类型碳排放年趋势")
    @GetMapping(value = "/carbonCategoryTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> carbonCategoryTrend(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType("y");
        Date endTime = setEndTimeByTimeType(startTime,"y");
        return ResponseHelper.successful(energyConfigTelComService.getEnergyAndCarbonTrend("y", startTime, endTime, userId,"carbonCategory"));
    }
    @ApiOperation("各用能类型总量年趋势")
    @GetMapping(value = "/energyCategoryTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> energyCategoryTrend(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType("y");
        Date endTime = setEndTimeByTimeType(startTime,"y");
        return ResponseHelper.successful(energyConfigTelComService.getEnergyAndCarbonTrend("y", startTime, endTime, userId,"energyCategory"));
    }
    @ApiOperation("下级各用能类型总量排名(结构堆叠)")
    @GetMapping(value = "/nextLevelEnergyRank",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> nextLevelEnergyRank(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getNextLevelRank(timeType, startTime, endTime, userId,"energy"));
    }
    @ApiOperation("下级各用能类型碳排放排名(结构堆叠)")
    @GetMapping(value = "/nextLevelCarbonRank",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> nextLevelCarbonRank(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getNextLevelRank(timeType, startTime, endTime, userId,"carbon"));
    }
    @ApiOperation("用电分项占比")
    @GetMapping(value = "/electCategoryProportion",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> electCategoryProportion(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.electCategoryProportion(timeType, startTime, endTime, userId,2));
    }

    @ApiOperation("某基站PUE")
    @GetMapping(value = "/PUEOfResourceStructure",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> PUEofResourceStructure(
            @ApiParam(name = "resourceStructureId", value = "对象ID", required = true)Integer resourceStructureId,
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.GetPUEOFResourceStructure(resourceStructureId, startTime, endTime,timeType));
    }

    @ApiOperation("权限基站汇总PUE")
    @GetMapping(value = "/PUEOfArea",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> PUEofArea(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.findMaxMInAvgUE(startTime, endTime,timeType,userId));
    }
    @ApiOperation("权限基站汇总PUE趨勢")
    @GetMapping(value = "/PUEOfAreaTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> PUEofAreaTrend(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.findMaxMInAvgUETrend(startTime, endTime,timeType,userId));
    }

    // 如果接口没有传开始时间则按照时间类型，自动赋值本日本月本年
    public Date setStartTimeByTimeType(String timeType){
        Date startTime = new Date();
        switch (timeType){
            case "d" :
                startTime = DateUtil.getTodayStartTime().getTime();
                break;
            case "m":
                startTime = DateUtil.getFirstDayOfMonth(DateTime.now());
                break;
            case "y":
                startTime = DateUtil.dateAddMonth(DateUtil.getNextYearFirstDay(DateTime.now()),-12);
                break;
        }
//        startTime.setYear(122); //1900+122==2022
        return startTime;
    }
    public Date setEndTimeByTimeType(Date startTime,String timeType){
        Date endTime = new Date();
        switch (timeType){
            case "d" :
                endTime = DateUtil.getLastSecondsOfToday(startTime);
                break;
            case "m":
                endTime = DateUtil.getLastDayOfMonth(DateTime.now());
                break;
            case "y":
                endTime = DateUtil.dateAddSeconds(DateUtil.getNextYearFirstDay(DateTime.now()),-1);
                break;
        }
//        endTime.setYear(122);
        return endTime;
    }


    //----------------------------------------电信场景动态获取能源类型计算用能及碳排放数据----------------------------------------

    @ApiOperation("总用电量及同环比(动态获取)")
    @GetMapping(value = "/dynamic/totalEnergyUseOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicTotalEnergyUseOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.totalEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId,"energy"));
    }
    @ApiOperation("碳排放及同环比(动态获取)")
    @GetMapping(value = "/dynamic/totalCarbonUseOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicTotalCarbonUseOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.totalEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId,"carbon"));
    }
    @ApiOperation("各用能类型总量(动态获取)")
    @GetMapping(value = "/dynamic/electCategoryOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicEnergyCategoryOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.everyEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId,"energy"));
    }

    @ApiOperation("各用能类型碳排放总量(动态获取)")
    @GetMapping(value = "/dynamic/carbonCategoryOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicCarbonCategoryOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.everyEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId,"carbon"));
    }

    @ApiOperation("碳抵消同环比(动态获取)")
    @GetMapping(value = "/dynamic/greenCarbonUseOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicGreenCarbonUseOfRole(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.greenCarbonOfRole(timeType, startTime, endTime, userId));
    }

    @ApiOperation("某基站用碳及用能总量(动态获取)")
    @GetMapping(value = "/dynamic/oneRoomEnergyAndCarbon",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicOneRoomEnergyAndCarbon(
            @ApiParam(name = "resourceStructureId", value = "对象ID", required = true)Integer resourceStructureId
    ){
        Date startTime = setStartTimeByTimeType("d");
        Date endTime = setEndTimeByTimeType(startTime,"d");
        return ResponseHelper.successful(energyConfigTelComService.dynamicGetRoomEnergyAndCarbon("d", startTime, endTime, resourceStructureId,null));
    }

    @ApiOperation("各用能类型碳排放年趋势(动态获取)")
    @GetMapping(value = "/dynamic/carbonCategoryTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicCarbonCategoryTrend(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType("y");
        Date endTime = setEndTimeByTimeType(startTime,"y");
        return ResponseHelper.successful(energyConfigTelComService.dynamicGetEnergyAndCarbonTrend("y", startTime, endTime, userId,"carbon"));
    }

    @ApiOperation("各用能类型总量年趋势(动态获取)")
    @GetMapping(value = "/dynamic/energyCategoryTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicEnergyCategoryTrend(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType("y");
        Date endTime = setEndTimeByTimeType(startTime,"y");
        return ResponseHelper.successful(energyConfigTelComService.dynamicGetEnergyAndCarbonTrend("y", startTime, endTime, userId,"energy"));
    }

    @ApiOperation("下级各用能类型总量排名(结构堆叠)(动态获取)")
    @GetMapping(value = "/dynamic/nextLevelEnergyRank",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicNextLevelEnergyRank(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.dynamicGetNextLevelRank(timeType, startTime, endTime, userId,"energy"));
    }
    @ApiOperation("下级各用能类型碳排放排名(结构堆叠)(动态获取)")
    @GetMapping(value = "/dynamic/nextLevelCarbonRank",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> dynamicNextLevelCarbonRank(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.dynamicGetNextLevelRank(timeType, startTime, endTime, userId,"carbon"));
    }

    @ApiOperation("碳排放碳抵消占比(动态获取)")
    @GetMapping(value = "/dynamic/greenRatio",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> greenOrOtherRatio(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.greenOrOtherRatio(timeType, startTime, endTime, userId));
    }

    @Operation(summary = "所有基站某能耗定义类型的topN")
    @GetMapping(value = "/stationEnergy",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationEnergy(
            @Parameter(name = "timeType", description = "时间类型", required = true) String timeType,
            @Parameter(name = "complexIndexDefinitionId", description = "指标定义Id", required = true) Integer complexIndexDefinitionId,
            @Parameter(name = "topN", description = "", required = true) Integer topN
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyConfigTelComService.getStationEnergy(timeType, startTime, endTime, complexIndexDefinitionId, topN));
    }
}
