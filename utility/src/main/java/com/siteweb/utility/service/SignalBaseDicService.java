package com.siteweb.utility.service;

import com.siteweb.utility.dto.SignalBaseDicCategoryDTO;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.SignalBaseDic;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SignalBaseDicService {

    List<SignalBaseDic> findSignalBaseDics();

    SignalBaseDic findById(Long signalBaseDicId);

    List<SignalBaseDic> findByIds(List<Long> signalBaseDicIds);

    Map<Long, String> getStandardIdNameMap(Integer equipmentTemplateId,List<Long> baseTypeIds);

    List<SignalBaseDicDTO> findByBaseEquipmentId(Integer baseEquipmentId);

    List<SignalBaseDicDTO> findBranchSignalByBaseEquipmentId(Integer baseEquipmentId);

    List<SignalBaseDicDTO> findSignalBaseDicByBaseEquipmentIds(String baseEquipmentIds);

    List<SignalBaseDicDTO> findSignalBaseDicByEquipmentId(Integer equipmentId);

    List<SignalBaseDicCategoryDTO> findSignalBaseCategory(List<Long> baseTypeIds);

    List<SignalBaseDicDTO> findByBaseEquipmentIdAndEquipmentIds(Integer baseEquipmentId, Set<Integer> equipmentIds);
}

