package com.siteweb.admin.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.dto.MenuStructureDTO;
import com.siteweb.admin.entity.MenuStructure;
import com.siteweb.admin.language.LanguageUtil;
import com.siteweb.admin.service.MenuStructureService;
import com.siteweb.admin.vo.MenuStructureVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * MenuStructure info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-30 17:00:36
 */
@RestController
@RequestMapping("/api")
@Api(value = "MenuStructureController", tags = {"菜单目录操作接口"})
public class MenuStructureController {

    @Autowired
    MenuStructureService menuStructureService;

    /**
     * GET /menustructures : get the MenuStructures.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of MenuStructures in body
     */
    @GetMapping("/menustructures")
    @ApiOperation(value = "获取所有MenuStructure实体")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "返回所有MenuStructure实体", content = {@Content})})
    public ResponseEntity<ResponseResult> getMenuStructures() {
        return ResponseHelper.successful(menuStructureService.findMenuStructures());
    }

    @GetMapping(value = "/menustructures", params = "menuProfileId",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getByMenuProfileId(HttpServletRequest request, Integer menuProfileId) {
        List<MenuStructureDTO> parentMenuStructureByProfileId = menuStructureService.getParentMenuStructureByProfileId(menuProfileId);
        LanguageUtil.menuLanguageBaseSwitch(parentMenuStructureByProfileId,request.getHeader(LanguageUtil.LANGUAGE_HEADER));
        return ResponseHelper.successful(parentMenuStructureByProfileId);
    }

    /**
     * GET /menustructures/:id get the MenuStructure by id.
     *
     * @param menuStructureId the MenuStructureId
     * @return the ResponseEntity with status 200 (OK) and with body the MenuStructure, or with status
     * 404 (Not Found)
     */
    @ApiOperation(value = "根据MenuStructureId查询MenuStructure实体")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "返回根据MenuStructureId查询到的MenuStructure实体", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "根据MenuStructureId查询不到MenuStructure实体", content = @Content)
    })
    @GetMapping("/menustructures/{menuStructureId}")
    public ResponseEntity<ResponseResult> getMenuStructureById(
            @PathVariable("menuStructureId") Integer menuStructureId) {
        MenuStructure menuStructure = menuStructureService.findById(menuStructureId);
        return Optional.ofNullable(menuStructure)
                .map(result -> ResponseHelper.successful(menuStructure))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /menustructures : create a new MenuStructure
     *
     * @param menuStructureVOList the MenuStructure to create
     * @return the ResponseEntity with status 201 (Created) and with body the new MenuStructure, or
     * with status 400 (Bad Request) if the MenuStructure has already an ID
     */
    @ApiOperation(value = "新增MenuStructure实体")
    @ApiOperationSupport(ignoreParameters = {"menuStructureId"})
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "新的MenuStructure已被创建", content = {@Content}),
            @ApiResponse(responseCode = "400", description = "新增MenuStructure报错", content = @Content)
    })
    @PostMapping(value = "/menustructures")
    public ResponseEntity<ResponseResult> createMenuStructure(@Valid @RequestBody List<MenuStructureVO> menuStructureVOList) {
        Integer result = menuStructureService.createMenuStructure(menuStructureVOList);
        if (result > 0) {
            return ResponseHelper.successful(result);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createMenuStructure error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /menustructures : Updates an existing MenuStructure.
     *
     * @param menuStructureVO the MenuStructure to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated MenuStructure, or
     * with status 404 (Not Found) if the menuStructureId is not exists,
     */
    @ApiOperation(value = "修改MenuStructure实体")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "MenuStructure已被修改", content = {@Content}),
            @ApiResponse(responseCode = "400", description = "要修改的MenuStructure其menuStructureId为null或输入错误", content = @Content)
    })
    @PutMapping(value = "/menustructures")
    public ResponseEntity<ResponseResult> updateMenuStructure(
            @Valid @RequestBody MenuStructureVO menuStructureVO) {
        if (menuStructureVO.getMenuStructureId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "MenuStructureId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = menuStructureService.updateMenuStructure(menuStructureVO);
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "MenuStructureId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation(value = "修改MenuStructure的排序")
    @PutMapping(value = "/menustructures/sort")
    public ResponseEntity<ResponseResult> updateMenuStructureSortIndex(@RequestBody List<MenuStructureVO> menuStructureVOList){
         return ResponseHelper.successful(menuStructureService.updateMenuStructureSort(menuStructureVOList));
    }

    /**
     * DELETE /menustructures/:id : delete the MenuStructure by id.
     *
     * @param menuStructureVO the id of the MenuStructure to delete
     * @return the ResponseEntity with status 200 (OK) or status 404 (when not found)
     */
    @ApiOperation(value = "删除MenuStructure实体")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "MenuStructure实体被删除", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "根据MenuStructureId查询不到MenuStructure实体", content = @Content)
    })
    @DeleteMapping(value = "/menustructures", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteMenuStructure(@RequestBody MenuStructureVO menuStructureVO) {
        menuStructureService.deleteMenuStructure(menuStructureVO);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
