<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.SignalBaseDicMapper">
    <resultMap id="IdValueDTOMap" type="com.siteweb.utility.dto.IdValueDTO">
        <result column="value" property="value" javaType="java.lang.Long" jdbcType="BIGINT"/>
        <result column="label" property="label" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="findBranchSignalByBaseEquipmentId" resultType="com.siteweb.utility.entity.SignalBaseDic">
        SELECT BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, BaseLogicCategoryId, StoreInterval, Abs<PERSON><PERSON>ue<PERSON><PERSON><PERSON>old, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StoreInterval2, <PERSON><PERSON><PERSON><PERSON>ueThreshold2, Percent<PERSON>hreshold2, ExtendField1, ExtendField2, ExtendField3, UnitId, BaseStatusId, BaseHysteresis, BaseFreqPeriod, BaseFreqCount, BaseShowPrecision, BaseStatPeriod, CGElement, BaseNameExt, Description, IsSystem
        FROM tbl_signalbasedic a
        where a.BaseEquipmentId = #{baseEquipmentId}
        and (a.BaseTypeId % 1000) = 1
        and a.BaseNameExt !='NULL'
        and a.BaseNameExt is not null  ;
    </select>
    <select id="findSignalBaseDicByEquipmentId" resultType="com.siteweb.utility.entity.SignalBaseDic">
        select * from tbl_signalbasedic a
        where a.baseTypeId in(
        select c.baseTypeId from tbl_Equipment b inner join tbl_Signal c on b.EquipmentTemplateId = c.EquipmentTemplateId
        where b.EquipmentId = #{equipmentId})
    </select>
    <select id="findSignalBaseCategory" resultType="com.siteweb.utility.dto.SignalBaseDicCategoryDTO">
        select
        distinct
        a.BaseTypeId as baseTypeId,
        b.SignalCategory as signalCategory
        from tbl_signalbasedic a
        inner join tbl_signal b on a.BaseTypeId = b.BaseTypeId
        where a.baseTypeId in
        <foreach collection="baseTypeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findByBaseEquipmentIdAndEquipmentIds" resultType="com.siteweb.utility.dto.SignalBaseDicDTO">
        SELECT a.BaseTypeId,a.BaseTypeName
        FROM tbl_signalbasedic a
        WHERE a.baseTypeId IN (SELECT c.baseTypeId
        FROM tbl_Equipment b
        INNER JOIN tbl_Signal c ON b.EquipmentTemplateId = c.EquipmentTemplateId
        WHERE b.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>)
        AND a.BaseEquipmentId = #{baseEquipmentId}
    </select>
    <select id="findStandardIdNameMap" resultMap="IdValueDTOMap">
        SELECT
        distinct es.BaseTypeId as value,
        replace(sds.SignalStandardName,'XX',convert(es.BaseTypeId - floor(es.BaseTypeId / 1000)*1000,char(10))) as label
        FROM TBL_Signal es
        LEFT JOIN TBL_SignalBaseDic sb ON sb.BaseTypeId = es.BaseTypeId
        LEFT JOIN TBL_SignalBaseMap sbm ON sbm.BaseTypeId = (floor(es.BaseTypeId / 1000) * 1000 + 1)
        AND sbm.StandardType = #{standardVer}
        <if test="standardVer == 3">
            AND ((es.SignalCategory = 1 AND sbm.BaseCondId = 9999) OR
            (es.SignalCategory = 2 AND sbm.BaseCondId != 9999))
        </if>
        LEFT JOIN TBL_StandardDicSig sds ON sds.StandardDicId = sbm.StandardDicId
        AND sds.StandardType = #{standardVer}
        WHERE es.BaseTypeId IN
        <foreach collection="baseTypeIds" item="baseTypeId" open="(" separator="," close=")">
            #{baseTypeId}
        </foreach>
        AND es.EquipmentTemplateId = #{equipmentTemplateId}
        AND es.Enable = 1
        AND es.Visible = 1
    </select>
</mapper>