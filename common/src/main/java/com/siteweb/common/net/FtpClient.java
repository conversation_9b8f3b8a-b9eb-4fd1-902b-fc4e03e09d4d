package com.siteweb.common.net;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.SocketException;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

@Slf4j
@Data
public class FtpClient {
    private String server = "localhost";
    private int port = 21;
    private String username = "anonymous";
    private String password = "<EMAIL>";
    private String remotePath = ".";
    private String localFilePath = ".";
    private boolean binaryMode = true;
    private FTPClient apacheFtpClient;

    public FtpClient(String server, int port) {
        this.apacheFtpClient = new FTPClient();
        this.server = server;
        this.port = port;
    }

    public FtpClient(String server, int port, String username, String password) {
        this.apacheFtpClient = new FTPClient();
        this.server = server;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    public FtpClient(String server, int port, String username, String password, FTPClientConfig config) {
        this.apacheFtpClient = new FTPClient();
        this.apacheFtpClient.configure(config);
        this.server = server;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    /**
     * 连接并登录到FTP服务器
     */
    public void login() throws IOException {
        try {
            // 设置超时配置
            apacheFtpClient.setConnectTimeout(10000); // 连接超时10秒
            apacheFtpClient.setDefaultTimeout(30000); // Socket超时30秒
            apacheFtpClient.setDataTimeout(Duration.ofSeconds(30)); // 数据传输超时30秒
            apacheFtpClient.setBufferSize(1024 * 1024);

            apacheFtpClient.setControlEncoding("GBK");
            apacheFtpClient.connect(server, port);
            int replyCode = apacheFtpClient.getReplyCode();
            if (!apacheFtpClient.login(username, password)) {
                throw new IOException("登录失败，回复码: " + replyCode);
            }
            log.info("成功连接到FTP服务器: {}:{}", server, port);

            if (binaryMode) {
                apacheFtpClient.setFileType(FTP.BINARY_FILE_TYPE);
            } else {
                apacheFtpClient.setFileType(FTP.ASCII_FILE_TYPE);
            }
            apacheFtpClient.enterLocalPassiveMode();
        } catch (SocketException e) {
            throw new IOException("无法连接到FTP服务器: " + e.getMessage(), e);
        }
    }

    /**
     * 断开与FTP服务器的连接
     */
    public void logout() {
        if (apacheFtpClient.isConnected()) {
            try {
                apacheFtpClient.logout();
                apacheFtpClient.disconnect();
                log.info("已断开与FTP服务器的连接。");
            } catch (IOException e) {
                log.error("断开FTP服务器时出错: {}", e.getMessage());
            }
        }
    }

    /**
     * 切换FTP服务器的工作目录
     * @param dirName 要切换到的目录名
     * @throws IOException 如果发生I/O错误
     */
    public void changeDirectory(String dirName) throws IOException {
        boolean success = apacheFtpClient.changeWorkingDirectory(dirName);
        if (!success) {
            throw new IOException("切换目录失败: " + dirName);
        }
        this.remotePath = apacheFtpClient.printWorkingDirectory();
        log.info("工作目录已切换到: {}", this.remotePath);
    }

    /**
     * 下载远程目录到本地
     * @param remoteDirPath 远程目录路径
     * @param localParentDir 本地保存目录
     * @param excludePatterns 要排除的文件或目录的匹配模式列表
     * @throws IOException 如果发生I/O错误
     */
    public void downloadDirectory(String remoteDirPath, String localParentDir, List<String> excludePatterns) throws IOException {
        FTPFile[] subFiles = apacheFtpClient.listFiles(remoteDirPath);
        if (Objects.isNull(subFiles)) {
            return;
        }
        for (FTPFile file : subFiles) {
            String fileName = file.getName();

            if (shouldExclude(fileName, excludePatterns)) {
                log.info("排除文件/目录: {}", fileName);
                continue;
            }

            String remoteFilePath = remoteDirPath + "/" + fileName;
            File localFile = new File(localParentDir + "/" + fileName);

            if (file.isDirectory()) {
                if (!localFile.exists()) {
                    localFile.mkdirs();
                    log.info("目录创建: {}", fileName);
                }
                downloadDirectory(remoteFilePath, localFile.getAbsolutePath(), excludePatterns);
            } else {
                try (OutputStream outputStream = new FileOutputStream(localFile)) {
                    apacheFtpClient.retrieveFile(remoteFilePath, outputStream);
                    log.info("文件下载成功: {}", remoteFilePath);
                }
            }
        }
    }

    private boolean shouldExclude(String fileName, List<String> excludePatterns) {
        if (excludePatterns == null || excludePatterns.isEmpty()) {
            return false;
        }

        for (String pattern : excludePatterns) {
            if (pattern.contains("*")) {
                String regex = pattern.replace(".", "\\.").replace("*", ".*");
                if (fileName.matches(regex)) {
                    return true;
                }
            } else if (fileName.equals(pattern)) {
                return true;
            }
        }
        return false;
    }
}