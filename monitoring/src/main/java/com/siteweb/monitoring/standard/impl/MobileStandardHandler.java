package com.siteweb.monitoring.standard.impl;

import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.mapper.StandardDicEventMapper;
import com.siteweb.monitoring.mapper.StandardDicSigMapper;
import com.siteweb.monitoring.standard.AbstractStandardHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MobileStandardHandler extends AbstractStandardHandler {
    @Autowired
    StandardDicSigMapper sigMapper;
    @Autowired
    StandardDicEventMapper eventMapper;

    @Override
    public List<IdValueDTO<Long, String>> handleSignalStandard() {
        return sigMapper.findMobileStandardSig();
    }

    @Override
    public List<IdValueDTO<Long, String>> handleEventStandard() {
        return eventMapper.getAllStandardDicEvent().stream()
                          .filter(event -> event.getStandardType() == 1)
                          .sorted(compareByEquipmentAndEvent())
                          .map(event -> new IdValueDTO<>(Long.valueOf(event.getStandardDicId()),
                                          String.join("_",
                                          event.getEquipmentLogicClass(),
                                          event.getEventStandardName(),
                                          event.getNetManageId())))
                          .toList();
    }

    @Override
    public List<Signal> getSignalBySignalBaseEntryIds(List<Integer> entryIds) {
        return sigMapper.getMobileSignalIdBySignalBaseEntryIds(entryIds);
    }
}
