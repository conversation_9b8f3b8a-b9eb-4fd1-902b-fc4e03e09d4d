<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.RolePermissionMapMapper">

    <insert id="batchCreateRolePermissionMap">
        INSERT INTO RolePermissionMap(RoleId, PermissionCategoryId, PermissionId) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId},#{item.permissionCategoryId},#{item.permissionId})
        </foreach>
    </insert>
    <insert id="batchCreateUserRoleRight">
        INSERT INTO tbl_userroleright(roleid, operationid, operationtype) VALUES
        <foreach collection="userRoleRightVOList" item="item" separator=",">
            (#{item.roleId},#{item.operationId},#{item.operationType})
        </foreach>
    </insert>

    <delete id="delUserRoleRightByRoleIdAndOperationType">
        DELETE FROM TBL_UserRoleRight WHERE RoleId = #{roleId} AND OperationType = #{operationType}
    </delete>
    <delete id="deleteByCascadeId">
        DELETE
        rpm
        FROM rolepermissionmap rpm
        INNER JOIN permission p ON
        rpm.permissionId = p.permissionId
        AND p.Category = 6
        WHERE rpm.RoleId = #{roleId} AND p.Description IN
        <foreach collection="cascadeIdSet" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="getUserRoleRightCount" resultType="int">
        SELECT count(*) from TBL_UserRoleRight where RoleId= #{roleId} and OperationId = #{operationId} and OperationType = #{operationType}
    </select>

    <select id="getAreaRolePermissionMaps" resultType="com.siteweb.admin.entity.RolePermissionMap">
        SELECT RoleId, OperationId AS PermissionId, 9 AS permissionCategoryId FROM tbl_userroleright WHERE RoleId= #{roleId} and OperationType  = 2
    </select>

    <select id="getSpecialtyPermissionMaps" resultType="com.siteweb.admin.entity.RolePermissionMap">
        SELECT RoleId, OperationId AS PermissionId, 8 AS permissionCategoryId FROM tbl_userroleright WHERE RoleId= #{roleId} and OperationType  = 3
    </select>

    <select id="getRolePermissionMapsByUserIdAndCategoryId" resultType="com.siteweb.admin.entity.RolePermissionMap">
        SELECT t1.RoleId, t1.PermissionId, t1.PermissionCategoryId FROM rolepermissionmap t1
            INNER JOIN tbl_userrolemap t2 ON t1.RoleId = t2.RoleId
            INNER JOIN tbl_account t3 on t2.UserId = t3.UserId
        WHERE t3.UserId = #{userId,jdbcType=INTEGER}
            AND t1.PermissionCategoryId = #{permissionCategoryId,jdbcType=INTEGER}
    </select>

    <select id="findRolePermissionMapsByUserId" resultType="integer">
        SELECT t1.PermissionId FROM rolepermissionmap t1
            INNER JOIN tbl_userrolemap t2 ON t1.RoleId = t2.RoleId
            INNER JOIN tbl_account t3 on t2.UserId = t3.UserId
        WHERE t3.UserId = #{userId,jdbcType=INTEGER}
    </select>

    <select id="findRolePermissionsByUserId" resultType="int">
        SELECT t1.PermissionId FROM rolepermissionmap t1
            INNER JOIN tbl_userrolemap t2 ON t1.RoleId = t2.RoleId
            INNER JOIN tbl_account t3 on t2.UserId = t3.UserId
        WHERE t3.UserId = #{userId,jdbcType=INTEGER}
          AND t1.PermissionCategoryId = #{permissionCategoryId,jdbcType=INTEGER}
    </select>


    <select id="findRolePermissionMapByRoleId" resultType="com.siteweb.admin.entity.RolePermissionMap">
        SELECT rpm.RolePermissionMapId, rpm.RoleId, rpm.PermissionCategoryId, rpm.PermissionId FROM `rolepermissionmap` rpm
            INNER JOIN `scenepermissioncategorymap` spm ON rpm.PermissionCategoryId = spm.PermissionCategoryId
            INNER JOIN scene s ON s.SceneId = spm.SceneId
            WHERE s.checked = 1 AND RoleId = #{roleId}
    </select>
    <select id="findUserOperationPermissionGroups" resultType="java.lang.Integer">
        SELECT b.PermissionId
        FROM tbl_userrolemap a
                 INNER JOIN rolepermissionmap b on a.RoleId = b.RoleId and b.PermissionCategoryId = 2
        WHERE a.UserId = #{userId}
    </select>
</mapper>