package com.siteweb.energy.service;

import com.siteweb.energy.dto.*;
import java.util.List;

public interface EnergyReportService {
    //日趋势
    List<EnergyReportDayTrendResultDTO> EnergyConsumeDayTrendReport(EnergyReportParameterDTO para);
    //月趋势
    List<EnergyReportMonthTrendResultDTO> EnergyConsumeMonthTrendReport(EnergyReportParameterDTO para);
    //年趋势
    List<EnergyReportYearTrendResultDTO> EnergyConsumeYearTrendReport(EnergyReportParameterDTO para);
    List<EnergyReportDayTrendResultDTO> EnergyCarbonDayTrendReport(EnergyReportParameterDTO para);
    //月趋势
    List<EnergyReportMonthTrendResultDTO> EnergyCarbonMonthTrendReport(EnergyReportParameterDTO para);
    //年趋势
    List<EnergyReportYearTrendResultDTO> EnergyCarbonYearTrendReport(EnergyReportParameterDTO para);

    List<EnergyReportYearTrendResultDTO> EnergyCostYearTrendReport(EnergyReportParameterDTO para);

    List<EnergyReportMonthTrendResultDTO> EnergyCostMonthTrendReport(EnergyReportParameterDTO para);

    List<EnergyReportDayTrendResultDTO> EnergyCostDayTrendReport(EnergyReportParameterDTO para);

    //年损耗趋势
    List<EnergyReportYearTrendResultDTO> EnergyLossYearTrendReport(EnergyReportParameterDTO para);
    //月损耗趋势
    List<EnergyReportMonthTrendResultDTO> EnergyLossMonthTrendReport(EnergyReportParameterDTO para);
    //日损耗趋势
    List<EnergyReportDayTrendResultDTO> EnergyLossDayTrendReport(EnergyReportParameterDTO para);

    List<EnergyReportResultDTO> EnergyConsumeReport(EnergyReportParameterDTO para);
}
