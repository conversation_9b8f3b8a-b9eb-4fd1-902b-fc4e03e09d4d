package com.siteweb.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.asset.dto.AssetLedgerDTO;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.entity.ExtValueConfiguration;
import com.siteweb.asset.mapper.AssetDeviceMapper;
import com.siteweb.asset.mapper.ExtValueConfigurationMapper;
import com.siteweb.asset.service.AssetCategoryService;
import com.siteweb.asset.service.AssetDeviceService;
import com.siteweb.asset.service.EquipmentLedgerService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.mapper.EquipmentTemplateMapper;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 */
@Service
public class EquipmentLedgerServiceImpl implements EquipmentLedgerService {
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private EquipmentMapper equipmentMapper;
    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EquipmentStateManager equipmentStateManager;
    @Autowired
    private ActiveEventManager activeEventManager;
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    private EquipmentManager equipmentManager;

    @Autowired
    private AssetDeviceService assetDeviceService;
    @Autowired
    private AssetDeviceMapper assetDeviceMapper;
    @Autowired
    private ExtValueConfigurationMapper extValueConfigurationMapper;
    @Autowired
    private AssetCategoryService assetCategoryService;
    @Autowired
    private SignalService signalService;
    @Autowired
    private DataItemService dataItemService;

    private static String TABLE_NAME = "tbl_equipment";
    @Override
    public List<IdValueDTO<String, Integer>> modelStatistics() {
        List<AssetDevice> assetDeviceList = assetDeviceService.findAll().stream().toList();
        assetDeviceList = filterViableEquipmentAsset(assetDeviceList);
        Map<String, Long> styleCountMap = assetDeviceList.stream()
                .collect(Collectors.groupingBy(
                        device -> {
                            String model = device.getModel();
                            return (model == null || model.trim().isEmpty()) ? "未分组" : model.trim();
                        },
                        Collectors.counting()
                ));

        return styleCountMap.entrySet().stream()
                .map(entry -> new IdValueDTO<>(entry.getKey(), entry.getValue().intValue()))
                .collect(Collectors.toList());

    }

    @Override
    public List<Tree<Integer>> styleTree() {
        List<AssetDevice> assetDeviceList = assetDeviceService.findAll();
        assetDeviceList = filterViableEquipmentAsset(assetDeviceList);
        List<AssetDevice> nonEmptyModelDevices = assetDeviceList.stream()
                .filter(a -> StringUtils.isNotBlank(a.getModel()))
                .toList();

        List<String> styleList = new ArrayList<>(nonEmptyModelDevices.stream()
                .map(a -> a.getModel().trim())
                .distinct()
                .toList());

        boolean hasEmptyModel = assetDeviceList.stream()
                .anyMatch(a -> StringUtils.isBlank(a.getModel()));

        if (hasEmptyModel) {
            styleList.add("未分组");
        }

        AtomicInteger idCounter = new AtomicInteger(1);

        return TreeUtil.build(styleList, -1, (templateStyle, node) -> {
            node.setId("未分组".equals(templateStyle) ? 0 : idCounter.getAndIncrement());
            node.setName(templateStyle);
            node.setParentId(-1);
        });

    }



    @Override
    public EquipmentStatusStatisticsDTO statusStatistics() {
        //此处设备总数用设备来做
        List<Integer> equipmentByUserId = equipmentService.findEquipmentDTOsByUserId(TokenUserUtil.getLoginUserId()).stream().map(EquipmentDTO::getEqId).toList();
        Set<Integer> equipmentIdsByUserId = new HashSet<>(equipmentByUserId);
        long equipmentSum = equipmentByUserId.size();

        Set<Integer> allEquipmentAlarmState = equipmentStateManager.getAllEquipmentAlarmState();
        HashMap<Integer, EquipmentState> equipmentStateByIds = equipmentStateManager.getEquipmentStateByIds(equipmentIdsByUserId.stream().toList());
        List<EquipmentState> onlineEquipmentStateList = equipmentStateByIds.values().stream().filter(a -> a.getOnlineState() == OnlineState.ONLINE).toList();
        long onlineCount = onlineEquipmentStateList.size();
        long offlineCount = equipmentStateByIds.values().stream().filter(a -> a.getOnlineState() != OnlineState.ONLINE).count();
        long onlineAlarmCount = onlineEquipmentStateList.stream().filter(a -> allEquipmentAlarmState.contains(a.getEquipmentId())).count();

        List<EquipmentStateSignalPointDTO> equipmentStateWithSignalPoint = equipmentMapper.getEquipmentStateWithSignalPoint(equipmentIdsByUserId);

        long sumPoint = equipmentStateWithSignalPoint.stream()
                .filter(a-> ObjectUtil.isNotNull(a.getSignalPointCount())).
                mapToInt(EquipmentStateSignalPointDTO::getSignalPointCount).sum();
        return new EquipmentStatusStatisticsDTO(equipmentSum,onlineAlarmCount,onlineCount,offlineCount,sumPoint);
    }

    List<EquipmentStateSignalPointDTO> noPageStatusListByResourceStructure(Integer resourceStructureId, Integer alarmState, Integer conState, Integer equipmentCategory,String keywords){
        //用户可见设备
        Set<Integer> equipmentIdsByUserId = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (ObjectUtil.isNotNull(resourceStructureId)){
            Set<Integer> resourceStructureIds = resourceStructureManager.getAll().stream().filter(a -> a.getLevelOfPath().contains(String.valueOf(resourceStructureId))).map(ResourceStructure::getResourceStructureId).collect(Collectors.toSet());
            List<Integer> resourceEquipments = equipmentService.findEquipmentsByResourceStructureIds(resourceStructureIds).stream().map(Equipment::getEquipmentId).toList();
            equipmentIdsByUserId.retainAll(resourceEquipments);
        }
        if (ObjectUtil.isNotNull(alarmState)){
            Set<Integer> alarmEquipments = equipmentStateManager.getAllEquipmentAlarmState();
            if (  alarmState == AlarmState.ALARM.value()){
                equipmentIdsByUserId.retainAll(alarmEquipments);
            }
            else if (  alarmState == AlarmState.NORMAL.value()){
                equipmentIdsByUserId.removeAll(alarmEquipments);
            }
        }
        if (ObjectUtil.isNotNull(conState)){
            HashMap<Integer, EquipmentState> equipmentStateByIds = equipmentStateManager.getEquipmentStateByIds(equipmentIdsByUserId.stream().toList());
            Set<Integer> onlineEqu = equipmentStateByIds.values().stream().filter(a->a.getOnlineState() == OnlineState.ONLINE).map(EquipmentState::getEquipmentId).collect(Collectors.toSet());
            if (conState == OnlineState.ONLINE.value()){
                equipmentIdsByUserId.retainAll(onlineEqu);
            }
            else if (conState == OnlineState.OFFLINE.value() ||conState == OnlineState.UNREGISTER.value()){
                equipmentIdsByUserId.removeAll(onlineEqu);
            }
        }
        List<EquipmentStateSignalPointDTO> equipmentStateWithSignalPoint = new ArrayList<>() ;

        if (CollUtil.isNotEmpty(equipmentIdsByUserId)){

            equipmentStateWithSignalPoint = equipmentMapper.getEquipmentStateWithSignalPoint(equipmentIdsByUserId);
            if (equipmentCategory != -1 && equipmentCategory != -2){
                //-1查所有，-2查未分类
                equipmentStateWithSignalPoint = equipmentStateWithSignalPoint.stream().filter(a -> Objects.equals(a.getEquipmentCategory(), equipmentCategory)).toList();
            }
        }

        if (CollUtil.isNotEmpty(equipmentIdsByUserId) && StringUtils.isNotEmpty(keywords)){
            equipmentStateWithSignalPoint = equipmentStateWithSignalPoint.stream()
                    .filter(a->StringUtils.contains(a.getEquipmentName(), keywords))
                    .toList();
        }
        Map<Integer, String> categoryMap = dataItemService.findByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue()).stream().collect(Collectors.toMap(DataItem::getItemId, DataItem::getItemValue));

        equipmentStateWithSignalPoint.forEach(point ->{
            point.setEquipmentCategoryName(categoryMap.getOrDefault(point.getEquipmentCategory(), null));
        });

        if (equipmentCategory == -2){
            equipmentStateWithSignalPoint = equipmentStateWithSignalPoint.stream().filter(a -> Objects.isNull(a.getEquipmentCategoryName())).toList();
        }
        return equipmentStateWithSignalPoint;
    }

    @Override
    public PageImpl<EquipmentStateSignalPointDTO> getStatusListByResourceStructure(Pageable pageable, Integer resourceStructureId, Integer alarmState, Integer conState, Integer equipmentCategory,String keywords) {

        List<EquipmentStateSignalPointDTO> equipmentStateSignalPointDTOS = this.noPageStatusListByResourceStructure(resourceStructureId, alarmState, conState, equipmentCategory, keywords);
        List<EquipmentStateSignalPointDTO> records = equipmentStateSignalPointDTOS.stream()
                .skip((long) (pageable.getPageNumber()) * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .peek(a -> {
                    a.setEquipmentPosition(resourceStructureManager.getLevelOfPathName(a.getLevelOfPath()));
                })
                .toList();
        List<Integer> recordIds = records.stream().map(EquipmentStateSignalPointDTO::getEquipmentId).toList();

        if (CollUtil.isNotEmpty(recordIds)){
            Map<Integer, ActiveEvent> maxEventLevelByEquipmentIds = activeEventManager.queryAllActiveEvents().stream()
                    .filter(active -> recordIds.contains(active.getEquipmentId())
                            && ObjectUtil.isNull(active.getEndTime())
                            && ObjectUtil.isNotNull(active.getEventLevel()))
                    .collect(Collectors.toMap(
                            ActiveEvent::getEquipmentId,
                            Function.identity(),
                            (event1, event2) -> event1.getEventLevel() <= event2.getEventLevel() ? event1 : event2
                    ));

            HashMap<Integer, EquipmentState> equipmentStateByIds = equipmentStateManager.getEquipmentStateByIds(recordIds);
            records.forEach(a -> {
                if (maxEventLevelByEquipmentIds.containsKey(a.getEquipmentId())){
                    a.setAlarmState(AlarmState.ALARM.value());
                    ActiveEvent activeEvent = maxEventLevelByEquipmentIds.get(a.getEquipmentId());
                    a.setMaxEventLevel(activeEvent.getEventLevel());
                    a.setMaxEventLevelName(activeEvent.getEventSeverity());
                }
                else{
                    a.setAlarmState(AlarmState.NORMAL.value());
                }
                if (equipmentStateByIds.containsKey(a.getEquipmentId()) && equipmentStateByIds.get(a.getEquipmentId()).getOnlineState() == OnlineState.ONLINE){
                    a.setOnlineState(OnlineState.ONLINE.value());
                }
                else {
                    a.setOnlineState(OnlineState.OFFLINE.value());
                }
            });
        }

        return new PageImpl<>(records, pageable, equipmentStateSignalPointDTOS.size());
    }

    @Override
    public ExcelWriter exportEquStatus(Integer resourceStructureId, Integer alarmState, Integer conState, Integer equipmentCategory, String keywords) {
        List<EquipmentStateSignalPointDTO> equipmentStateSignalPointDTOS = this.noPageStatusListByResourceStructure(resourceStructureId, alarmState, conState, equipmentCategory, keywords);
        equipmentStateSignalPointDTOS = equipmentStateSignalPointDTOS.stream()
                .peek(a -> {
                    a.setEquipmentPosition(resourceStructureManager.getLevelOfPathName(a.getLevelOfPath()));
                })
                .toList();
        List<Integer> recordIds = equipmentStateSignalPointDTOS.stream().map(EquipmentStateSignalPointDTO::getEquipmentId).toList();

        if (CollUtil.isNotEmpty(recordIds)){
            Map<Integer, Integer> maxEventLevelByEquipmentIds = activeEventManager.queryAllActiveEvents().stream()
                    .filter(active -> recordIds.contains(active.getEquipmentId()) && ObjectUtil.isNull(active.getEndTime()) && ObjectUtil.isNotNull(active.getEventLevel()))
                    .collect(Collectors.toMap(ActiveEvent::getEquipmentId, ActiveEvent::getEventLevel, Math::min));
            HashMap<Integer, EquipmentState> equipmentStateByIds = equipmentStateManager.getEquipmentStateByIds(recordIds);
            equipmentStateSignalPointDTOS.forEach(a -> {
                if (maxEventLevelByEquipmentIds.containsKey(a.getEquipmentId())){
                    a.setAlarmStateName(localeMessageSourceUtil.getMessage("eventNotification.activeEvent.eventName"));
                }
                else{
                    a.setAlarmStateName(localeMessageSourceUtil.getMessage("equipment.manage.status.alarmstatus.normal"));
                }
                if (equipmentStateByIds.containsKey(a.getEquipmentId()) && equipmentStateByIds.get(a.getEquipmentId()).getOnlineState() == OnlineState.ONLINE){
                    a.setOnlineStateName(localeMessageSourceUtil.getMessage("api.stationStatus.1"));
                }
                else {
                    a.setOnlineStateName(localeMessageSourceUtil.getMessage("api.stationStatus.0"));
                }
            });
        }


        ExcelWriter writer = ExcelUtil.getWriter(true);

        writer.addHeaderAlias("equipmentName", localeMessageSourceUtil.getMessage("patrol.alarm.equipmentName"));
        writer.addHeaderAlias("equipmentCategoryName", localeMessageSourceUtil.getMessage("patrol.alarm.equipmentCategoryName"));
        writer.addHeaderAlias("equipmentPosition", localeMessageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        writer.addHeaderAlias("signalPointCount", localeMessageSourceUtil.getMessage("equipment.manage.status.signalpoint"));
        writer.addHeaderAlias("onlineStateName", localeMessageSourceUtil.getMessage("equipment.manage.status.connectstatus"));
        writer.addHeaderAlias("alarmStateName",localeMessageSourceUtil.getMessage("equipment.manage.status.alarmstatus"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(equipmentStateSignalPointDTOS,true);
        writer.setColumnWidth(0, 50);
        writer.setColumnWidth(1, 30);
        writer.setColumnWidth(2, 100);
        writer.setColumnWidth(3, 10);
        writer.setColumnWidth(4, 10);
        writer.setColumnWidth(5, 10);
        return writer;
    }

    @Override
    public List<EquipmentSignalPointStatisticsDTO> getSignalpointStatistics() {
        List<EquipmentSignalPointStatisticsDTO> signalPoint = equipmentTemplateMapper.getSignalPoint();
        List<EquipmentSignalPointStatisticsDTO> result = new ArrayList<>();
        Map<Integer, List<Equipment>> equipmentTemplateGroup = equipmentManager.getAllEquipments().stream()
                .filter(a -> ObjectUtil.isNotNull(a.getEquipmentTemplateId()))
                .collect(Collectors.groupingBy(Equipment::getEquipmentTemplateId));
        signalPoint.forEach(a->{
            List<Integer> equipments = equipmentTemplateGroup.getOrDefault(a.getEquipmentTemplateId(),Collections.emptyList()).stream().map(Equipment::getEquipmentId).toList();
            if (CollUtil.isNotEmpty(equipments)){
                a.setEquipmentCount(equipments.size());
                int onlineCount = equipmentStateManager.getEquipmentStateByIds(equipments).values().stream().filter(equipmentState -> equipmentState.getOnlineState() == OnlineState.ONLINE).toList().size();
                if (ObjectUtil.equals(onlineCount,0)){
                    a.setEquipmentOnLineRate("0%");
                }
                else{
                    a.setEquipmentOnLineRate(String.format("%.2f%%", onlineCount*1.0d/equipments.size()*100));
                }
                a.setSignalPointCountSum(a.getEquipmentCount()*a.getSignalPointCount());
                result.add(a);
            }
        });
        return result;
    }

    List<AssetDevice> filterViableEquipmentAsset(List<AssetDevice> originAssetList){
        if (CollUtil.isEmpty(originAssetList)){
            return Collections.emptyList();
        }
        List<Integer> equipmentByUserId = equipmentService.findEquipmentDTOsByUserId(TokenUserUtil.getLoginUserId()).stream().map(EquipmentDTO::getEqId).toList();
        return originAssetList.stream().filter(a-> !StringUtils.equals(a.getTableName(),TABLE_NAME) || equipmentByUserId.contains(Integer.parseInt(a.getAssetCode()))).toList();
    }
    @Override
    public List<AssetLedgerDTO> getAllAssetLedger(String resourceIds, String keywords, String styleName){

        List<AssetDevice> allAssetDevices = assetDeviceMapper.findAllByModel(styleName);
        allAssetDevices = filterViableEquipmentAsset(allAssetDevices);
        if (CollUtil.isEmpty(allAssetDevices)){
            return Collections.emptyList();
        }
        List<ExtValueConfiguration> assetDeviceValuesList = extValueConfigurationMapper.selectMapByTableNameAndTablePkIds("assetDevice", allAssetDevices.stream().map(AssetDevice::getAssetDeviceId).toList());
        assetDeviceValuesList = assetDeviceValuesList.stream().filter(e -> e.getExtId() != null && e.getExtValue() != null).toList();
        Map<Integer, List<ExtValueConfiguration>> groupedByPkId = assetDeviceValuesList.stream()
                .collect(Collectors.groupingBy(ExtValueConfiguration::getExtTablePkId));

        List<AssetLedgerDTO> result = new ArrayList<>();
        List<AssetLedgerDTO> finalResult = result;
        allAssetDevices.forEach(a->{
            finalResult.add(new AssetLedgerDTO(a,groupedByPkId.getOrDefault(a.getAssetDeviceId(),Collections.emptyList())));
        });
        if(StringUtils.isNotEmpty(resourceIds)){
            List<String> finalResourceList = Arrays.stream(resourceIds.split(",")).toList();
            result = result.stream()
                    .filter(a -> finalResourceList.stream().anyMatch(b->StringUtils.contains(a.getLevelOfPath(),b))).toList();
        }
        if (StringUtils.isNotEmpty(keywords)) {
            result = result.stream()
                    .filter(a -> {
                        // 先做字符串匹配，减少对 Set 查询的调用次数
                        return (StringUtils.contains(a.getEquipmentName(), keywords) ||
                                StringUtils.contains(a.getVendor(), keywords) ||
                                StringUtils.contains(a.getEquipmentCategoryName(), keywords) ||
                                StringUtils.contains(a.getEquipmentStyle(), keywords)||
                                StringUtils.contains(a.getEquipmentPosition(), keywords)||
                                StringUtils.contains(a.getAssetDepartmentName(), keywords)||
                                StringUtils.contains(a.getAssetResponsiblePersonName(), keywords)||
                                StringUtils.contains(a.getManufacturerContactPerson(), keywords)
                        );
                    })
                    .collect(Collectors.toList());
        }

        return result;
    }

    @Override
    public PageImpl<AssetLedgerDTO> getAllAssetLedgerPaged(Pageable pageable, String resourceIds, String keywords, String styleName){
        List<AssetLedgerDTO> allAssetLedger = getAllAssetLedger(resourceIds, keywords, styleName);
        List<AssetLedgerDTO> records = allAssetLedger.stream()
                .skip((long) (pageable.getPageNumber()) * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .peek(a -> a.setEquipmentPosition(resourceStructureManager.getLevelOfPathName(a.getLevelOfPath())))
                .toList();
        return new PageImpl<>(records, pageable, allAssetLedger.size());
    }

    @Override
    public Integer getAssetDeviceSum() {
        List<AssetDevice> allAssetDevices = assetDeviceMapper.findAllByModel(null);
        allAssetDevices = filterViableEquipmentAsset(allAssetDevices);
        return allAssetDevices.size();
    }

    @Override
    public List<TemplateSignalPointDTO> getSingleFullPoint(Integer templateId){
        List<TemplateSignalPointDTO> result = getFullPoint(Collections.singletonList(templateId)).getOrDefault(templateId,null);
        AtomicInteger index = new AtomicInteger(1);
        result.forEach(a -> a.setIndex(index.getAndIncrement()));
        return result;
    }
    @Override
    public Map<Integer, List<TemplateSignalPointDTO>> getFullPoint(Collection<Integer> templateIds){
        if (CollUtil.isEmpty(templateIds)){
            return Collections.emptyMap();
        }
        return equipmentTemplateMapper.getSignalPointDetail(templateIds).stream().collect(Collectors.groupingBy(TemplateSignalPointDTO::getEquipmentTemplateId));
    }
    @Override
    public ExcelWriter exportSignalPoint(List<Integer> templateIds) {
        // 获取数据
        Map<Integer, List<TemplateSignalPointDTO>> fullPoint = getFullPoint(templateIds);

        // 创建 ExcelWriter
        ExcelWriter writer = ExcelUtil.getWriter(true);

        // 遍历 Map，每个 key 创建一个 sheet
        int sheetIndex = 0;
        for (Map.Entry<Integer, List<TemplateSignalPointDTO>> entry : fullPoint.entrySet()) {
            List<TemplateSignalPointDTO> data = entry.getValue();
            if (CollUtil.isEmpty(data)){
                continue;
            }

            //sheetName 写入excel的时候会截取前31个，为避免重复使用id开头
            String sheetName =  data.get(0).getEquipmentTemplateName()+"_" +data.get(0).getEquipmentCategoryName() ;
            String formatSheetName = data.get(0).getEquipmentTemplateId() +"_"+ sheetName.replaceAll("[/:*?\"<>|]", "_");
            // 切换到新的 sheet
            if (sheetIndex == 0) {
                writer.renameSheet(formatSheetName);
            } else {
                writer.setSheet(formatSheetName);
            }
            //由于会出现截断，首行再放一遍
            writer.merge(0, 0, 0, 3, sheetName, true);
            writer.passCurrentRow();
            sheetIndex++;
            AtomicInteger index = new AtomicInteger(1);
            data.forEach(a -> a.setIndex(index.getAndIncrement()));

            // 设置表头
            writer.addHeaderAlias("index", localeMessageSourceUtil.getMessage("equipment.manage.status.point.index"));
            writer.addHeaderAlias("signalName", localeMessageSourceUtil.getMessage("equipment.manage.status.point.name"));
            writer.addHeaderAlias("channelNo", localeMessageSourceUtil.getMessage("equipment.manage.status.point.channelno"));
            writer.addHeaderAlias("signalId", localeMessageSourceUtil.getMessage("equipment.manage.status.point.signalid"));
//            writer.addHeaderAlias("backTraceNo", localeMessageSourceUtil.getMessage("equipment.manage.status.point.loopno"));


            // 只写入别名字段
            writer.setOnlyAlias(true);
            writer.write(data, true);
            // 设置列宽
            writer.setColumnWidth(0, 10);
            writer.setColumnWidth(1, 30);
            writer.setColumnWidth(2, 20);
            writer.setColumnWidth(3, 20);
//            writer.setColumnWidth(4, 20);
        }

        return writer;
    }

}
