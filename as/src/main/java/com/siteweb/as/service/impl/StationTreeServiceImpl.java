package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.as.dto.StationStructure;
import com.siteweb.as.mapper.ASMapper;
import com.siteweb.as.service.StationTreeService;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.service.StationMaskService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class StationTreeServiceImpl implements StationTreeService {
    /**
     * 局站标志
     */
    private static final int STATION_FLAG = 20002;
    /**
     * 分组标志
     */
    private static final int GROUP_FLAG = 20001;
    @Autowired
    ASMapper asMapper;
    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    StationMaskService stationMaskService;
    @Autowired
    StationService stationService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    StationManager stationManager;
    @Override
    public StationStructure getTreeByCategory(Integer userId, Integer treeCategoryId) {
        StationStructure stationHead = new StationStructure();
        List<StationStructure> stationStructureList = asMapper.getStationStructureByCategoryId(treeCategoryId);
        Map<Integer, Set<Integer>> stationGroupByRegionPermission = stationService.findStationGroupByRegionPermission(userId);
        Map<Integer, StationStructure> parentGroupMap = getPermissionGroupMap(stationGroupByRegionPermission, stationStructureList);
        List<StationStructure> stationMapList = getPermissionStationStructures(treeCategoryId, stationGroupByRegionPermission);
        stationStructureList.addAll(stationMapList);

        //20001分组  20002局站
        for (StationStructure item : stationStructureList) {
            //父节点id类型只会有分组
            item.setParentNodeId(item.getParentNodeId2() + "." + GROUP_FLAG);
            if (Objects.equals(0, item.getParentNodeId2())) {
                stationHead = item;
                continue;
            }
            Optional.ofNullable(parentGroupMap.get(item.getParentNodeId2()))
                    .ifPresent(parent -> {
                        // 如果 parent.getChildren() 为空，初始化为新的 ArrayList
                        parent.setChildren(Optional.ofNullable(parent.getChildren()).orElseGet(ArrayList::new));
                        // 添加当前项目到父节点的子节点集合中
                        parent.getChildren().add(item);
                    });
        }
        //获取局站最大告警等级 从activeEventManager
        Map<Integer, Integer> alarmMap = activeEventManager.getStationAlarmState();
        Set<Integer> maskEffectiveSet = stationMaskService.isMaskEffective(stationMapList.stream().map(StationStructure::getId).toList());
        Map<Integer, Integer> eventSeverityMap = dataItemService.findByEntryId(DataEntryEnum.EVENT_LEVEL.getValue())
                .stream()
                .collect(Collectors.toMap(dataItem -> Integer.valueOf(dataItem.getExtendField4()), DataItem::getItemId));
        for (StationStructure item : stationMapList) {
            alarmMap.computeIfPresent(item.getId(), (key, value) -> {
                item.setMaxEventLevel(value);
                Integer maxEventSeverity = eventSeverityMap.get(item.getMaxEventLevel());
                item.setMaxEventSeverity(Objects.isNull(maxEventSeverity) ? -1 : maxEventSeverity);
                return value;
            });
            if (maskEffectiveSet.contains(item.getId())) {
                item.setProjectState(4);
            }
        }
        stationHead.updateMaxEvent();
        //如果该分组没有局站，则直接删除该分组
        pruneInvalidBottomGroups(stationHead);
        return stationHead;
    }


    /**
     * 获取有权限的局站信息
     * @param treeCategoryId 分组类型
     * @param stationGroupByRegionPermission 权限信息
     * @return {@link List }<{@link StationStructure }> 有权限的局站信息列表
     */
    private List<StationStructure> getPermissionStationStructures(Integer treeCategoryId, Map<Integer, Set<Integer>> stationGroupByRegionPermission) {
        Set<Integer> stationIdSet = stationGroupByRegionPermission.getOrDefault(SourceType.STATION.value(), Collections.emptySet());
        if (CollUtil.isEmpty(stationIdSet)) {
            return Collections.emptyList();
        }
        // 1. 先根据权限和 treeCategoryId 过滤出有权限的局站
        List<StationStructure> filteredStations = asMapper.getStationMapByCategoryId(treeCategoryId, stationIdSet);
        // 2. 批量获取有权限局站的在线状态
        Map<Integer, Integer> onlineStates = stationManager.findOnlineStates(List.copyOf(stationIdSet));

        // 3. 设置在线状态并返回
        return filteredStations.stream()
                               .map(station -> {
                                   station.setConnectState(onlineStates.getOrDefault(station.getId(), OnlineState.UNREGISTER.value()));
                                   return station;
                               })
                               .toList();
    }

    /**
     * 获取拥有区域权限的分组
     * @param stationGroupByRegionPermission 拥有的区域权限的map
     * @param stationStructureList 局站分组临时存储列表
     * @return {@link Map }<{@link Integer }, {@link StationStructure }> 有权限的分组MAP，key是分组id，values是分组详情
     */
    private Map<Integer, StationStructure> getPermissionGroupMap(Map<Integer, Set<Integer>> stationGroupByRegionPermission, List<StationStructure> stationStructureList) {
        Set<Integer> citySet = stationGroupByRegionPermission.getOrDefault(SourceType.SSCENTER.value(),Collections.emptySet());
        Set<Integer> districtSet = stationGroupByRegionPermission.getOrDefault(SourceType.DISTRICT.value(), Collections.emptySet());
        Map<Integer, StationStructure> parentGroupMap = new HashMap<>();
        for (StationStructure stationStructure : stationStructureList) {
            //不是根分组并且分组没有权限
            if (!Objects.equals(stationStructure.getParentNodeId2(), 0) && !citySet.contains(stationStructure.getId()) && !districtSet.contains(stationStructure.getId())) {
                continue;
            }
            parentGroupMap.put(stationStructure.getId(), stationStructure);
        }
        return parentGroupMap;
    }
    /**
     * 递归修剪树：
     * 1. 强制确保20002局站无子节点。
     * 2. 移除最底层20001分组，如果其子节点不全是20002（包括空情况）。
     * 3. 递归移除因此变无效的上层分组（无任何20002子树）。
     *
     * @param node 当前节点
     * @return boolean 是否这个子树包含任何有效20002局站（用于上层决策）
     */
    private boolean pruneInvalidBottomGroups(StationStructure node) {
        if (node == null) {
            return false; // 无节点，无效
        }

        // 特殊处理：如果当前是20002局站，强制清空children（符合业务规则：叶子节点无嵌套）
        if (Objects.equals(node.getNodeType(), STATION_FLAG)) {
            node.setChildren(null); // 或 new ArrayList<>() 如果需要空列表
            return true; // 20002本身有效
        }

        // 如果没有子节点，且不是20002，则无效（例如，空20001）
        if (CollUtil.isEmpty(node.getChildren())) {
            return false; // 无20002，无效
        }

        // 收集保留的子节点（后序递归）
        List<StationStructure> retainedChildren = new ArrayList<>();
        boolean hasValidStation = false;

        for (StationStructure child : node.getChildren()) {
            // 递归修剪子树，并检查子树是否包含有效20002
            boolean childHasValid = pruneInvalidBottomGroups(child);
            if (childHasValid) {
                retainedChildren.add(child);
                hasValidStation = true;
            }
        }

        // 更新当前节点的children为保留的
        node.setChildren(retainedChildren.isEmpty() ? null : retainedChildren);

        // 如果当前是20001，检查是否是最底层
        if (Objects.equals(node.getNodeType(), GROUP_FLAG)) {
            // 是最底层：子节点中是否无20001
            boolean isBottomLevel = CollUtil.isEmpty(node.getChildren()) ||
                    node.getChildren().stream().noneMatch(c -> Objects.equals(c.getNodeType(), GROUP_FLAG));

            if (isBottomLevel) {
                // 检查子节点是否全是20002（包括空情况视为不全）
                boolean allChildrenAreStations = !CollUtil.isEmpty(node.getChildren()) &&  // 非空
                        node.getChildren().stream().allMatch(c -> Objects.equals(c.getNodeType(), STATION_FLAG));

                // 如果不是，移除这个分组（返回false，让上层不保留它）
                if (!allChildrenAreStations) {
                    return false;
                }
            }
        }

        // 返回是否这个子树包含有效20002（直接或间接）
        return hasValidStation;
    }
}
