package com.siteweb.asset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.RegionService;
import com.siteweb.asset.dto.AssetDeviceDTO;
import com.siteweb.asset.dto.BatchImportDTO;
import com.siteweb.asset.entity.AssetCategory;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.entity.ExtFieldConfiguration;
import com.siteweb.asset.entity.ExtValueConfiguration;
import com.siteweb.asset.mapper.AssetCategoryMapper;
import com.siteweb.asset.mapper.AssetDeviceMapper;
import com.siteweb.asset.mapper.ExtFieldConfigurationMapper;
import com.siteweb.asset.mapper.ExtValueConfigurationMapper;
import com.siteweb.asset.service.AssetCategoryService;
import com.siteweb.asset.service.AssetDeviceService;
import com.siteweb.asset.service.ExtFieldConfigurationService;
import com.siteweb.asset.service.ExtValueConfigurationService;
import com.siteweb.asset.vo.AssetDeviceFilterVo;
import com.siteweb.asset.vo.ByteDanceAssetExtFieldEnum;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.OperationRecord;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.OperationRecordService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2023/3/10 10:23
 */
@Service
@Slf4j
public class AssetDeviceServiceImpl implements AssetDeviceService {

    public static final String TABLE_NAME = "assetDevice";
    public static final String FROM_EQUIPMENT = "tbl_equipment";
    private static final Integer RAW_DATA = 1;//原始数据
    @Autowired
    ExtFieldConfigurationService extFieldConfigurationService;
    @Autowired
    ExtValueConfigurationService extValueConfigurationService;
    @Autowired
    AssetCategoryService assetCategoryService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    AssetDeviceMapper assetDeviceMapper;
    @Autowired
    AssetCategoryMapper assetCategoryMapper;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    ExtValueConfigurationMapper extValueConfigurationMapper;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    OperationRecordService operationRecordService;
    @Autowired
    RegionService regionService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    ExtFieldConfigurationMapper extFieldConfigurationMapper;
    @Autowired
    SystemConfigService systemConfigService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AssetDevice> createBatch(BatchImportDTO batchImportDTO) {
        List<AssetDevice> result = new ArrayList<>();
        if (batchImportDTO.getImportType().equals(RAW_DATA)) {
            assetDeviceMapper.delete(Wrappers.lambdaQuery());
        }
        List<AssetDevice> assetDeviceList = new ArrayList<>();
        Map<Integer, ExtFieldConfiguration> extTypeMap = extFieldConfigurationService.findByExtTable(AssetDeviceServiceImpl.TABLE_NAME).stream().collect(Collectors.toMap(ExtFieldConfiguration::getExtId, e -> e));
        Map<String, AssetCategory> categoryMap = assetCategoryService.findAll().stream().collect(Collectors.toMap(AssetCategory::getAssetCategoryName, e -> e, (e1, e2) -> e1));
        List<String> assetDeviceCodeList = batchImportDTO.getAssetDeviceDTOList().stream().map(AssetDeviceDTO::getAssetCode).toList();
        Map<Integer, Map<Integer, ExtValueConfiguration>> assetExtValueMap = null;
        if (CollUtil.isNotEmpty(assetDeviceCodeList)) {
            List<AssetDevice> assetDevices = assetDeviceMapper.selectList(Wrappers.lambdaQuery(AssetDevice.class).in(AssetDevice::getAssetCode, assetDeviceCodeList));
            List<Integer> assetDeviceIds = assetDevices.stream().map(AssetDevice::getAssetDeviceId).toList();
            if (CollUtil.isNotEmpty(assetDeviceIds)) {
                List<ExtValueConfiguration> extValueConfigurationList = extValueConfigurationMapper.selectList(Wrappers.lambdaQuery(ExtValueConfiguration.class).eq(ExtValueConfiguration::getExtTable, TABLE_NAME).in(ExtValueConfiguration::getExtTablePkId, assetDeviceIds));
                assetExtValueMap = extValueConfigurationList.stream().collect(Collectors.groupingBy(ExtValueConfiguration::getExtTablePkId)).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().stream().collect(Collectors.toMap(ExtValueConfiguration::getExtId, i -> i))));
            }
        }
        // 扩展字段表数据集合
        Map<Integer, Map<String, String>> extFieldTableDataMap = extFieldConfigurationService.findExtFieldTableDataMap(AssetDeviceServiceImpl.TABLE_NAME, true);
        Map<String, ResourceStructure> allResourceStructureList = resourceStructureManager.getLevelOfPathNameMap("_");
        for (AssetDeviceDTO assetDeviceDTO : batchImportDTO.getAssetDeviceDTOList()) {
            AssetCategory assetCategory = categoryMap.get(assetDeviceDTO.getAssetCategoryName());
            if (ObjectUtil.isNotEmpty(assetCategory)) {
                assetDeviceDTO.setAssetCategoryId(assetCategory.getAssetCategoryId());
                assetDeviceDTO.setAssetCategory(assetCategory);
            }
            if (Boolean.TRUE.equals(batchImportDTO.getSaveAssetCategory()) && assetDeviceDTO.getAssetCategoryId() == null && Objects.isNull(assetCategory)) {
                if (CharSequenceUtil.isNotEmpty(assetDeviceDTO.getAssetCategoryName())) {
                    assetCategory = new AssetCategory();
                    assetCategory.setAssetCategoryName(assetDeviceDTO.getAssetCategoryName());
                    assetCategoryService.create(assetCategory);
                    assetDeviceDTO.setAssetCategory(assetCategory);
                    assetDeviceDTO.setAssetCategoryId(assetDeviceDTO.getAssetCategory().getAssetCategoryId());
                }
            }
            AssetDevice assetDevice = this.findByAssetCode(assetDeviceDTO.getAssetCode());
            if (Objects.nonNull(assetDevice)) {
                AssetDevice updateAssetDevice = getAssetDeviceByDTO(assetDeviceDTO, assetDevice);
                assetDeviceList.add(updateAssetDevice);
                assetDeviceMapper.updateById(updateAssetDevice);
                if (CollUtil.isNotEmpty(assetExtValueMap)) {
                    List<ExtValueConfiguration> extValueConfigurationList = updateAssetDevice.getExtValueConfigurationList();
                    for (ExtValueConfiguration extValueConfiguration : extValueConfigurationList) {
                        ExtFieldConfiguration extFieldConfiguration = extTypeMap.get(extValueConfiguration.getExtId());
                        Map<Integer, ExtValueConfiguration> extValueMap = assetExtValueMap.get(updateAssetDevice.getAssetDeviceId());
                        if (CollUtil.isNotEmpty(extValueMap)) {
                            ExtValueConfiguration valueConfiguration = extValueMap.get(extValueConfiguration.getExtId());
                            boolean save = Objects.isNull(valueConfiguration);
                            if (fillExtValueData(assetDeviceDTO, extValueConfiguration, extFieldConfiguration, allResourceStructureList, extFieldTableDataMap))
                                continue;
                            if (save) {
                                extValueConfiguration.setExtTablePkId(assetDevice.getAssetDeviceId());
                                extValueConfiguration.setExtTable(TABLE_NAME);
                                extValueConfigurationService.create(extValueConfiguration);
                            }else {
                                extValueConfiguration.setExtValId(valueConfiguration.getExtValId());
                                extValueConfigurationService.updateById(extValueConfiguration);
                            }
                        }
                    }
                }
            } else {
                AssetDevice assetDeviceEntity = assetDeviceDTO.build();
                assetDeviceEntity.setTableName(TABLE_NAME);
                assetDeviceMapper.insert(assetDeviceEntity);
                if(CollUtil.isNotEmpty(assetDeviceEntity.getExtValueConfigurationList())){
                    for (ExtValueConfiguration extValueConfiguration : assetDeviceEntity.getExtValueConfigurationList()) {
                        ExtFieldConfiguration extFieldConfiguration = extTypeMap.get(extValueConfiguration.getExtId());
                        if (fillExtValueData(assetDeviceDTO, extValueConfiguration, extFieldConfiguration, allResourceStructureList, extFieldTableDataMap))
                            continue;
                        extValueConfiguration.setExtTablePkId(assetDeviceEntity.getAssetDeviceId());
                        extValueConfiguration.setExtTable(TABLE_NAME);
                        extValueConfigurationService.create(extValueConfiguration);
                    }
                }
                result.add(assetDeviceEntity);
            }
        }
        result.addAll(assetDeviceList);
        return result;
    }

    private boolean fillExtValueData(AssetDeviceDTO assetDeviceDTO, ExtValueConfiguration extValueConfiguration, ExtFieldConfiguration extFieldConfiguration, Map<String, ResourceStructure> allResourceStructureList, Map<Integer, Map<String, String>> extFieldTableDataMap) {
        if ("equipment".equals(extFieldConfiguration.getExtDataType())) {
            if (CharSequenceUtil.isEmpty(extValueConfiguration.getExtValue())) {
                return true;
            }
            int lastIndexOf = extValueConfiguration.getExtValue().lastIndexOf("_");
            String resourceLevelOfPath = extValueConfiguration.getExtValue().substring(0, lastIndexOf);
            ResourceStructure resourceStructure = allResourceStructureList.get(resourceLevelOfPath);
            if (Objects.isNull(resourceStructure)) {
                log.error("资产批量导入，{}资产的{}字段，找不到相应的层级：{}", assetDeviceDTO.getAssetName(), extFieldConfiguration.getExtName(), resourceLevelOfPath);
                return true;
            }
            List<Equipment> equipmentList = equipmentManager.findEquipmentsByResourceStructureId(resourceStructure.getResourceStructureId());
            String equipmentName = extValueConfiguration.getExtValue().substring(lastIndexOf + 1);
            Optional<Equipment> first = equipmentList.stream().filter(e -> Objects.equals(e.getEquipmentName(), equipmentName)).findFirst();
            if (first.isEmpty()) {
                log.error("资产批量导入，{}资产的{}字段，找不到{}层级下的{}设备", assetDeviceDTO.getAssetName(), extFieldConfiguration.getExtName(), resourceLevelOfPath, equipmentName);
                return true;
            }
            Equipment equipment = first.get();
            JSONObject obj = new JSONObject();
            obj.set("eqId", equipment.getEquipmentId());
            obj.set("eqName", equipment.getEquipmentName());
            obj.set("rId", resourceStructure.getResourceStructureId());
            obj.set("positionId", resourceStructure.getLevelOfPath().replace(".", "-"));
            JSONArray data = new JSONArray();
            data.add(obj);
            extValueConfiguration.setExtValue(data.toString());
        }else if ("table".equals(extFieldConfiguration.getExtDataType())) {
            Map<String, String> extTableDataMap = extFieldTableDataMap.get(extValueConfiguration.getExtId());
            String extFieldTableValueId = extTableDataMap.get(extValueConfiguration.getExtValue());
            if (Objects.nonNull(extFieldTableValueId)) {
                extValueConfiguration.setExtValue(extFieldTableValueId);
            }
        }
        return false;
    }

    private AssetDevice getAssetDeviceByDTO(AssetDeviceDTO assetDeviceDTO, AssetDevice assetDevice) {
        // assetDeviceDTO.setObjectId(assetDevice.getObjectId());
        // assetDeviceDTO.setObjectType(assetDevice.getObjectType());
        // assetDeviceDTO.setGlobalResourceId(assetDevice.getGlobalResourceId());
        // assetDeviceDTO.setSortIndex(assetDevice.getSortIndex());
        if (StringUtils.isNotEmpty(assetDevice.getTableName())){
            assetDeviceDTO.setTableName(assetDevice.getTableName());
        }
        else {
            assetDeviceDTO.setTableName(TABLE_NAME);
        }
        assetDeviceDTO.setAssetDeviceId(assetDevice.getAssetDeviceId());
        return assetDeviceDTO.build();
    }

    private AssetDevice findByAssetCode(String assetCode) {
        LambdaUpdateWrapper<AssetDevice> wrapper = Wrappers.lambdaUpdate(AssetDevice.class);
        wrapper.eq(AssetDevice::getAssetCode, assetCode);
        return assetDeviceMapper.selectOne(wrapper);
    }

    @Override
    public List<AssetDevice> findAssetDeviceByParam(AssetDevice assetDevice) {
        LambdaQueryWrapper<AssetDevice> wrapper = Wrappers.lambdaQuery(AssetDevice.class);
        wrapper.eq(ObjectUtil.isNotEmpty(assetDevice.getAssetCategoryId()), AssetDevice::getAssetCategoryId, assetDevice.getAssetCategoryId());
        wrapper.eq(CharSequenceUtil.isNotEmpty(assetDevice.getAssetCode()), AssetDevice::getAssetCode, assetDevice.getAssetCode());
        wrapper.eq(CharSequenceUtil.isNotEmpty(assetDevice.getModel()), AssetDevice::getModel, assetDevice.getModel());
        wrapper.eq(CharSequenceUtil.isNotEmpty(assetDevice.getAssetName()), AssetDevice::getAssetName, assetDevice.getAssetName());
        wrapper.eq(CharSequenceUtil.isNotEmpty(assetDevice.getBrand()), AssetDevice::getBrand, assetDevice.getBrand());
        return assetDeviceMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssetDevice create(AssetDevice assetDevice) {
        checkForm(assetDevice);
        assetDevice.setTableName(TABLE_NAME);
        assetDeviceMapper.insert(assetDevice);
        if (CollUtil.isNotEmpty(assetDevice.getExtValueConfigurationList())) {
            SystemConfig smsenable = Optional.ofNullable(systemConfigService.findBySystemConfigKey("assetDevice.extField.equipment.LinkageModifyAssetInfo.enable")).orElse(new SystemConfig());
            boolean linkageModifyAssetInfoEnable = Boolean.parseBoolean(CharSequenceUtil.emptyToDefault(smsenable.getSystemConfigValue(), "true"));
            for (ExtValueConfiguration extValueConfiguration : assetDevice.getExtValueConfigurationList()) {
                if (ObjectUtil.isNotNull(extValueConfiguration.getExtDataType()) && extValueConfiguration.getExtDataType().equals("equipment")) {
                    if (linkageModifyAssetInfoEnable) {
                        String eqId = jsonToString(extValueConfiguration.getExtValue(), "eqId");
                        if (ObjectUtil.isNotNull(eqId) && !eqId.equals("")) {
//                        if (isBind(extValueConfiguration.getExtTablePkId(), extValueConfiguration.getExtId(), eqId, false)) {
//                            log.error("This Equipment Has Been Bound,EquipmentId is " + extValueConfiguration.getExtValue());
//                            assetDeviceMapper.deleteById(assetDevice);
//                            return null;
//                        }
                            Equipment equipment = equipmentManager.getEquipmentById(Integer.valueOf(eqId));
                            if (ObjectUtil.isNotNull(equipment)) {
                                String equipmentCategoryName = Optional.ofNullable(dataItemService.findByEntryIdIdAndItemId(7, equipment.getEquipmentCategory()))
                                        .map(DataItem::getItemValue).orElse("null");
                                QueryWrapper<AssetCategory> queryWrapper = new QueryWrapper<>();
                                queryWrapper.eq("AssetCategoryName",equipmentCategoryName);
                                AssetCategory existCategory = assetCategoryMapper.selectOne(queryWrapper);

                                String fullPath = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
                                LambdaUpdateWrapper<AssetDevice> updateWrapper = Wrappers.lambdaUpdate(AssetDevice.class);
                                updateWrapper.eq(AssetDevice::getAssetDeviceId, assetDevice.getAssetDeviceId());
                                updateWrapper.set(AssetDevice::getAssetName, assetDevice.getAssetName());
                                if(ObjectUtil.isNull(existCategory)){
                                    AssetCategory newCategory = new AssetCategory();
                                    newCategory.setAssetCategoryName(equipmentCategoryName);
                                    assetCategoryMapper.insert(newCategory);
                                    updateWrapper.set(AssetDevice::getAssetCategoryId,newCategory.getAssetCategoryId());
                                }else {
                                    updateWrapper.set(AssetDevice::getAssetCategoryId,existCategory.getAssetCategoryId());
                                }
                                updateWrapper.set(AssetDevice::getBrand, assetDevice.getBrand());
                                updateWrapper.set(AssetDevice::getCapacityParameter, assetDevice.getCapacityParameter());
                                updateWrapper.set(AssetDevice::getSettingPosition, fullPath);
                                updateWrapper.set(AssetDevice::getSerialNumber, equipment.getEquipmentSn());
                                updateWrapper.set(AssetDevice::getManufactor, equipment.getVendor());
                                assetDeviceMapper.update(updateWrapper);
                            }
                        }
                    }
                }
                extValueConfiguration.setExtTablePkId(assetDevice.getAssetDeviceId());
                extValueConfiguration.setExtTable(TABLE_NAME);
                extValueConfigurationService.create(extValueConfiguration);
            }
        }
        return assetDevice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssetDevice updateById(AssetDevice assetDevice) {
        checkForm(assetDevice);
        AssetDevice oldAssetDevice = assetDeviceMapper.selectList(Wrappers.lambdaQuery()).stream().filter(o->o.getAssetDeviceId().equals(assetDevice.getAssetDeviceId())).findFirst().orElse(null);
        if (CollUtil.isNotEmpty(assetDevice.getExtValueConfigurationList())) {
            SystemConfig smsenable = Optional.ofNullable(systemConfigService.findBySystemConfigKey("assetDevice.extField.equipment.LinkageModifyAssetInfo.enable")).orElse(new SystemConfig());
            boolean linkageModifyAssetInfoEnable = Boolean.parseBoolean(CharSequenceUtil.emptyToDefault(smsenable.getSystemConfigValue(), "true"));
            for (ExtValueConfiguration extValueConfiguration : assetDevice.getExtValueConfigurationList()) {
                if (ObjectUtil.isNotNull(extValueConfiguration.getExtDataType()) && extValueConfiguration.getExtDataType().equals("equipment")) {
                    if (linkageModifyAssetInfoEnable) {
                        String eqId = jsonToString(extValueConfiguration.getExtValue(), "eqId");
                        if (ObjectUtil.isNotNull(eqId) && !eqId.equals("")) {
//                        if (isBind(extValueConfiguration.getExtTablePkId(), extValueConfiguration.getExtId(), eqId, true)) {
//                            log.error("This Equipment Has Been Bound,EquipmentId is " + extValueConfiguration.getExtValue());
//                            return null;
//                        }
                            Equipment equipment = equipmentManager.getEquipmentById(Integer.valueOf(eqId));
                            if (ObjectUtil.isNotNull(equipment) ) {
                                String equipmentCategoryName = Optional.ofNullable(dataItemService.findByEntryIdIdAndItemId(7, equipment.getEquipmentCategory()))
                                        .map(DataItem::getItemValue).orElse("null");
                                QueryWrapper<AssetCategory> queryWrapper = new QueryWrapper<>();
                                queryWrapper.eq("AssetCategoryName",equipmentCategoryName);
                                AssetCategory existCategory = assetCategoryMapper.selectOne(queryWrapper);
                                if(ObjectUtil.isNull(existCategory)){
                                    AssetCategory newCategory = new AssetCategory();
                                    newCategory.setAssetCategoryName(equipmentCategoryName);
                                    assetCategoryMapper.insert(newCategory);
                                    assetDevice.setAssetCategoryId(newCategory.getAssetCategoryId());
                                }else {
                                    assetDevice.setAssetCategoryId(existCategory.getAssetCategoryId());
                                }
                                String fullPath = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
                                assetDevice.setManufactor(equipment.getVendor());
                                assetDevice.setSettingPosition(fullPath);
                                assetDevice.setSerialNumber(equipment.getEquipmentSn());
                            } else
                                log.error("This Equipment Info Is Null, EquipmentId Is " + eqId);
                        }
                    }
                }
                extValueConfiguration.setExtTablePkId(assetDevice.getAssetDeviceId());
                extValueConfiguration.setExtTable(TABLE_NAME);
                if (ObjectUtil.isEmpty(extValueConfiguration.getExtValId())) {
                    extValueConfigurationService.create(extValueConfiguration);
                    continue;
                }
                extValueConfigurationService.updateById(extValueConfiguration);
            }
        }
        assetDeviceMapper.updateById(assetDevice);
        this.saveOperationLog("update", assetDevice, oldAssetDevice);
        return assetDevice;
    }
    /**
     * 对绑定了动环设备的资产进行权限过滤，其他的资产不影响
     */
    @Override
    public List<AssetDevice> findAll() {
        //todo 用户权限过滤
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        List<AssetDevice> allAsset = assetDeviceMapper.selectList(Wrappers.lambdaQuery());
        //拥有所有区域的权限 获取所有设备
        if (Boolean.TRUE.equals(regionService.isAllRegion(loginUserId))){
            return allAsset;
        }
        List<EquipmentDTO> equipmentDTOs = equipmentService.findEquipmentDTOsByUserId(loginUserId);
        List<AssetDevice> res = new ArrayList<>();
        boolean show ;

        for (AssetDevice oneAsset : allAsset) {
            List<ExtValueConfiguration> valueConfigurations = oneAsset.getExtValueConfigurationList();
            if (CollUtil.isEmpty(valueConfigurations)) {
                res.add(oneAsset);
            } else {
                show = true;
                for (ExtValueConfiguration valueConfiguration : valueConfigurations) {
                    if (valueConfiguration.getExtFieldConfiguration().getExtDataType().equals("equipment")) {
                        String eqId = jsonToString(valueConfiguration.getExtValue(), "eqId");
                        if (ObjectUtil.isNotNull(eqId) && !eqId.equals("")) {
                            EquipmentDTO exist = equipmentDTOs.stream().
                                    filter(o -> o.getEqId().equals(Integer.parseInt(eqId)))
                                    .findFirst().orElse(null);
                            if (ObjectUtil.isNull(exist)) {
                                show = false;
                                break;
                            }
                        }
                    }
                }
                if (show)
                    res.add(oneAsset);
            }
        }
        return res;
    }

    @Override
    public IPage<AssetDevice> page(Page<AssetDevice> objectPage, AssetDeviceFilterVo filterVo) {
        // 所有资产
        List<AssetDevice> assetDevices = assetDeviceMapper.selectList(Wrappers.lambdaQuery());
        if (CharSequenceUtil.isNotEmpty(filterVo.getAssetCategoryName())) {
            assetDevices = assetDevices.stream().filter(e -> CharSequenceUtil.contains(e.getAssetCategoryName(), filterVo.getAssetCategoryName())).toList();
            Page<AssetDevice> result = new Page<>(objectPage.getCurrent(), objectPage.getSize(), assetDevices.size());
            result.setRecords(assetDevices);
            return result;
        }

        Integer loginUserId = TokenUserUtil.getLoginUserId();
        // 是否返回所有数据
        boolean returnAllData = regionService.isAllRegion(loginUserId);
        List<EquipmentDTO> equipmentDTOs = returnAllData ? Collections.emptyList() : equipmentService.findEquipmentDTOsByUserId(loginUserId);
        // 没有全部权限，并且没有数据，返回空数据
        if (! returnAllData && CollUtil.isEmpty(equipmentDTOs)) {
            return new Page<>(objectPage.getCurrent(), objectPage.getSize(), 0);
        }

        List<Integer> assetDeviceIds = assetDevices.stream().map(AssetDevice::getAssetDeviceId).toList();
        // 资产扩展字段值集合
        Map<Integer, List<ExtValueConfiguration>> extValueConfigurationMap = extValueConfigurationService.findByExtTablePkIdsAndExtTable(assetDeviceIds, AssetDeviceServiceImpl.TABLE_NAME).stream().collect(Collectors.groupingBy(ExtValueConfiguration::getExtTablePkId));
        // 资产扩展字段集合
        Set<Integer> extIds = extValueConfigurationMap.values().stream().flatMap(Collection::stream).map(ExtValueConfiguration::getExtId).collect(Collectors.toSet());
        Map<Integer, ExtFieldConfiguration> extFieldConfigurationMap = extFieldConfigurationService.findByExtTableWithExtIds(AssetDeviceServiceImpl.TABLE_NAME, extIds).stream().collect(Collectors.toMap(ExtFieldConfiguration::getExtId, e -> e));

        // 有权限的设备id集合
        Set<Integer> equipmentIds = equipmentDTOs.stream().map(EquipmentDTO::getEqId).collect(Collectors.toSet());
        Map<Integer, Map<String, String>> extFieldTableDataMap = extFieldConfigurationService.findExtFieldTableDataMap(AssetDeviceServiceImpl.TABLE_NAME, false);
        // 过滤权限
        assetDevices = assetDevices.stream().filter(e -> {
            // 获取扩展字段列表
            List<ExtValueConfiguration> extValueList = extValueConfigurationMap.get(e.getAssetDeviceId());
            // 处理扩展字段，判断是否有权限
            if (CollUtil.isNotEmpty(extValueList)) {
                for (ExtValueConfiguration extValue : extValueList) {
                    ExtFieldConfiguration extField = extFieldConfigurationMap.get(extValue.getExtId());
                    if (Objects.nonNull(extField)) {
                        // 检查设备权限
                        if (Objects.equals(extField.getExtDataType(), "equipment")) {
                            String eqId = jsonToString(extValue.getExtValue(), "eqId");
                            if (CharSequenceUtil.isNotEmpty(eqId)){
                                int equipmentId = Integer.parseInt(eqId);
                                if (!returnAllData && !equipmentIds.contains(equipmentId)) {
                                    return false;  // 没有权限则过滤掉
                                }
                                Equipment equipment = equipmentService.findById(Integer.parseInt(eqId));
                                String resourceFullPath = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
                                extValue.setExtValue(resourceFullPath + "_" + equipment.getEquipmentName());
                            }else {
                                extValue.setExtValue(null);
                            }
                        }else if (Objects.equals(extField.getExtDataType(), "table")) {
                            Map<String, String> extTableDataMap = extFieldTableDataMap.get(extField.getExtId());
                            extValue.setExtValue(extTableDataMap.get(extValue.getExtValue()));
                        }
                        extValue.setExtFieldConfiguration(extField);
                    }
                }
                e.setExtValueConfigurationList(extValueList);
            }
            // 处理关键字筛选
            if (CharSequenceUtil.isNotEmpty(filterVo.getKeyword())) {
                String keyword = filterVo.getKeyword();
                boolean find = CharSequenceUtil.contains(e.getAssetCode(), keyword) ||
                        CharSequenceUtil.contains(e.getAssetName(), keyword) ||
                        CharSequenceUtil.contains(e.getAssetCategoryName(), keyword) ||
                        CharSequenceUtil.contains(e.getBrand(), keyword) ||
                        CharSequenceUtil.contains(e.getModel(), keyword) ||
                        CharSequenceUtil.contains(e.getCapacityParameter(), keyword) ||
                        CharSequenceUtil.contains(e.getSettingPosition(), keyword) ||
                        CharSequenceUtil.contains(e.getSerialNumber(), keyword) ||
                        CharSequenceUtil.contains(e.getManufactor(), keyword);
                // 如果没有匹配设备属性，继续查找扩展字段中的匹配
                if (!find && CollUtil.isNotEmpty(extValueList)) {
                    for (ExtValueConfiguration extValue : extValueList) {
                        ExtFieldConfiguration extField = extFieldConfigurationMap.get(extValue.getExtId());
                        if (Objects.nonNull(extField) && CharSequenceUtil.contains(extValue.getExtValue(), keyword)) {
                            find = true;  // 扩展字段找到匹配
                            break;
                        }
                    }
                }
                return find;  // 返回匹配结果
            }
            return true;  // 如果没有关键字筛选条件，则通过过滤
        }).toList();
        List<AssetDevice> slice;
        if (objectPage.getSize() == -1) {
            // 查全部,供导出使用
            slice = assetDevices;
        } else {
            slice = assetDevices.stream().skip((objectPage.getCurrent() - 1) * objectPage.getSize())
                    .limit(objectPage.getSize())
                    .toList();
        }
        Page<AssetDevice> result = new Page<>(objectPage.getCurrent(), objectPage.getSize(), assetDevices.size());
        result.setRecords(slice);
        return result;
    }

    //资产请求是前端分页，所以这个分页的方法暂时屏蔽（上面的分页方法没有用到，没做权限处理）
//    public IPage<AssetDevice> page(Page<AssetDevice> objectPage, AssetDeviceFilterVo filterVo) {
//        LambdaQueryWrapper<AssetDevice> wrapper = Wrappers.lambdaQuery(AssetDevice.class);
//        List<AssetCategory> assetCategoryList = assetCategoryMapper.findByAssetCategoryName(filterVo.getAssetCategoryName());
//        List<Integer> idList = null;
//        if (CollUtil.isNotEmpty(assetCategoryList)) {
//            idList = assetCategoryList.stream().map(AssetCategory::getAssetCategoryId).toList();
//            wrapper.in(StringUtils.isNotEmpty(idList), AssetDevice::getAssetCategoryId, idList);
//        }
//
//        //资产管理新增绑定设备的资产需要添加权限管理,所以在此手动分页
//        long pageNum = objectPage.getCurrent();
//        long pageSize = objectPage.getSize();
//        List<AssetDevice> filterAsset = findAll();
//        //所有有权限的资产
//        if (CollUtil.isNotEmpty(idList)) {
//            List<Integer> finalIdList = idList;
//            filterAsset = filterAsset.stream()
//                    .filter(assetDevice -> finalIdList.contains(assetDevice.getAssetCategoryId()))
//                    .collect(Collectors.toList());
//        }
//        int fromIndex = Math.max((int) ((pageNum - 1) * pageSize), 0);
//        int toIndex = (int) Math.min(fromIndex + pageSize, filterAsset.size());
//        if (fromIndex <= filterAsset.size()) {
//            filterAsset = filterAsset.subList(fromIndex, toIndex);
//        } else {
//            filterAsset = new ArrayList<>(); // 如果起始位置超出列表大小，返回空列表
//        }
//        IPage<AssetDevice> res = assetDeviceMapper.selectPage(objectPage, wrapper);
//        res.setRecords(filterAsset);
//        return res;
//    }
    @Override
    public AssetDevice findById(Integer id) {
        return assetDeviceMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Integer id) {
        int delete = assetDeviceMapper.deleteById(id);
        extValueConfigurationService.deleteByExtTableAndExtTablePkId(TABLE_NAME, id);
        return delete > 0;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteByIds(List<Integer> ids) {
        assetDeviceMapper.deleteByIds(ids);
        List<ExtValueConfiguration> extValueConfigurations = extValueConfigurationService.findByExtTablePkIdsAndExtTable(ids, TABLE_NAME);
        if (CollUtil.isNotEmpty(extValueConfigurations)){
            List<Integer> valueIds = extValueConfigurations.stream().map(ExtValueConfiguration::getExtValId).toList();
            extValueConfigurationMapper.deleteByIds(valueIds);
        }
    }

    private void checkForm(AssetDevice assetDevice) {
        LambdaUpdateWrapper<AssetDevice> wrapper = Wrappers.lambdaUpdate(AssetDevice.class);
        wrapper.eq(AssetDevice::getAssetCode, assetDevice.getAssetCode());
        wrapper.ne(AssetDevice::getAssetDeviceId, assetDevice.getAssetDeviceId());
        if (ObjectUtil.isNotEmpty(assetDeviceMapper.selectOne(wrapper))) {
            throw new BusinessException(localeMessageSourceUtil.getMessage("asset.assetCode.exist"));
        }
    }

    /**
     * 判断此设备有没有被绑定
     */
    public Boolean isBind(Integer extTablePkId, Integer extId, String equipmentId, Boolean isUpdate) {
        QueryWrapper<ExtValueConfiguration> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ExtId", extId);
        queryWrapper.isNotNull("ExtValue");
        List<ExtValueConfiguration> extValues = extValueConfigurationMapper.selectList(queryWrapper);
        ExtValueConfiguration exist;
        if (isUpdate) {
            exist = extValues.stream().filter(
                    o -> !o.getExtTablePkId().equals(extTablePkId) &&
                            ObjectUtil.isNotNull(jsonToString(o.getExtValue(), "eqId")) &&
                            Objects.equals(jsonToString(o.getExtValue(), "eqId"), equipmentId)).findFirst().orElse(null);
        } else {
            exist = extValues.stream().filter(o -> ObjectUtil.isNotNull(jsonToString(o.getExtValue(), "eqId")) &&
                    Objects.equals(jsonToString(o.getExtValue(), "eqId"), equipmentId)).findFirst().orElse(null);
        }
        return ObjectUtil.isNotNull(exist);
    }

    @Override
    public void saveOperationLog(String operateType, AssetDevice newAssetDevice, AssetDevice oldAssetDevice) {
        try {
            Integer loginUserId = TokenUserUtil.getLoginUserId();
            OperationRecord operationRecord = new OperationRecord();
            operationRecord.setUserId(loginUserId);
            operationRecord.setOperation(newAssetDevice.getAssetDeviceId());
            operationRecord.setOperationType(3); // 固定为操作类型3

            String operationContent;

            // 根据操作类型处理日志
            switch (operateType) {
                case "add":
                    operationContent = "新增资产：资产ID为" + newAssetDevice.getAssetDeviceId();
                    break;
                case "update":
                    operationContent = generateUpdateLog(newAssetDevice, oldAssetDevice);
                    break;
                case "delete":
                    operationContent = "删除资产：资产ID为" + newAssetDevice.getAssetDeviceId();
                    break;
                default:
                    throw new IllegalArgumentException("未知的操作类型: " + operateType);
            }
            if (operationContent.length() > 3000) {
                operationContent = operationContent.substring(0, 3000);
            }
            operationRecord.setOperationContent(operationContent);
            operationRecordService.saveOperationRecord(operationRecord);

        } catch (Exception ex) {
            log.error("Save OperationLog Error, OperateType: " + operateType + ", AssetDeviceId: " + newAssetDevice.getAssetDeviceId(), ex);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFromEquipment() {
        if (bytedanceCheckExtend()) {
            //失败
            return null;
        }

        // 获取所有设备
        List<Equipment> allEquipments = equipmentManager.getAllEquipments();
        Map<Integer, Equipment> allEquipmentMap = allEquipments.stream().collect(Collectors.toMap(Equipment::getEquipmentId, e -> e));
        Set<String> equipmentIdSet = allEquipments.stream()
                .map(equipment -> equipment.getEquipmentId().toString())
                .collect(Collectors.toSet());

        // 获取已有资产设备 ID 集合
        List<AssetDevice> currentAssetDevices = assetDeviceMapper.selectList(new LambdaQueryWrapper<AssetDevice>().eq(AssetDevice::getTableName,FROM_EQUIPMENT));

        Set<String> assetEquIdsSet = currentAssetDevices.stream()
                .map(AssetDevice::getAssetCode)
                .filter(com.baomidou.mybatisplus.core.toolkit.StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        // 未同步的设备（新增）
        List<Equipment> equipmentsToAdd = allEquipments.stream()
                .filter(equipment -> !assetEquIdsSet.contains(equipment.getEquipmentId().toString()))
                .toList();

        // 筛选需要更新的设备（已存在，但需要更新信息）
        List<AssetDevice> equipmentsToUpdate = currentAssetDevices.stream()
                .filter(assetDevice -> equipmentIdSet.contains(assetDevice.getAssetCode()))
                .toList();

        // 筛选需要删除的设备（存在于数据库中但不再同步的设备）
        List<AssetDevice> equipmentsToDelete = currentAssetDevices.stream()
                .filter(assetDevice -> !equipmentIdSet.contains(assetDevice.getAssetCode()))
                .toList();

        Map<Integer, String> resourceIdLevelOfPathMap = resourceStructureManager.getResourceIdLevelOfPathMap(allEquipments.stream().map(Equipment::getResourceStructureId).distinct().toList());
        Map<Integer, String> resourceFullPathNameMap = resourceStructureManager.getResourceFullPath(resourceIdLevelOfPathMap);
        List<ExtValueConfiguration> insertValue = new ArrayList<>();
        List<ExtValueConfiguration> updateValue = new ArrayList<>();
        // 新增资产
        if (CollUtil.isNotEmpty(equipmentsToAdd)){
            Map<String, List<ExtValueConfiguration>> valuesByEquipmentId = new HashMap<>();
            List <AssetDevice> assetDevices = equipmentsToAdd.stream().map(equipment -> {
                String fullPath = resourceIdLevelOfPathMap.get(equipment.getResourceStructureId());
                String levelOfPathName = resourceFullPathNameMap.get(equipment.getResourceStructureId());
                AssetDevice assetDevice = getAssetDevice(equipment, levelOfPathName);

                List<ExtValueConfiguration> valueList = new ArrayList<>();
                if(equipment.getBuyDate() != null){
                    valueList.add(new ExtValueConfiguration(ByteDanceAssetExtFieldEnum.PURCHASE_DATE.getExtId(), DateUtil.dateToDayString(equipment.getBuyDate()),TABLE_NAME));
                }
                valueList.add(new ExtValueConfiguration(ByteDanceAssetExtFieldEnum.LEVEL_OF_PATH_NOSHOW.getExtId(), fullPath,TABLE_NAME));

                valuesByEquipmentId.put(equipment.getEquipmentId().toString(), valueList);
                return assetDevice;
            }).toList();

            assetDeviceMapper.saveBatch(assetDevices);
            // 获取主键
            Map<String, Integer> assetDeviceIdMap = assetDeviceMapper.getAssetDeviceByCodesAndTableName(
                            equipmentsToAdd.stream().map(Equipment::getEquipmentId).map(String::valueOf).distinct().toList(), FROM_EQUIPMENT)
                    .stream().collect(Collectors.toMap(AssetDevice::getAssetCode, AssetDevice::getAssetDeviceId));

            valuesByEquipmentId.entrySet().parallelStream().forEach(entry -> {
                Integer assetDeviceId = assetDeviceIdMap.get(entry.getKey());
                entry.getValue().forEach(a -> a.setExtTablePkId(assetDeviceId));
            });
            insertValue.addAll(valuesByEquipmentId.values().stream().flatMap(List::stream).toList());
        }
        // 更新资产
        if (CollUtil.isNotEmpty(equipmentsToUpdate)){

            List<ExtValueConfiguration> extValueConfigurationList =  extValueConfigurationMapper.selectMapByTableNameAndTablePkIds(TABLE_NAME,equipmentsToUpdate.stream().map(AssetDevice::getAssetDeviceId).toList());

            Map<Integer, Map<Integer,ExtValueConfiguration>>  assetExtValueMap = extValueConfigurationList.stream()
                    .collect(Collectors.groupingBy(ExtValueConfiguration::getExtTablePkId)).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().stream()
                            .collect(Collectors.toMap(ExtValueConfiguration::getExtId, i -> i))));

            equipmentsToUpdate.forEach(assetDevice -> {
                Equipment equipment = allEquipmentMap.getOrDefault(Integer.parseInt(assetDevice.getAssetCode()),new Equipment());
                String fullPath = resourceIdLevelOfPathMap.get(equipment.getResourceStructureId());
                String levelOfPathName = resourceFullPathNameMap.get(equipment.getResourceStructureId());

                if (ObjectUtil.isNotNull(equipment.getEquipmentName())) {
                    assetDevice.setAssetName(equipment.getEquipmentName());
                }
                if (ObjectUtil.isNotNull(equipment.getEquipmentCategory())){
                    assetDevice.setAssetCategoryId(equipment.getEquipmentCategory());
                }
                if (ObjectUtil.isNotNull(equipment.getEquipmentStyle())){
                    assetDevice.setModel(equipment.getEquipmentStyle());
                }
                if (ObjectUtil.isNotNull(equipment.getVendor())){
                    assetDevice.setManufactor(equipment.getVendor());
                }
                if (ObjectUtil.isNotNull(equipment.getEquipmentSn())){
                    assetDevice.setSerialNumber(equipment.getEquipmentSn());
                }
                if (levelOfPathName != null) {
                    assetDevice.setSettingPosition(levelOfPathName);
                }
                if (ObjectUtil.isNotNull(equipment.getBuyDate())){
                    ExtValueConfiguration buyDate = assetExtValueMap.getOrDefault(assetDevice.getAssetDeviceId(), new HashMap<>()).getOrDefault(ByteDanceAssetExtFieldEnum.PURCHASE_DATE.getExtId(), null);
                    if (buyDate == null){
                        buyDate = new ExtValueConfiguration(assetDevice.getAssetDeviceId(),ByteDanceAssetExtFieldEnum.PURCHASE_DATE.getExtId(),DateUtil.dateToDayString(equipment.getBuyDate()) ,TABLE_NAME);
                        insertValue.add(buyDate);
                    }
                    else{
                        buyDate.setExtValue(DateUtil.dateToDayString(equipment.getBuyDate()));
                        updateValue.add(buyDate);
                    }
                }

                ExtValueConfiguration levelOfPathNameValue = assetExtValueMap.getOrDefault(assetDevice.getAssetDeviceId(), new HashMap<>())
                        .getOrDefault(ByteDanceAssetExtFieldEnum.LEVEL_OF_PATH_NOSHOW.getExtId(), null);
                if (levelOfPathNameValue == null){
                    levelOfPathNameValue = new ExtValueConfiguration(assetDevice.getAssetDeviceId(),ByteDanceAssetExtFieldEnum.LEVEL_OF_PATH_NOSHOW.getExtId(),fullPath,TABLE_NAME);
                    insertValue.add(levelOfPathNameValue);
                }
                else {
                    levelOfPathNameValue.setExtValue(fullPath);
                    updateValue.add(levelOfPathNameValue);
                }
            });
            assetDeviceMapper.deleteByIds(equipmentsToUpdate.stream().map(AssetDevice::getAssetDeviceId).toList());
            assetDeviceMapper.saveBatchWithId(equipmentsToUpdate);

        }
        //删除资产
        if (CollUtil.isNotEmpty(equipmentsToDelete)){
            List<Integer> deleteDeviceIds = equipmentsToDelete.stream().map(AssetDevice::getAssetDeviceId).toList();
            assetDeviceMapper.deleteByIds(deleteDeviceIds);
            extValueConfigurationMapper.delete(new LambdaQueryWrapper<ExtValueConfiguration>().in(ExtValueConfiguration::getExtTablePkId,deleteDeviceIds).eq(ExtValueConfiguration::getExtTable,TABLE_NAME));
        }
        if (CollUtil.isNotEmpty(insertValue)){
            extValueConfigurationMapper.saveBatch(insertValue);
        }
        if (CollUtil.isNotEmpty(updateValue)){
            extValueConfigurationMapper.deleteByIds(updateValue.stream().map(ExtValueConfiguration::getExtValId).toList());
            extValueConfigurationMapper.saveBatchWithId(updateValue);

        }

        //资产类型维护
        assetCategoryMapper.syncFromEquipmentCategory();

        return "Add:"+equipmentsToAdd.size()+" Update:"+equipmentsToUpdate.size()+" Del:"+equipmentsToDelete.size();
    }

    @Override
    public ExcelWriter exportAllAssetDevices() {
        IPage<AssetDevice> page = this.page(new Page<>(1, -1), new AssetDeviceFilterVo());
        List<AssetDevice> records = page.getRecords();
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("assetCode", localeMessageSourceUtil.getMessage("assetdevice.assetCode"));
        writer.addHeaderAlias("assetName", localeMessageSourceUtil.getMessage("assetdevice.assetName"));
        writer.addHeaderAlias("assetCategoryName", localeMessageSourceUtil.getMessage("assetdevice.assetCategoryName"));
        writer.addHeaderAlias("brand", localeMessageSourceUtil.getMessage("assetdevice.brand"));
        writer.addHeaderAlias("model", localeMessageSourceUtil.getMessage("assetdevice.model"));
        writer.addHeaderAlias("capacityParameter", localeMessageSourceUtil.getMessage("assetdevice.capacityParameter"));
        writer.addHeaderAlias("settingPosition", localeMessageSourceUtil.getMessage("assetdevice.settingPosition"));
        writer.addHeaderAlias("serialNumber", localeMessageSourceUtil.getMessage("assetdevice.serialNumber"));
        writer.addHeaderAlias("manufactor", localeMessageSourceUtil.getMessage("assetdevice.manufactor"));
        List<ExtFieldConfiguration> extFieldConfigurations = extFieldConfigurationMapper.selectList(Wrappers.lambdaQuery(ExtFieldConfiguration.class).eq(ExtFieldConfiguration::getExtTable, TABLE_NAME));
        if (CollUtil.isNotEmpty(extFieldConfigurations)) {
            extFieldConfigurations.forEach(extFieldConfiguration -> {
                if (CharSequenceUtil.contains(extFieldConfiguration.getExtCode(), "_noshow")) {
                    return;
                }
                String fieldName = extFieldConfiguration.getExtName();
                writer.addHeaderAlias(fieldName, fieldName);
            });
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.renameSheet("资产列表");
        List<Map<String, Object>> list = new ArrayList<>();
        records.forEach(assetDevice -> {
            // assetDevice转map
            Map<String, Object> map = BeanUtil.beanToMap(assetDevice);
            List<ExtValueConfiguration> extValueConfigurationList = assetDevice.getExtValueConfigurationList();
            if (CollUtil.isNotEmpty(extFieldConfigurations)) {
                extFieldConfigurations.forEach(extFieldConfiguration -> {
                    if (CharSequenceUtil.contains(extFieldConfiguration.getExtCode(), "_noshow")) {
                        return;
                    }
                    String fieldName = extFieldConfiguration.getExtName();
                    map.put(fieldName, "");
                });
            }
            if (CollUtil.isNotEmpty(extValueConfigurationList)) {
                extValueConfigurationList.forEach(extValueConfiguration -> {
                    String fieldName = Optional.ofNullable(extValueConfiguration).map(ExtValueConfiguration::getExtFieldConfiguration)
                            .map(ExtFieldConfiguration::getExtName).orElse(null);
                    map.put(fieldName, extValueConfiguration.getExtValue());
                });
            }
            list.add(map);
        });
        writer.write(list);
        return writer;
    }

    @NotNull
    private static AssetDevice getAssetDevice(Equipment equipment, String levelOfPathName) {
        AssetDevice assetDevice = new AssetDevice();

        assetDevice.setAssetCode(equipment.getEquipmentId().toString());
        assetDevice.setAssetName(equipment.getEquipmentName());
        assetDevice.setAssetCategoryId(equipment.getEquipmentCategory());
        assetDevice.setModel(equipment.getEquipmentStyle());
        assetDevice.setManufactor(equipment.getVendor());
        assetDevice.setSerialNumber(equipment.getEquipmentSn());
        assetDevice.setTableName("tbl_equipment");
        assetDevice.setSettingPosition(levelOfPathName);
        return assetDevice;
    }


    // 生成更新操作的日志内容
    private String generateUpdateLog(AssetDevice newAssetDevice, AssetDevice oldAssetDevice) {
        StringBuilder sb = new StringBuilder();
        sb.append("修改资产：资产ID为").append(newAssetDevice.getAssetDeviceId()).append(";");

        // 使用一个通用方法比较并记录字段差异
        compareAndLog(sb, "编号", oldAssetDevice.getAssetCode(), newAssetDevice.getAssetCode());
        compareAndLog(sb, "名称", oldAssetDevice.getAssetName(), newAssetDevice.getAssetName());
        compareAndLog(sb, "类型", oldAssetDevice.getAssetCategoryName(), newAssetDevice.getAssetCategoryName());
        compareAndLog(sb, "品牌", oldAssetDevice.getBrand(), newAssetDevice.getBrand());
        compareAndLog(sb, "厂商", oldAssetDevice.getManufactor(), newAssetDevice.getManufactor());
        compareAndLog(sb, "位置", oldAssetDevice.getSettingPosition(), newAssetDevice.getSettingPosition());
        compareAndLog(sb, "型号", oldAssetDevice.getModel(), newAssetDevice.getModel());
        compareAndLog(sb, "容量参数", oldAssetDevice.getCapacityParameter(), newAssetDevice.getCapacityParameter());
        compareAndLog(sb, "序列号", oldAssetDevice.getSerialNumber(), newAssetDevice.getSerialNumber());
        compareExtValueList(sb, oldAssetDevice.getExtValueConfigurationList(),newAssetDevice.getExtValueConfigurationList());
        return sb.toString();
    }
    private void compareExtValueList(StringBuilder sb ,List<ExtValueConfiguration> oldList, List<ExtValueConfiguration> newList) {
        // 提取旧列表中的 extValue 值
        List<String> oldExtValues = oldList.stream()
                .map(ExtValueConfiguration::getExtValue)
                .collect(Collectors.toList());
        // 提取新列表中的 extValue 值
        List<String> newExtValues = newList.stream()
                .map(ExtValueConfiguration::getExtValue)
                .collect(Collectors.toList());
        // 调用 compareAndLog 方法比较两者
        compareAndLog(sb, "扩展字段", oldExtValues, newExtValues);
    }
    private <T> void compareAndLog(StringBuilder sb, String fieldName, T oldValue, T newValue) {
        if (!ObjectUtil.equals(newValue, oldValue)) {
            sb.append("修改前").append(fieldName).append(":").append(oldValue)
                    .append(", 修改后").append(fieldName).append(":").append(newValue).append(";");
        }
    }
    //"eqId"
    public static String jsonToString(String json, String field) {
        try {
            if (CharSequenceUtil.isBlank(json)) {
                return null;
            }
            JSONArray jsonArray = JSONUtil.parseArray(json);
            if (CollUtil.isEmpty(jsonArray)) {
                return null;
            }
            return CollUtil.join(jsonArray.stream().map(e -> ((JSONObject) e).getStr(field)).filter(e -> !Objects.isNull(e)).toList(), ",");

        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * 字节资产扩展字段校验，失败 导入 配置等直接失效
     * @return
     */
    public boolean bytedanceCheckExtend() {
        // 获取所有 extField 的映射
        Map<Integer, String> extFiledIdCodeMap = extFieldConfigurationService.findAll()
                .stream()
                .collect(Collectors.toMap(ExtFieldConfiguration::getExtId, ExtFieldConfiguration::getExtCode));

        // 检查是否存在不匹配的情况
        return Arrays.stream(ByteDanceAssetExtFieldEnum.values())
                .anyMatch(enumItem -> !StringUtils.equals(enumItem.getExtCode(), extFiledIdCodeMap.get(enumItem.getExtId())));
    }

        public <T> void executeBatch(List<T> dataList, int batchSize, Consumer<List<T>> batchExecutor) {
            if (dataList == null || dataList.isEmpty()) {
                return; // 空数据直接返回
            }

            for (int i = 0; i < dataList.size(); i += batchSize) {
                List<T> batchList = dataList.subList(i, Math.min(i + batchSize, dataList.size()));
                batchExecutor.accept(batchList);
            }
        }
}
