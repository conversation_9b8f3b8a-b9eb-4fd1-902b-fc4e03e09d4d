<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.HistoryEventMapper">

    <sql id="Base_Column_List">
        SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName, EventConditionId,
        EventLevel, EventSeverity, StartTime, EndTime, CancelTime, CancelUserId, CancelUserName, ConfirmTime,
        ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath, Description, SourceHostId,
        InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId, BaseTypeName,
        EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId,
        EventStateId, CenterId, CenterName, StructureName, MonitorUnitName, StructureId, StationCategoryId,
        EquipmentVendor, EndValue, ResourceStructureId, BaseEquipmentId
    </sql>
    <insert id="batchInsert">
        <foreach collection="list" item="item" separator=";">
            MERGE INTO tbl_historyevent USING DUAL
            ON (startTime = #{item.startTime} AND stationId = #{item.stationId} AND
            equipmentId = #{item.equipmentId} AND eventId = #{item.eventId} AND
            eventConditionId = #{item.eventConditionId})
            WHEN MATCHED THEN
            UPDATE SET
            stationName = #{item.stationName},
            equipmentName = #{item.equipmentName},
            eventName = #{item.eventName},
            eventSeverityId = #{item.eventSeverityId},
            eventSeverity = #{item.eventSeverity},
            eventLevel = #{item.eventLevel},
            endTime = #{item.endTime},
            cancelTime = #{item.cancelTime},
            cancelUserId = #{item.cancelUserId},
            cancelUserName = #{item.cancelUserName},
            confirmTime = #{item.confirmTime},
            confirmerId = #{item.confirmerId},
            confirmerName = #{item.confirmerName},
            eventValue = #{item.eventValue},
            endValue = #{item.endValue},
            reversalNum = #{item.reversalNum},
            meanings = #{item.meanings},
            eventFilePath = #{item.eventFilePath},
            description = #{item.description},
            sourceHostId = #{item.sourceHostId},
            instructionId = #{item.instructionId},
            instructionStatus = #{item.instructionStatus},
            standardAlarmNameId = #{item.standardAlarmNameId},
            standardAlarmName = #{item.standardAlarmName},
            baseTypeId = #{item.baseTypeId},
            baseTypeName = #{item.baseTypeName},
            equipmentCategory = #{item.equipmentCategory},
            equipmentCategoryName = #{item.equipmentCategoryName},
            maintainState = #{item.maintainState},
            signalId = #{item.signalId},
            relateSequenceId = #{item.relateSequenceId},
            eventCategoryId = #{item.eventCategoryId},
            eventStateId = #{item.eventStateId},
            centerId = #{item.centerId},
            centerName = #{item.centerName},
            structureName = #{item.structureName},
            monitorUnitName = #{item.monitorUnitName},
            structureId = #{item.structureId},
            stationCategoryId = #{item.stationCategoryId},
            equipmentVendor = #{item.equipmentVendor},
            convergenceEventId = #{item.convergenceEventId},
            resourceStructureId = #{item.resourceStructureId},
            baseEquipmentId = #{item.baseEquipmentId}
            WHEN NOT MATCHED THEN
            INSERT (
            sequenceId, stationId, stationName, equipmentId, equipmentName, eventId, eventName,
            eventConditionId, eventSeverityId, eventSeverity, eventLevel, startTime, endTime,
            cancelTime, cancelUserId, cancelUserName, confirmTime, confirmerId, confirmerName,
            eventValue, endValue, reversalNum, meanings, eventFilePath, description, sourceHostId,
            instructionId, instructionStatus, standardAlarmNameId, standardAlarmName, baseTypeId,
            baseTypeName, equipmentCategory, equipmentCategoryName, maintainState, signalId,
            relateSequenceId, eventCategoryId, eventStateId, centerId, centerName, structureName,
            monitorUnitName, structureId, stationCategoryId, equipmentVendor, convergenceEventId,
            resourceStructureId, baseEquipmentId
            ) VALUES (
            #{item.sequenceId}, #{item.stationId}, #{item.stationName}, #{item.equipmentId},
            #{item.equipmentName}, #{item.eventId}, #{item.eventName}, #{item.eventConditionId},
            #{item.eventSeverityId}, #{item.eventSeverity}, #{item.eventLevel}, #{item.startTime},
            #{item.endTime}, #{item.cancelTime}, #{item.cancelUserId}, #{item.cancelUserName},
            #{item.confirmTime}, #{item.confirmerId}, #{item.confirmerName}, #{item.eventValue},
            #{item.endValue}, #{item.reversalNum}, #{item.meanings}, #{item.eventFilePath},
            #{item.description}, #{item.sourceHostId}, #{item.instructionId}, #{item.instructionStatus},
            #{item.standardAlarmNameId}, #{item.standardAlarmName}, #{item.baseTypeId},
            #{item.baseTypeName}, #{item.equipmentCategory}, #{item.equipmentCategoryName},
            #{item.maintainState}, #{item.signalId}, #{item.relateSequenceId}, #{item.eventCategoryId},
            #{item.eventStateId}, #{item.centerId}, #{item.centerName}, #{item.structureName},
            #{item.monitorUnitName}, #{item.structureId}, #{item.stationCategoryId},
            #{item.equipmentVendor}, #{item.convergenceEventId}, #{item.resourceStructureId},
            #{item.baseEquipmentId}
            )
        </foreach>
    </insert>

    <select id="findByBaseEquipmentIdAndStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE BaseEquipmentId=#{baseEquipmentId} AND StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="findByEquipmentIdAndStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE EquipmentId=#{equipmentId} AND StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="findByEventIdAndStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE EventId=#{eventId} AND StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="findByStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="countByStartTimeSpan" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM tbl_historyevent WHERE StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
    </select>

    <select id="listAlertEquipmentsByTimeSpan" resultType="java.lang.Integer">
        SELECT
        distinct EquipmentId
        FROM tbl_historyevent WHERE StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        GROUP BY EquipmentId
    </select>

    <select id="findByConvergenceEventId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE ConvergenceEventId  =  #{convergenceEventId}
        ORDER BY StartTime DESC
    </select>

    <select id="findByStartTimeAndEquipmentIdAndEventIdAndConditionId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        select th.* from ( select t.* from activeeventoperationlog t ) t
                                     left join tbl_historyevent th on t.StartTime = th.StartTime
            and t.StationId = th.StationId
            and t.EquipmentId = th.EquipmentId
            and t.EventId = th.EventId
            and t.EventConditionId = th.EventConditionId
        where t.StartTime = #{startDate}
          and t.EquipmentId = #{equipmentId,jdbcType=INTEGER}
          and t.EventId =  #{eventId,jdbcType=INTEGER}
          and t.StationId = #{stationId,jdbcType=INTEGER}
          and t.EventConditionId = #{eventConditionId,jdbcType=INTEGER}
        limit 1
    </select>
    <select id="findAlarmDurationByEquipmentIdAndEventId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_historyevent
        WHERE EquipmentId = #{equipmentId}
          AND EventId = #{eventId}
          AND StartTime &gt;= #{startTime}
          AND StartTime &lt;= #{endTime}
    </select>
    <select id="findDurationByStationIdsAndEventCategoryId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_historyevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.EventCategoryId = #{eventCategoryId}
        AND a.StartTime &gt;= #{startTime}
        AND a.StartTime &lt;= #{endTime}
        AND a.stationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
    <select id="findDurationByResourceStructureIdsAndEventCategoryId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_historyevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.EventCategoryId = #{eventCategoryId}
        AND a.StartTime &gt;= #{startTime}
        AND a.StartTime &lt;= #{endTime}
        AND a.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
    <select id="getPowerOffCountByStationIds" resultType="com.siteweb.monitoring.vo.HistoryPowerOffCountVO">
        SELECT StationId AS stationId, COUNT(*) AS count
        FROM tbl_historyevent
        WHERE EventCategoryId = #{eventCategoryId}
        AND StartTime &gt;= #{startTime}
        AND StartTime &lt;= #{endTime}
        AND StationId IN
        <foreach item="stationId"  collection="stationIds" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        GROUP BY StationId
    </select>

    <select id="isExistAlarmByEquipmentIdAndTime" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT *
        FROM tbl_historyevent
        WHERE EquipmentId = #{equipmentId}
        AND StartTime &lt;= #{time}
        AND endTime &gt;= #{time}
    </select>
    <select id="findAlarmCountByEquipmentIdAndEventId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tbl_historyevent
        WHERE EquipmentId = #{equipmentId}
        <if test="eventId != null">
            AND EventId = #{eventId}
        </if>
        <if test="startTime != null">
            AND StartTime &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND StartTime &lt;= #{endTime}
        </if>
    </select>
    <select id="groupHistoryEventsBySeverity" resultType="java.util.Map">
        SELECT
        EventLevel AS eventLevel,
        COUNT(*) AS count
        FROM tbl_historyevent
        WHERE StartTime &gt; #{startDate}
        AND StartTime &lt; #{endDate}
        AND EquipmentId IN
        <foreach item="equipmentId" collection="equipmentIds" open="(" separator="," close=")">
            #{equipmentId}
        </foreach>
        AND EventLevel IS NOT NULL
        GROUP BY EventLevel
    </select>

    <!-- 权限过滤的通用WHERE条件 -->
    <sql id="Permission_Filter_Condition">
        <where>
            <if test="startTime != null">
                AND StartTime &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND endTime &lt;= #{endTime}
            </if>
            <if test="equipmentIds != null and equipmentIds.size() > 0">
                AND EquipmentId IN
                <foreach item="equipmentId" collection="equipmentIds" open="(" separator="," close=")">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="resourceStructureIds != null and resourceStructureIds.size() > 0">
                AND ResourceStructureId IN
                <foreach item="resourceStructureId" collection="resourceStructureIds" open="(" separator="," close=")">
                    #{resourceStructureId}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 统计历史事件总数量 -->
    <select id="countHistoryEvents" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM tbl_historyevent
        <include refid="Permission_Filter_Condition"/>
    </select>

    <!-- 按设备统计历史事件数量 -->
    <select id="countHistoryEventsByEquipment" resultType="java.util.Map">
        SELECT
        EquipmentId AS equipmentId,
        EquipmentName AS equipmentName,
        COUNT(*) AS count
        FROM tbl_historyevent
        <include refid="Permission_Filter_Condition"/>
        GROUP BY EquipmentId, EquipmentName
        ORDER BY count DESC
    </select>

    <!-- 按事件等级统计历史事件数量 -->
    <select id="countHistoryEventsByEventLevel" resultType="java.util.Map">
        SELECT
        EventLevel AS eventLevel,
        EventSeverity AS eventSeverity,
        COUNT(*) AS count
        FROM tbl_historyevent
        <include refid="Permission_Filter_Condition"/>
        GROUP BY EventLevel, EventSeverity
        ORDER BY EventLevel
    </select>

    <!-- 按设备类型统计历史事件数量 -->
    <select id="countHistoryEventsByEquipmentCategory" resultType="java.util.Map">
        SELECT
        EquipmentCategory AS equipmentCategory,
        EquipmentCategoryName AS equipmentCategoryName,
        COUNT(*) AS count
        FROM tbl_historyevent
        <include refid="Permission_Filter_Condition"/>
        GROUP BY EquipmentCategory, EquipmentCategoryName
        ORDER BY count DESC
    </select>

    <!-- 按资源结构ID统计历史事件数量 -->
    <select id="countHistoryEventsByResourceStructureId" resultType="java.util.Map">
        SELECT
        ResourceStructureId AS resourceStructureId,
        COUNT(*) AS count
        FROM tbl_historyevent
        <include refid="Permission_Filter_Condition"/>
        GROUP BY ResourceStructureId
        ORDER BY count DESC
    </select>

    <sql id="queryHistoryEventFilter">
        <if test="1 == 1">
            AND  a.EventLevel in (
            SELECT
            CAST(td.ExtendField4 AS int)
            FROM
            tbl_dataitem td
            WHERE
            td.entryId = 23
            AND td.enable = 1
            )
        </if>
        <if test="(filter.permissionResourceStructureIds != null and filter.permissionResourceStructureIds.size() > 0)
             or (filter.permissionEquipmentIds != null and filter.permissionEquipmentIds.size() > 0)">
            AND (
            <if test="filter.permissionResourceStructureIds != null and filter.permissionResourceStructureIds.size() > 0">
                a.resourceStructureId IN
                <foreach collection="filter.permissionResourceStructureIds" item="rsId" open="(" separator="," close=")">
                    #{rsId}
                </foreach>
            </if>
            <if test="filter.permissionResourceStructureIds != null and filter.permissionResourceStructureIds.size() > 0
                  and filter.permissionEquipmentIds != null and filter.permissionEquipmentIds.size() > 0">
                OR
            </if>
            <if test="filter.permissionEquipmentIds != null and filter.permissionEquipmentIds.size() > 0">
                a.equipmentId IN
                <foreach collection="filter.permissionEquipmentIds" item="eqId" open="(" separator="," close=")">
                    #{eqId}
                </foreach>
            </if>
            )
        </if>
        <if test="filter.resourceStructureIds != null and filter.resourceStructureIds.size() > 0">
            AND a.resourceStructureId IN
            <foreach item="id" collection="filter.resourceStructureIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.equipmentCategories != null and filter.equipmentCategories.size() > 0">
            AND a.equipmentCategory IN
            <foreach item="id" collection="filter.equipmentCategories" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.baseEquipmentIds != null and filter.baseEquipmentIds.size() > 0">
            AND a.baseEquipmentId IN
            <foreach item="id" collection="filter.baseEquipmentIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.equipmentIds != null and filter.equipmentIds.size() > 0">
            AND a.equipmentId IN
            <foreach item="id" collection="filter.equipmentIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.eventSeverityIds != null and filter.eventSeverityIds.size() > 0">
            AND a.eventLevel IN
            <foreach item="id" collection="filter.eventSeverityIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.eventConfirmed != null">
            <choose>
                <when test="filter.eventConfirmed == true">
                    AND a.confirmTime IS NOT NULL
                </when>
                <otherwise>
                    AND a.confirmTime IS NULL
                </otherwise>
            </choose>
        </if>
        <if test="filter.eventEnded != null">
            <choose>
                <when test="filter.eventEnded == true">
                    AND a.endTime IS NOT NULL
                </when>
                <otherwise>
                    AND a.endTime IS NULL
                </otherwise>
            </choose>
        </if>
        <if test="filter.baseTypeIds != null and filter.baseTypeIds.size() > 0">
            AND a.baseTypeId IN
            <foreach item="id" collection="filter.baseTypeIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.convergented != null">
            <choose>
                <when test="filter.convergented == true">
                    AND a.convergenceEventId IS NOT NULL AND a.convergenceEventId > 0
                </when>
                <otherwise>
                    AND (a.convergenceEventId IS NULL OR a.convergenceEventId = 0)
                </otherwise>
            </choose>
        </if>
        <if test="filter.maintainStated != null">
            <choose>
                <when test="filter.maintainStated == true">
                    AND a.maintainState != 1
                </when>
                <otherwise>
                    AND a.maintainState = 1
                </otherwise>
            </choose>
        </if>
        <if test="filter.startTimeFrom != null">
            AND a.startTime &gt;= #{filter.startTimeFrom} </if>
        <if test="filter.startTimeTo != null">
            AND a.startTime &lt;= #{filter.startTimeTo}   </if>
        <if test="filter.keywords != null and filter.keywords != ''">
            AND (
            a.equipmentName LIKE CONCAT('%', #{filter.keywords}, '%')
            OR a.eventName LIKE CONCAT('%', #{filter.keywords}, '%')
            OR a.baseTypeName LIKE CONCAT('%', #{filter.keywords}, '%')
            OR a.equipmentCategoryName LIKE CONCAT('%', #{filter.keywords}, '%')
            OR b.baseEquipmentName LIKE CONCAT('%', #{filter.keywords}, '%')
            OR a.eventSeverity LIKE CONCAT('%', #{filter.keywords}, '%')
            OR a.meanings LIKE CONCAT('%', #{filter.keywords}, '%')
            OR a.description LIKE CONCAT('%', #{filter.keywords}, '%')
            )
        </if>
        <if test="filter.handleStatus != null ">
            <choose>
                <when test="filter.handleStatus == 1">
                    AND a.confirmTime IS NULL AND a.endTime IS NULL
                </when>
                <when test="filter.handleStatus == 2">
                    AND a.confirmTime IS NULL AND a.endTime IS NOT NULL
                </when>
                <when test="filter.handleStatus == 3">
                    AND a.confirmTime IS NOT NULL AND a.endTime IS NULL
                </when>
                <when test="filter.handleStatus == 4">
                    AND a.confirmTime IS NOT NULL AND a.endTime IS NOT NULL
                </when>
            </choose>
        </if>
    </sql>
    <select id="queryHistoryEventPage" resultType="com.siteweb.monitoring.dto.HistoryEventPageDTO">
        SELECT
        a.SequenceId, a.StationId, a.StationName, a.EquipmentId, a.EquipmentName, a.EventId, a.EventName, a.EventConditionId,
        a.EventSeverity, a.EventLevel, a.StartTime, a.EndTime, a.CancelTime, a.CancelUserId, a.CancelUserName, a.ConfirmTime,
        a.ConfirmerId, a.ConfirmerName, a.EventValue, a.ReversalNum, a.Meanings, a.EventFilePath, a.Description, a.SourceHostId,
        a.InstructionId, a.InstructionStatus, a.StandardAlarmNameId, a.StandardAlarmName, a.BaseTypeId, a.BaseTypeName,
        a.EquipmentCategory, a.EquipmentCategoryName, a.MaintainState, a.SignalId, a.RelateSequenceId, a.EventCategoryId,
        a.EventStateId, a.CenterId, a.CenterName, a.StructureName, a.MonitorUnitName, a.StructureId, a.StationCategoryId,
        a.EquipmentVendor,
        a.EndValue, a.ConvergenceEventId, a.ResourceStructureId, a.BaseEquipmentId, b.BaseEquipmentName,a.EventSeverityId
        FROM tbl_historyevent a
        left join TBL_EquipmentBaseType b on a.BaseEquipmentId = b.BaseEquipmentId
        <where>
            <include refid="queryHistoryEventFilter"/>
        </where>
        ORDER BY
        <choose>
            <when test="sort != null and !sort.empty">
                <foreach item="order" collection="sort" separator=", ">
                    a.${order.property} ${order.direction}
                </foreach>
            </when>
            <otherwise>
                a.StartTime desc
            </otherwise>
        </choose>
    </select>

    <select id="groupHistoryEventBySeverity" resultType="com.siteweb.monitoring.dto.HistoryEventFilterGroupDTO">
        SELECT a.eventLevel AS eventLevel, COUNT(*) AS count
        FROM tbl_historyevent a
        LEFT JOIN TBL_EquipmentBaseType b on a.BaseEquipmentId = b.BaseEquipmentId
        <where>
            <if test="standardAlarmNameIdIsNotNull != null and standardAlarmNameIdIsNotNull == true ">
                AND a.standardAlarmNameId IS NOT NULL
            </if>
            <include refid="queryHistoryEventFilter"/>
        </where>
        GROUP BY a.eventLevel
    </select>
</mapper>