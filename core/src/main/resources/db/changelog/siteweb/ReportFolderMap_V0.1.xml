<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_reportfoldermap_table" author="shj" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="reportfoldermap" remarks="报表文件夹映射信息">
            <column name="id" type="int" remarks="主键ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="reportId" type="int" remarks="报表ID">
                <constraints nullable="false"/>
            </column>
            <column name="folderId" type="int" remarks="文件夹ID">
                <constraints nullable="false"/>
            </column>
            <column name="sortIndex" type="int" remarks="排序值">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="reportfoldermap" columnName="id" incrementBy="1" columnDataType="int" startWith="1"/>
        <addUniqueConstraint tableName="reportfoldermap" columnNames="reportId, folderId" constraintName="uniq_report_folder"/>
    </changeSet>

</databaseChangeLog>