package com.siteweb.admin.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class MenuItemDTO extends MenuLanguageBase<MenuItemDTO> {

	private Integer menuItemId;

	private Integer parentId;

	private String path;

	private String title;
	/**
	 * 别名
	 */
	private String alias;

	private String icon;

	private Boolean selected;

	private Boolean expanded;

	private String pathMatch;

	private Integer layoutPosition;

	private Boolean isSystemConfig;

	private Boolean isExternalWeb;

	private Boolean menuHasNavigation;

	private String description;

	private List<MenuItemDTO> children;
}
