package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SampleValueFormatUtil;
import com.siteweb.monitoring.dto.ConfigSignalDTO;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.SimpleSignalDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.model.HistorySignalGroupByTime;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.report.dto.HistorySignalExportDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.enums.TimeGranularityTypeEnum;
import com.siteweb.report.parser.model.HistorySignalReportParam;
import com.siteweb.report.service.ReportExportService;
import com.siteweb.report.util.ReportUtil;
import com.siteweb.report.vo.ReportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史数据报表
 * //TODO 该报表中已经没有原始颗粒度的选项了，后期可以考虑将此相关的代码移除
 * @Author: lzy
 * @Date: 2022/5/18 14:49
 */
@Component
@Slf4j
public class HistorySignalParser extends ReportParser {
    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    SignalService signalService;
    @Autowired
    ConfigSignalManager configSignalManager;
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    ReportExportService reportExportService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    private static final String POSITION = "position";
    private static final String SIGNAL_ID = "signalId";
    private static final String EQUIPMENT_ID = "equipmentId";
    private static final String BASE_TYPE_ID = "baseTypeId";
    private static final String UNIT = "unit";
    private static final String SIGNAL_NAME = "signalName";
    private static final String EQUIPMENTNAME = "equipmentName";
    private static final String VALUES = "values";
    private static final String VALUE = "value";
    private static final String DISPLAY_VALUE = "displayValue";
    private static final String ORIGINAL = "original";
    private static final String RESOURCE_STRUCTURE_NAME = "resourceStructureName";
    private final ThreadLocal<Boolean> unitThreadLocal = ThreadLocal.withInitial(() -> true);

    // 当type=original时，代表前端使用原始数据样式，否则会根据输出参数拆分成最大值，最小值，平均值。
    @Override
    public JSONObject parser(ReportVO reportVO) {
        try {
            JSONObject objectNode = new JSONObject(true);
            HistorySignalReportParam historySignalReportParam = new HistorySignalReportParam(reportVO.getReportParameterPresetList());
            String timeGranularity = historySignalReportParam.getTimeGranularity();
            this.setUnit(historySignalReportParam.getUnit());
            boolean switchDatabase2 = Integer.valueOf(28).equals(reportVO.getReportSchemaId());
            //选择了设备信号 仅走设备信号方法
            if (CharSequenceUtil.isNotBlank(historySignalReportParam.getSignalIds())) {
                JSONArray signalJsonArray = JSONUtil.parseArray(historySignalReportParam.getSignalIds());
                objectNode = this.getJsonObjectBySignalInfo(reportVO, historySignalReportParam, timeGranularity, switchDatabase2, signalJsonArray);
                return objectNode;
            }
            //选择了信号基类信息
            if (CollUtil.isNotEmpty(historySignalReportParam.getBaseTypeIds())) {
                objectNode = this.getJsonObjectByBaseTypeId(reportVO, historySignalReportParam, timeGranularity, switchDatabase2);
                return objectNode;
            }
            return objectNode;
        } finally {
            unitThreadLocal.remove();
        }
    }

    private JSONObject getJsonObjectBySignalInfo(ReportVO reportVO, HistorySignalReportParam historySignalReportParam, String timeGranularity, boolean switchDatabase2, JSONArray signalJsonArray) {
        //过滤没有设备权限的信号
        Set<Integer> equipmentIdsByUserId = equipmentService.findEquipmentIdsByUserId(reportVO.getUserId());
        signalJsonArray.removeIf(e -> {
            JSONObject jsonObject = (JSONObject) e;
            Integer equipmentId = jsonObject.getInt(EQUIPMENT_ID);
            return !equipmentIdsByUserId.contains(equipmentId);
        });
        JSONObject objectNode;
        if (CharSequenceUtil.isEmpty(timeGranularity) || Objects.equals("0", timeGranularity)) {
            // 默认查询原始数据
            objectNode = this.queryOriginalData(signalJsonArray, historySignalReportParam, switchDatabase2);
        } else {
            // 时间颗粒度查询
            objectNode = this.queryDataByTimeGranularity(signalJsonArray, historySignalReportParam, reportVO.getPageable(), switchDatabase2);
        }
        return objectNode;
    }

    private JSONObject getJsonObjectByBaseTypeId(ReportVO reportVO, HistorySignalReportParam historySignalReportParam, String timeGranularity, boolean switchDatabase2) {
        JSONObject objectNode;
        if (CharSequenceUtil.isEmpty(timeGranularity) || Objects.equals("0", timeGranularity)) {
            // 默认查询原始数据
            objectNode = queryOriginalDataByBaseTypeId(historySignalReportParam, reportVO.getPageable(), switchDatabase2,reportVO.getUserId());
        } else {
            // 时间颗粒度查询
            objectNode = queryDataByTimeGranularityAndBaseTypeId(historySignalReportParam, reportVO.getPageable(), switchDatabase2,reportVO.getUserId());
        }
        return objectNode;
    }

    /**
     * 原始数据查询
     */
    public JSONObject queryOriginalData(JSONArray signalJsonArray, HistorySignalReportParam param, Boolean switchDatabase2) {
        JSONObject jsonObject = new JSONObject(true);
        if (signalJsonArray == null) {
            return jsonObject;
        }
        JSONArray chartArray = new JSONArray();
        JSONArray thJsonArray = new JSONArray();
        List<HistorySignal> historySignalList = new ArrayList<>();
        for (int i = 0; i < signalJsonArray.size(); i++) {
            JSONObject element = signalJsonArray.getJSONObject(i);
            Integer equipmentId = element.getInt(EQUIPMENT_ID);
            final int index = i;
            Integer signalId = element.getInt(SIGNAL_ID);
            // 调用influxdb 获取历史数据
            List<HistorySignal> historySignals = historySignalManager.findHistorySignalByDurationAndSignalType(param.getStartTime(), param.getEndTime(), param.getSignalTypes(), equipmentId, signalId, switchDatabase2);
            // 给当前历史数据添加sort
            historySignals.forEach(e -> {
                e.setSortIndex(index);
                e.setDisplayValue(this.getDisplayValue(e));
            });

            historySignalList.addAll(historySignals);
            // 设置表头
            JSONObject chartObject = new JSONObject(true);
            chartObject.set(SIGNAL_ID, element.getStr(SIGNAL_ID));
            chartObject.set(SIGNAL_NAME, element.getStr(SIGNAL_NAME));
            chartObject.set(EQUIPMENTNAME, element.getStr(EQUIPMENTNAME));
            chartObject.set(POSITION, element.getStr(POSITION));
            chartObject.set(UNIT, element.getStr(UNIT));
            chartObject.set(VALUES, historySignals);

            chartArray.add(chartObject);

            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(EQUIPMENTNAME, element.get(EQUIPMENTNAME));
            thJsonObject.set(POSITION, element.get(POSITION));
            thJsonObject.set(SIGNAL_NAME, element.get(SIGNAL_NAME));
            thJsonObject.set(UNIT, element.get(UNIT));
            thJsonArray.add(thJsonObject);
        }
        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonArray);
        jsonObject.set(ReportStructureEnum.RESULT.value(), getOriginalDataResultJsonArray(historySignalList, signalJsonArray.size()));
        jsonObject.set(ReportStructureEnum.CHARTS.value(), chartArray);
        jsonObject.set("type", ORIGINAL);

        return jsonObject;
    }

    public JSONObject queryOriginalDataByBaseTypeId(HistorySignalReportParam param, Pageable pageable, Boolean switchDatabase2,Integer userId) {
        JSONArray baseTypeIdJsonArray = JSONUtil.parseArray(param.getBaseTypeIds());
        JSONObject jsonObject = new JSONObject(true);
        JSONArray chartArray = new JSONArray();
        JSONArray thJsonArray = new JSONArray();
        Set<String> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId).stream().map(String::valueOf).collect(Collectors.toSet());
        Set<Integer> queryEquipmentIdSet = param.getEquipmentIds();
        List<HistorySignal> historySignalList = new ArrayList<>();
        for (int i = 0; i < baseTypeIdJsonArray.size(); i++) {
            JSONObject element = baseTypeIdJsonArray.getJSONObject(i);
            // 调用influxdb 获取历史数据
            List<HistorySignal> historySignals = historySignalManager.findHistorySignalByBaseTypeIdAndSignalType(param.getStartTime(), param.getEndTime(),param.getSignalTypes(), element.getStr(BASE_TYPE_ID), pageable, switchDatabase2);
            for (HistorySignal historySignal : historySignals) {
                String equipmentId = historySignal.getDeviceId();
                //没有设备权限
                if (!equipmentIdSet.contains(equipmentId)) {
                    continue;
                }
                //不需要查询该设备信号
                if (CollUtil.isNotEmpty(queryEquipmentIdSet) && !queryEquipmentIdSet.contains(Integer.valueOf(equipmentId))) {
                    continue;
                }
                historySignalList.add(historySignal);
            }
        }
        Map<String, List<HistorySignal>> historySignalMap = historySignalList.stream().collect(Collectors.groupingBy(HistorySignal::getSignalId));
        Map<Integer, String> equipmentMap = new HashMap<>();
        Map<String, String> signalNameMap = new HashMap<>();
        int i = 0;
        for (Map.Entry<String, List<HistorySignal>> realSignal : historySignalMap.entrySet()) {
            JSONObject chartObject = new JSONObject(true);
            final Integer index = i;
            String[] signalSplit = realSignal.getKey().split("\\.");
            Integer equipmentId =  Integer.parseInt(signalSplit[0]);
            Integer signalId = Integer.parseInt(signalSplit[1]);
            // 给当前历史数据添加sort index
            List<HistorySignal> historySignals = realSignal.getValue();
            if (CollUtil.isNotEmpty(historySignals)) {
                historySignals.forEach(e -> {
                    e.setSortIndex(index);
                    e.setDisplayValue(this.getDisplayValue(e));
                });
            }
            //设置列头
            //获取信号
            chartObject.set(SIGNAL_ID, signalId);
            String signalName = signalNameMap.computeIfAbsent(realSignal.getKey(), key -> this.findSignalName(equipmentId, signalId));
            //获取设备
            String equipmentName = equipmentMap.computeIfAbsent(equipmentId, key -> this.findEquipmentName(equipmentId));
            chartObject.set(SIGNAL_NAME, signalName);
            chartObject.set(EQUIPMENTNAME, equipmentName);
            chartObject.set(VALUES, historySignals);
            chartArray.add(chartObject);
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(EQUIPMENTNAME, equipmentName);
            thJsonObject.set(SIGNAL_NAME, signalName);
            thJsonArray.add(thJsonObject);
            i++;
        }

        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonArray);
        jsonObject.set(ReportStructureEnum.RESULT.value(), getOriginalDataResultJsonArray(historySignalList, i));
        jsonObject.set(ReportStructureEnum.CHARTS.value(), chartArray);
        jsonObject.set("type", ORIGINAL);
        return jsonObject;
    }

    private JSONObject queryDataByTimeGranularityAndBaseTypeId(HistorySignalReportParam params, Pageable pageable, Boolean switchDatabase2, Integer userId) {
        JSONObject result = new JSONObject(true);
        // 图标数据
        JSONArray chartArray = new JSONArray();
        // 表头列
        JSONArray thJsonArray = new JSONArray();

        // 时间格式
        String timeGranularity = params.getTimeGranularity();
        Boolean minMaxAvgEnable = checkMinMaxAvgEnable(timeGranularity);
        timeGranularity = Boolean.TRUE.equals(minMaxAvgEnable) ? timeGranularity : timeGranularity.split("-")[0];

        List<HistorySignalGroupByTime> historySignalGroupByTimeList = new ArrayList<>();
        Integer index = 0;
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId);
        List<SimpleSignalDTO> simpleSignalList = signalService.findSimpleSignalDTOsByBaseTypeIdsAndEquipmentIds(params.getBaseTypeIds(),params.getEquipmentIds());
        Map<Integer,String> equipmentResourceStructureNameMap = new HashMap<>();
        for (SimpleSignalDTO simpleSignalDTO : simpleSignalList) {
            Integer equipmentId = simpleSignalDTO.getEquipmentId();
            //是否拥有权限
            if (!ReportUtil.hasEquipmentPermission(equipmentIdSet, equipmentId)) {
                continue;
            }
            final Integer j = index;
            List<HistorySignalGroupByTime> historySignals;
            if (timeGranularity.contains("w")) {
                // 周查询
                historySignals = historySignalManager.findWeekHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(), simpleSignalDTO.getRealSignalKey(),
                        switchDatabase2,params.getValueRetrievalMethod());
            } else if (timeGranularity.contains("month")) {
                // 月查询
                historySignals = historySignalManager.findMonthHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(), simpleSignalDTO.getRealSignalKey(),
                        switchDatabase2,params.getValueRetrievalMethod());
            } else if (timeGranularity.contains("year")){
                // 年查询
                historySignals = historySignalManager.findYearHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(), simpleSignalDTO.getRealSignalKey(),
                        switchDatabase2,params.getValueRetrievalMethod());
            }
            else {
                historySignals = historySignalManager.findHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(), simpleSignalDTO.getRealSignalKey(), timeGranularity, pageable, switchDatabase2,params.getValueRetrievalMethod());
                if (pageable != null) {
                    Integer tempTotalElements = historySignalManager.countHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(), simpleSignalDTO.getRealSignalKey(), timeGranularity, switchDatabase2);
                    ReportStructureEnum.updateTotalElement(result, tempTotalElements, pageable);
                }
            }
            // 赋值 sortIndex
            historySignals.forEach(e -> {
                e.setEquipmentId(equipmentId);
                e.setSignalId(simpleSignalDTO.getSignalId());
                e.setSortIndex(j);
                e.setDisplayValue(this.getDisplayValue(equipmentId, simpleSignalDTO.getSignalId(), e.getPointValue()));
            });
            historySignalGroupByTimeList.addAll(historySignals);
            historySignals.removeIf(e -> e.count == 0);
            // 设置表头
            JSONObject chartObject = new JSONObject(true);
            chartObject.set(SIGNAL_ID, simpleSignalDTO.getSignalId());
            chartObject.set(SIGNAL_NAME, simpleSignalDTO.getSignalName());
            chartObject.set(EQUIPMENTNAME, this.findEquipmentName(equipmentId));
            chartObject.set(VALUES, historySignals);
            chartObject.set(RESOURCE_STRUCTURE_NAME,equipmentResourceStructureNameMap.computeIfAbsent(equipmentId, key -> this.getResourceStructureNameByEquipmentId(equipmentId)));
            chartArray.add(chartObject);

            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(EQUIPMENTNAME, this.findEquipmentName(equipmentId));
            thJsonObject.set(SIGNAL_NAME, simpleSignalDTO.getSignalName());
            thJsonObject.set(RESOURCE_STRUCTURE_NAME,equipmentResourceStructureNameMap.computeIfAbsent(equipmentId, key -> this.getResourceStructureNameByEquipmentId(equipmentId)));
            thJsonArray.add(thJsonObject);
            index++;
        }
        result.set(ReportStructureEnum.TITLE.value(), thJsonArray);
        result.set(ReportStructureEnum.RESULT.value(), getTimeGranularityResultJsonArray(historySignalGroupByTimeList, index, params.getTimeGranularity()));
        result.set(ReportStructureEnum.CHARTS.value(), chartArray);
        if (!Boolean.TRUE.equals(minMaxAvgEnable)) {
            result.set("type", ORIGINAL);
        }
        return result;
    }

    private String findEquipmentName(Integer equipmentId) {
        return Optional.ofNullable(equipmentService.findById(equipmentId))
                       .orElseGet(Equipment::new)
                       .getEquipmentName();
    }

    private String findSignalName(Integer equipmentId, Integer signalId) {
        return Optional.ofNullable(signalService.findConfigSignalDTOBySignalId(equipmentId, signalId))
                       .orElseGet(ConfigSignalDTO::new)
                       .getSignalName();
    }

    /**
     * 获取原始数据的元数据
     *
     * @param historySignalList
     * @param sortIndexLength
     * @return
     */
    public JSONArray getOriginalDataResultJsonArray(List<HistorySignal> historySignalList, Integer sortIndexLength) {
        if (CollUtil.isEmpty(historySignalList)) {
            return new JSONArray();
        }
        historySignalList.sort((arg0, arg1) -> arg0.getTime().compareTo(arg1.getTime()) == 0 ? arg0.getSortIndex().compareTo(arg1.getSortIndex()) : arg0.getTime().compareTo(arg1.getTime()));

        JSONArray tdJsonArray = new JSONArray();
        int count = 0;
        String initTime = historySignalList.get(0).getTime();
        while (count < historySignalList.size()) {
            JSONObject tdJsonObject = new JSONObject(true);
            String tempTime = historySignalList.get(count).getTime();
            tdJsonObject.set("time", tempTime);
            JSONArray dataJsonArray = initOriginalDataRowDataJsonArray(sortIndexLength);

            while (tempTime.equals(initTime)) {
                Integer sortIndex = historySignalList.get(count).getSortIndex();
                JSONObject objectNode = (JSONObject) dataJsonArray.get(sortIndex);
                objectNode.set(VALUE, cn.hutool.core.util.NumberUtil.round(historySignalList.get(count).getPointValue(), 2));
                //取信号含义
                objectNode.set(DISPLAY_VALUE, this.getDisplayValue(historySignalList.get(count)));
                if (++count < historySignalList.size()) {
                    tempTime = historySignalList.get(count).getTime();
                } else {
                    break;
                }
            }
            tdJsonObject.set("data", dataJsonArray);
            ReportUtil.setRowCount(dataJsonArray, tdJsonObject);
            tdJsonArray.add(tdJsonObject);
            if (count < historySignalList.size()) {
                initTime = tempTime;
            } else {
                break;
            }
        }
        if (!tdJsonArray.isEmpty()) {
            ReportUtil.countColumnJsonArray(tdJsonArray, VALUE);
        }
        return tdJsonArray;
    }

    private String getDisplayValue(HistorySignal historySignal) {
        String realSignalKey = historySignal.getSignalId();
        String[] equipmentSignalSplit = realSignalKey.split("\\.");
        return getDisplayValue(Integer.valueOf(equipmentSignalSplit[0]), Integer.valueOf(equipmentSignalSplit[1]), historySignal.getPointValue());
    }

    private String getDisplayValue(Integer equipmentId, Integer signalId, String pointValue) {
        ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalId);
        if (Boolean.FALSE.equals(unitThreadLocal.get())) {
            return SampleValueFormatUtil.getFormatValue(configSignalItem.getShowPrecision(), pointValue);
        }
        return activeSignalManager.getCurrentValue(configSignalItem,pointValue);
    }

    /**
     * 原始数据查询结果，当某个时间段至少有一个测点的值不为空时,便需要显示该列,同列中值为空的用"-"代替,初始化该结构
     *
     * @param sortIndexLength 下标长度,既测点数量
     */
    private static JSONArray initOriginalDataRowDataJsonArray(Integer sortIndexLength) {
        JSONArray dataJsonArray = new JSONArray();
        for (int i = 0; i < sortIndexLength; i++) {
            JSONObject dataJsonObject = new JSONObject(true);
            dataJsonObject.set(VALUE, "-");
            dataJsonArray.add(dataJsonObject);
        }
        return dataJsonArray;
    }


    /***
     * 时间格式查询
     */
    private JSONObject queryDataByTimeGranularity(JSONArray signalJsonArray, HistorySignalReportParam params, Pageable pageable, Boolean switchDatabase2) {
        JSONObject result = new JSONObject(true);
        if (signalJsonArray == null) {
            return result;
        }
        // 图标数据
        JSONArray chartArray = new JSONArray();
        // 表头列
        JSONArray thJsonArray = new JSONArray();

        // 时间格式
        String timeGranularity = params.getTimeGranularity();
        Boolean minMaxAvgEnable = checkMinMaxAvgEnable(timeGranularity);
        timeGranularity = Boolean.TRUE.equals(minMaxAvgEnable) ? timeGranularity : timeGranularity.split("-")[0];

        List<HistorySignalGroupByTime> historySignalGroupByTimeList = new ArrayList<>();
        Map<Integer,String> equipmentResourceStructureNameMap = new  HashMap<>();
        for (int i = 0; i < signalJsonArray.size(); i++) {
            final int index = i;
            JSONObject object = (JSONObject) signalJsonArray.get(i);
            List<HistorySignalGroupByTime> historySignals;
            Integer equipmentId = object.getInt(EQUIPMENT_ID);
            String signalId = object.getStr(SIGNAL_ID);
            if (timeGranularity.contains("w")) {
                // 周查询
                historySignals = historySignalManager.findWeekHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(),equipmentId + "." + signalId, switchDatabase2, params.getValueRetrievalMethod());
            } else if (timeGranularity.contains("month")) {
                // 月查询
                historySignals = historySignalManager.findMonthHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(),  params.getSignalTypes(),equipmentId + "." + signalId, switchDatabase2, params.getValueRetrievalMethod());
            } else if (timeGranularity.contains("year")){
                // 年查询
                historySignals = historySignalManager.findYearHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(),  params.getSignalTypes(),equipmentId + "." + signalId, switchDatabase2, params.getValueRetrievalMethod());
            }
            else {
                historySignals = historySignalManager.findHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(),equipmentId + "." + signalId, timeGranularity, pageable, switchDatabase2, params.getValueRetrievalMethod());
                if (pageable != null) {
                    Integer tempTotalElements = historySignalManager.countHistorySignalGroupByTimeAndSignalType(params.getStartTime(), params.getEndTime(), params.getSignalTypes(),equipmentId + "." + signalId, timeGranularity, switchDatabase2);
                    ReportStructureEnum.updateTotalElement(result, tempTotalElements, pageable);
                }
            }
            // 赋值 sortIndex
            historySignals.forEach(e -> {
                e.setSortIndex(index);
                e.setEquipmentId(equipmentId);
                e.setSignalId(Integer.valueOf(signalId));
                e.setDisplayValue(this.getDisplayValue(e.getEquipmentId(), e.getSignalId(), e.getPointValue()));
            });

            historySignalGroupByTimeList.addAll(historySignals);

            historySignals.removeIf(e -> e.count == 0);

            // 设置表头
            JSONObject chartObject = new JSONObject(true);
            chartObject.set(SIGNAL_ID, object.get(SIGNAL_ID));
            chartObject.set(SIGNAL_NAME, object.get(SIGNAL_NAME));
            chartObject.set(EQUIPMENTNAME, object.get(EQUIPMENTNAME));
            chartObject.set(POSITION, object.get(POSITION));
            chartObject.set(UNIT, object.get(UNIT));
            chartObject.set(VALUES, historySignals);
            chartObject.set(RESOURCE_STRUCTURE_NAME,equipmentResourceStructureNameMap.computeIfAbsent(equipmentId, key -> this.getResourceStructureNameByEquipmentId(equipmentId)));
            chartArray.add(chartObject);

            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(EQUIPMENTNAME, object.get(EQUIPMENTNAME));
            thJsonObject.set(POSITION, object.get(POSITION));
            thJsonObject.set(SIGNAL_NAME, object.get(SIGNAL_NAME));
            thJsonObject.set(UNIT, object.get(UNIT));
            thJsonObject.set(RESOURCE_STRUCTURE_NAME,equipmentResourceStructureNameMap.computeIfAbsent(equipmentId, key -> this.getResourceStructureNameByEquipmentId(equipmentId)));
            thJsonArray.add(thJsonObject);
        }
        result.set(ReportStructureEnum.TITLE.value(), thJsonArray);
        result.set(ReportStructureEnum.RESULT.value(), getTimeGranularityResultJsonArray(historySignalGroupByTimeList, signalJsonArray.size(), params.getTimeGranularity()));
        result.set(ReportStructureEnum.CHARTS.value(), chartArray);
        if (!Boolean.TRUE.equals(minMaxAvgEnable)) {
            result.set("type", ORIGINAL);
        }

        return result;
    }

    private String getResourceStructureNameByEquipmentId(Integer equipmentId) {
        return Optional.ofNullable(equipmentManager.getEquipmentById(equipmentId))
                       .map(e -> resourceStructureManager.getResourceStructureById(e.getResourceStructureId()))
                       .map(ResourceStructure::getResourceStructureName)
                       .orElse("");
    }

    /**
     * 时间粒度格式为（1m-start， 1h-end等）
     *
     * @param timeGranularity
     * @return
     */
    private static Boolean checkMinMaxAvgEnable(String timeGranularity) {
        if (timeGranularity.contains("-")) {
            TimeGranularityTypeEnum timeGranularityTypeEnum = TimeGranularityTypeEnum.getTimeGranularityTypeEnum(timeGranularity.split("-")[1]);
            return timeGranularityTypeEnum == null;
        }
        return true;
    }

    /**
     * 获取时间粒度的元数据
     *
     * @param historySignalGroupByTimeList
     * @param sortIndexLength
     * @param timeGranularity
     * @return
     */
    private JSONArray getTimeGranularityResultJsonArray(List<HistorySignalGroupByTime> historySignalGroupByTimeList, Integer sortIndexLength, String timeGranularity) {
        if (CollUtil.isEmpty(historySignalGroupByTimeList)) {
            return new JSONArray();
        }
        //将sortIndexLength个测点的数据进行排序,先按时间排序,再按下标排序
        historySignalGroupByTimeList.sort((arg0, arg1) -> arg0.getTime().compareTo(arg1.getTime()) == 0 ? arg0.getSortIndex().compareTo(arg1.getSortIndex()) : arg0.getTime().compareTo(arg1.getTime()));
        Boolean minMaxAvgEnable = checkMinMaxAvgEnable(timeGranularity);

        JSONArray tdJsonArray = new JSONArray();
        int count = 0;
        String initTime = historySignalGroupByTimeList.get(0).getTime();
        while (count < historySignalGroupByTimeList.size()) {
            JSONObject tdJsonObject = new JSONObject(true);
            String tempTime = historySignalGroupByTimeList.get(count).getTime();

            setTimeByTimeGranularity(tdJsonObject, timeGranularity.split("-")[0], historySignalGroupByTimeList, count, tempTime);

            JSONArray dataJsonArray = Boolean.TRUE.equals(minMaxAvgEnable) ? initTimeGranularityRowDataJsonArray(sortIndexLength) : initOriginalDataRowDataJsonArray(sortIndexLength);

            //同个时间范围的归为一列
            while (tempTime.equals(initTime)) {
                this.putItemJson(historySignalGroupByTimeList, count, dataJsonArray, minMaxAvgEnable);

                if (++count >= historySignalGroupByTimeList.size()) {
                    break;
                }
                tempTime = historySignalGroupByTimeList.get(count).getTime();
            }
            tdJsonObject.set("data", dataJsonArray);

            if (Boolean.TRUE.equals(minMaxAvgEnable)) {
                ReportUtil.countTimeGranularityRowJsonArray(tdJsonObject, dataJsonArray, 2);
            } else {
                ReportUtil.setRowCount(dataJsonArray, tdJsonObject);
            }

            tdJsonArray.add(tdJsonObject);
            if (count < historySignalGroupByTimeList.size()) {
                initTime = tempTime;
            } else {
                break;
            }
        }
        if (!tdJsonArray.isEmpty()) {
            if (Boolean.TRUE.equals(minMaxAvgEnable)) {
                ReportUtil.countColumnJsonArray(tdJsonArray, "max|min|avg");
            } else {
                ReportUtil.countColumnJsonArray(tdJsonArray, VALUE);
            }
        }
        return tdJsonArray;
    }

    private static void setTimeByTimeGranularity(JSONObject tdJsonObject, String timeGranularity, List<HistorySignalGroupByTime> historySignalGroupByTimeList, Integer count, String tempTime) {
        switch (timeGranularity) {
            case "1w":
                tdJsonObject.set("time", historySignalGroupByTimeList.get(count).getWeekDay());
                break;
            case "month":
                tdJsonObject.set("time", historySignalGroupByTimeList.get(count).getMonthDay());
                break;
            case "year":
                tdJsonObject.set("time", historySignalGroupByTimeList.get(count).getYearDay());
                break;
            default:
                tdJsonObject.set("time", tempTime);
                break;
        }
    }

    private void putItemJson(List<HistorySignalGroupByTime> historyDataPointGroupByTimeList, Integer count, JSONArray dataJsonArray, Boolean minMaxAvgEnable) {
        Integer sortIndex = historyDataPointGroupByTimeList.get(count).getSortIndex();
        JSONObject itemJson = dataJsonArray.getJSONObject(sortIndex);
        if (Boolean.FALSE.equals(minMaxAvgEnable)) {
            HistorySignalGroupByTime historySignalGroupByTime = historyDataPointGroupByTimeList.get(count);
            itemJson.set(DISPLAY_VALUE, this.getDisplayValue(historySignalGroupByTime.getEquipmentId(), historySignalGroupByTime.getSignalId(), historySignalGroupByTime.getPointValue()));
            itemJson.set(VALUE,historySignalGroupByTime.getPointValue());
            Optional.ofNullable(historySignalGroupByTime.getPointValue())
                    .ifPresent(v -> itemJson.set(VALUE, cn.hutool.core.util.NumberUtil.round(v, 2)));
        } else {
            itemJson.set("avg", historyDataPointGroupByTimeList.get(count).getAvg());
        }
    }

    /**
     * 聚合查询结果，当某个时间段至少有一个测点的值不为空时,便需要显示该列,同列中值为空的用"-"代替,初始化该结构
     *
     * @param sortIndexLength 下标长度,既测点数量
     */
    private static JSONArray initTimeGranularityRowDataJsonArray(Integer sortIndexLength) {
        JSONArray dataJsonArray = new JSONArray();
        for (int i = 0; i < sortIndexLength; i++) {
            JSONObject dataJsonObject = new JSONObject(true);
            dataJsonObject.set("max", "-");
            dataJsonObject.set("min", "-");
            dataJsonObject.set("avg", "-");
            dataJsonArray.add(dataJsonObject);
        }
        return dataJsonArray;
    }

    /**
     * 是否转义信号含义
     * @param unit 是否转义信号含义
     */
    private void setUnit(String unit){
        if (CharSequenceUtil.isBlank(unit)) {
            unitThreadLocal.set(false);
            return;
        }
        unitThreadLocal.set(Objects.equals(unit,"1"));
    }

    @Override
    public long countSample(ReportVO reportVO) {
        HistorySignalReportParam historySignalReportParam = new HistorySignalReportParam(reportVO.getReportParameterPresetList());
        historySignalReportParam.setSwitchDatabase2(Integer.valueOf(28).equals(reportVO.getReportSchemaId()));
        return reportExportService.historySignalReportCountSample(reportVO.getUserId(), historySignalReportParam);
    }

    @Override
    public long export(ReportVO reportVO,String filePath) {
        try (CsvWriter writer = CsvUtil.getWriter(Paths.get(filePath).toUri().getPath(), StandardCharsets.UTF_8, false)) {
            HistorySignalReportParam historySignalReportParam = new HistorySignalReportParam(reportVO.getReportParameterPresetList());
            historySignalReportParam.setSwitchDatabase2(Integer.valueOf(28).equals(reportVO.getReportSchemaId()));
            List<HistorySignalExportDTO> historySignal = reportExportService.findHistorySignal(reportVO.getUserId(), historySignalReportParam);
            writer.writeHeaderLine(messageSourceUtil.getMessage("common.report.csv.collectTime"), messageSourceUtil.getMessage("common.report.csv.equipmentName"), messageSourceUtil.getMessage("common.report.csv.signalName"), messageSourceUtil.getMessage("common.report.csv.signalValue"));
            writer.write(historySignal);
            return historySignal.size();
        }
    }

    private HistorySignalParser() {
        super(ReportDataSourceEnum.HISTORY_SIGNALS.getValue());
    }

}

