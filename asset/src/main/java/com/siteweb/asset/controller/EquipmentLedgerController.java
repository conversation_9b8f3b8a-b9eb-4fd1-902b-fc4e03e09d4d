package com.siteweb.asset.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.asset.service.EquipmentLedgerService;
import com.siteweb.utility.dto.IdValueDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

/**
 * @Description: 字节 设备台账页面相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/equipment")
public class EquipmentLedgerController {
    @Autowired
    EquipmentLedgerService equipmentLedgerService;
    @ApiOperation("统计设备型号")
    @GetMapping(value = "/modelstatistics")
    public ResponseEntity<ResponseResult> modelStatistics(){
        List<IdValueDTO<String, Integer>> idValueDTOS = equipmentLedgerService.modelStatistics();
        return ResponseHelper.successful(idValueDTOS);
    }
    @ApiOperation("设备型号树")
    @GetMapping(value = "/modeltree")
    public ResponseEntity<ResponseResult> getEquipmentTemplateStyleTree(){
        List<Tree<Integer>> result =  equipmentLedgerService.styleTree();
        return ResponseHelper.successful(result);
    }
    @ApiOperation("设备型号对应设备列表")
    @GetMapping(value = "/equipmentsbystyle")
    public ResponseEntity<ResponseResult> getEquipmentsByStyle(Pageable pageable,String resourceStructureIds, String keywords,String styleName) {
        return ResponseHelper.successful(equipmentLedgerService.getAllAssetLedgerPaged(pageable,resourceStructureIds,keywords,styleName));
    }
    @ApiOperation("设备状态查询统计")
    @GetMapping(value = "/statusstatistics")
    public ResponseEntity<ResponseResult> getStatusStatistics() {
        return ResponseHelper.successful(equipmentLedgerService.statusStatistics());
    }
    @ApiOperation("位置对应设备状态列表")
    @GetMapping(value = "/statusbyposition")
    public ResponseEntity<ResponseResult> getStatusListByResourceStructure(Pageable pageable,Integer resourceStructureId, Integer alarmState, Integer conState,Integer equipmentCategory,String keywords) {
        return ResponseHelper.successful(equipmentLedgerService.getStatusListByResourceStructure(pageable,resourceStructureId,alarmState,conState,equipmentCategory,keywords));
    }
    @ApiOperation(value = "导出设备状态")
    @GetMapping(value = "/statusbyposition/excel")
    public ResponseEntity<Resource> exportEquStatus(Integer resourceStructureId, Integer alarmState, Integer conState,Integer equipmentCategory,String keywords) {
        String fileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        try (ExcelWriter writer = equipmentLedgerService.exportEquStatus(resourceStructureId,alarmState,conState,equipmentCategory,keywords);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            writer.flush(bos, true);
            Resource resource = new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ".xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);
        } catch (Exception e) {
            log.error("Export xlsx error: {}", ExceptionUtil.stacktraceToString(e));
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    @ApiOperation(value = "点位统计")
    @GetMapping(value = "/signalpointstatistics")
    public ResponseEntity<ResponseResult> getSignalpointStatistics() {
        return ResponseHelper.successful(equipmentLedgerService.getSignalpointStatistics());
    }
    @ApiOperation(value = "资产总数")
    @GetMapping(value = "/assetdevicesum")
    public ResponseEntity<ResponseResult> getAssetDeviceSum() {
        return ResponseHelper.successful(equipmentLedgerService.getAssetDeviceSum());
    }

    @ApiOperation(value = "设备模板点位详情")
    @GetMapping(value = "/getsignalpoint/{templateId}")
    public ResponseEntity<ResponseResult> getSingalSignalPoint(@PathVariable("templateId") Integer templateId) {
        return ResponseHelper.successful(equipmentLedgerService.getSingleFullPoint(templateId));
    }

    @ApiOperation(value = "导出全量点表")
    @PostMapping(value = "/exportsignalpoint")
    public ResponseEntity<Resource>  exportSignalPoint(@RequestBody List<Integer> templateIds) {
        String fileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        try (ExcelWriter writer = equipmentLedgerService.exportSignalPoint(templateIds);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            writer.flush(bos, true);
            Resource resource = new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ".xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);
        } catch (Exception e) {
            log.error("Export xlsx error: {}", ExceptionUtil.stacktraceToString(e));
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}
