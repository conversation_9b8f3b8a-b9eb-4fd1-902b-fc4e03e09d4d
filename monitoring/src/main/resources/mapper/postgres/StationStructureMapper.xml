<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.StationStructureMapper">
    <select id="findByStationId" resultType="com.siteweb.monitoring.entity.StationStructure">
        SELECT structure.StructureId,
               structure.StructureName,
               structure.StructureGroupId,
               structure.ParentStructureId,
               structure.IsUngroup,
               structure.StructureType,
               structure.MapZoom,
               structure.Longitude,
               structure.Latitude,
               structure.Description,
               structure.LevelPath,
               structure.Enable
        FROM TBL_StationStructureMap map,
             TBL_StationStructure structure
        WHERE map.StationId = #{stationId}
          AND map.StructureId = structure.StructureId
          AND structure.StructureGroupId = 1;
    </select>
    <select id="findStationStructureIdLabel" resultType="com.siteweb.utility.dto.IdValueDTO">
        SELECT map.StationId as value,structure.StructureName as label
        FROM TBL_StationStructureMap map
        INNER JOIN TBL_StationStructure structure ON map.StructureId = structure.StructureId
        WHERE map.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        AND structure.StructureGroupId = 1;
    </select>
    <select id="findStationStructures" resultType="com.siteweb.monitoring.entity.StationStructure">
        SELECT map.stationid,structure.*
        FROM tbl_stationstructuremap map
        JOIN tbl_stationstructure structure ON map.structureid = structure.structureid
        WHERE structure.structuregroupid = #{groupid}
        <if test="stationids != null and stationids.size() > 0">
            AND map.stationid IN
            <foreach collection="stationids" item="stationid" open="(" close=")" separator=",">
                #{stationid}
            </foreach>
        </if>
    </select>
</mapper>