package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.vo.FsuFilterVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface FsuMapper extends BaseMapper<IcsFsuDataNewInfoDTO> {
    @MapKey("FsuType")
    List<Map<String, String>> findFsuTypeList();

    List<IcsFsuDataNewInfoDTO> findAllFsu(@Param("stationIdList") Collection<Integer> stationIdList, @Param("filterVo") FsuFilterVo filterVo);

    List<IdValueDTO<String, Integer>> findFsuTypeStatistics(@Param("stationIdSet") Set<Integer> stationIdSet);

    List<IdValueDTO<String, Integer>> findSiteUnitStatistics(@Param("stationIdSet") Set<Integer> stationIdSet);

    List<IdValueDTO<String, Integer>> findFsuFlashUsedStatistics(@Param("stationIdSet") Set<Integer> stationIdSet);

    Page<IcsFsuDataNewInfoDTO> findFsuPage(@Param("page") Page<IcsFsuDataNewInfoDTO> page, @Param("stationIdList") Collection<Integer> stationIdList, @Param("filterVo") FsuFilterVo filterVo);

    List<IdValueDTO<String, Integer>> findFsuVersionStatusStatistics(@Param("stationIdSet") Set<Integer> stationIdSet);
}

