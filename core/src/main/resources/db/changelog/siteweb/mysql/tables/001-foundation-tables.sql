CREATE TABLE `tbl_account` (
  `UserId` int NOT NULL,
  `UserName` varchar(128) NOT NULL,
  `LogonId` varchar(20) NOT NULL,
  `Password` varchar(128) DEFAULT NULL,
  `Enable` bit(1) NOT NULL DEFAULT b'1',
  `MaxError` int DEFAULT NULL,
  `Locked` bit(1) NOT NULL DEFAULT b'0',
  `ValidTime` datetime DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `IsRemote` bit(1) NOT NULL DEFAULT b'0',
  `CenterId` int DEFAULT NULL,
  `PasswordValidTime` datetime DEFAULT NULL,
  `ThemeName` varchar(128) DEFAULT NULL COMMENT '网站主题名称',
  `NeedResetPwd` tinyint(1) NOT NULL DEFAULT '0',
  `Avatar` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`UserId`)
);
-- 重庆电信登录开关控制表，仅在重庆电信开关开启时使用，放在这里归档
-- CREATE TABLE `cqctcc_login` (
--  `LogonId` varchar(20) NOT NULL,
--  `Enable` bit(1) NOT NULL DEFAULT b'0',
--  PRIMARY KEY (`LogonId`)
-- ) COMMENT='重庆电信登录开关控制，开关开启的才可用s6原生登录';

CREATE TABLE `tbl_activecontrol` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `ControlId` int NOT NULL,
  `ControlName` varchar(128) NOT NULL,
  `SerialNo` int NOT NULL AUTO_INCREMENT,
  `ControlSeverity` int NOT NULL,
  `CmdToken` text NOT NULL,
  `ControlPhase` int NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(255) DEFAULT NULL,
  `ControlResultType` int DEFAULT NULL,
  `ControlResult` varchar(255) DEFAULT NULL,
  `ControlExecuterId` int DEFAULT NULL,
  `ControlExecuterIdName` varchar(255) DEFAULT NULL,
  `ControlType` int DEFAULT NULL,
  `ActionId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Retry` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `ParameterValues` text NOT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  PRIMARY KEY (`ControlId`,`EquipmentId`,`SerialNo`,`StartTime`,`StationId`),
  KEY `TBL_ActiveControl_IDX1` (`SerialNo`),
  KEY `IDX_ActiceControl_2` (`StationId`,`EquipmentId`,`ControlId`,`StartTime`)
);

CREATE TABLE `tbl_activeevent` (
  `SequenceId` varchar(128) NOT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `EventId` int NOT NULL,
  `EventName` varchar(128) NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventSeverityId` int NOT NULL,
  `EventSeverity` varchar(128) NOT NULL,
  `EventLevel` int DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `CancelTime` datetime DEFAULT NULL,
  `CancelUserId` int DEFAULT NULL,
  `CancelUserName` varchar(128) DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(128) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `ReversalNum` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EventFilePath` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SourceHostId` int DEFAULT NULL,
  `InstructionId` varchar(255) DEFAULT NULL,
  `InstructionStatus` int DEFAULT NULL,
  `StandardAlarmNameId` int DEFAULT NULL,
  `StandardAlarmName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EquipmentCategory` int NOT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `MaintainState` int NOT NULL DEFAULT '0',
  `SignalId` int DEFAULT NULL,
  `RelateSequenceId` varchar(128) DEFAULT NULL,
  `EventCategoryId` int NOT NULL,
  `EventStateId` int NOT NULL,
  `CenterId` int DEFAULT NULL,
  `CenterName` varchar(128) DEFAULT NULL,
  `StructureName` varchar(128) DEFAULT NULL,
  `MonitorUnitName` varchar(128) DEFAULT NULL,
  `StructureId` int DEFAULT NULL,
  `StationCategoryId` int DEFAULT NULL,
  `EquipmentVendor` varchar(128) DEFAULT NULL,
  `ConvergenceEventId` bigint DEFAULT '0',
  `ResourceStructureId` int DEFAULT '0',
  `BaseEquipmentId` int DEFAULT '0',
  PRIMARY KEY (`SequenceId`),
  UNIQUE KEY `TBL_ActiveEvent_ID1` (`SequenceId`)
);

-- 告警自动确认需要使用
CREATE INDEX IDX_EventSeverityId_EndTime ON TBL_ActiveEvent(EventSeverityId,EndTime);
-- 告警收敛使用
CREATE INDEX IDX_EquipmentId_ConvergenceEventId_StartTime ON TBL_ActiveEvent (EquipmentId, ConvergenceEventId, StartTime);

CREATE TABLE `tbl_activesignal` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(128) DEFAULT NULL,
  `SignalCategory` int DEFAULT NULL,
  `DataType` int NOT NULL,
  `FloatValue` double DEFAULT NULL,
  `StringValue` varchar(128) DEFAULT NULL,
  `DateTimeValue` datetime DEFAULT NULL,
  `SampleTime` datetime NOT NULL,
  `ShowPrecision` varchar(20) DEFAULT NULL,
  `Unit` varchar(64) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `EventSeverityName` varchar(128) DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `Flag` int DEFAULT NULL,
  `LastUpdate` datetime NOT NULL,
  PRIMARY KEY (`EquipmentId`,`LastUpdate`,`SignalId`,`StationId`)
);

CREATE TABLE `tbl_alarmchange` (
  `SequenceId` varchar(128) NOT NULL,
  `SerialNo` bigint NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `OperationType` int NOT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `EventId` int NOT NULL,
  `EventName` varchar(128) NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventSeverityId` int NOT NULL,
  `EventSeverity` varchar(128) DEFAULT NULL,
  `EventLevel` int DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `CancelTime` datetime DEFAULT NULL,
  `CancelUserId` int DEFAULT NULL,
  `CancelUserName` varchar(128) DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(128) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `ReversalNum` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EventFilePath` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SourceHostId` int DEFAULT NULL,
  `InstructionId` varchar(255) DEFAULT NULL,
  `InstructionStatus` int DEFAULT NULL,
  `StandardAlarmNameId` int DEFAULT NULL,
  `StandardAlarmName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EquipmentCategory` int NOT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `MaintainState` int NOT NULL DEFAULT '0',
  `SignalId` int DEFAULT NULL,
  `RelateSequenceId` varchar(128) DEFAULT NULL,
  `EventCategoryId` int NOT NULL,
  `EventStateId` int NOT NULL,
  `CenterId` int DEFAULT NULL,
  `CenterName` varchar(128) DEFAULT NULL,
  `StructureName` varchar(128) DEFAULT NULL,
  `MonitorUnitName` varchar(128) DEFAULT NULL,
  `StructureId` int DEFAULT NULL,
  `StationCategoryId` int DEFAULT NULL,
  `EquipmentVendor` varchar(128) DEFAULT NULL,
  `ConvergenceEventId` bigint DEFAULT '0',
  `ResourceStructureId` int DEFAULT '0',
  `BaseEquipmentId` int DEFAULT '0',
  `InsertTime` datetime DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `TBL_AlarmChange_ID2` (`SerialNo`),
  KEY `TBL_AlarmChange_ID1` (`SequenceId`,`OperationType`)
);

CREATE TABLE `tbl_area` (
  `AreaId` int NOT NULL,
  `AreaName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`AreaId`)
);

CREATE TABLE `tbl_areamap` (
  `StationId` int NOT NULL,
  `AreaId` int NOT NULL,
  PRIMARY KEY (`AreaId`,`StationId`)
);

CREATE TABLE `tbl_backupandcleartime` (
  `BackupAndClearTimeId` int PRIMARY KEY AUTO_INCREMENT,
  `DataType` int DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_configchangedefine` (
  `ConfigId` int NOT NULL PRIMARY KEY ,
  `EntityName` varchar(255) NOT NULL,
  `TableName` varchar(255) NOT NULL,
  `IdDefine` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_configchangemacrolog` (
  `ObjectId` varchar(255) NOT NULL,
  `ConfigId` int NOT NULL,
  `EditType` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  KEY `IDX_configchangemacrolog_1` (`UpdateTime` DESC,`ConfigId`),
  PRIMARY KEY `IDX_configchangemacrolog_2` (`ObjectId`,`ConfigId`,`EditType`)
);

CREATE TABLE `tbl_configchangemap` (
  `MicroConfigId` int NOT NULL,
  `MicroEditType` int NOT NULL,
  `MacroConfigId` int NOT NULL,
  `MacroEditType` int NOT NULL,
  `IdConvertRule` varchar(255) DEFAULT NULL,
  PRIMARY KEY `IDX_configchangemacrolog_2` (`MicroConfigId`,`MicroEditType`)
);

CREATE TABLE `tbl_configchangemicrolog` (
  `ObjectId` varchar(255) NOT NULL,
  `ConfigId` int NOT NULL,
  `EditType` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  PRIMARY KEY `IDX_configchangemicrolog_1` (`ObjectId`,`ConfigId`,`EditType`)
);

CREATE TABLE `tbl_configchecktask` (
  `ObjectType` varchar(64) NOT NULL,
  `ObjectId` varchar(128) NOT NULL,
  `Priority` int NOT NULL,
  `CheckTime` datetime NOT NULL,
  `BugType` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`BugType`,`ObjectId`)
);

CREATE TABLE `tbl_control` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `ControlId` int NOT NULL,
  `ControlName` varchar(128) NOT NULL,
  `ControlCategory` int NOT NULL,
  `CmdToken` text NOT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `ControlSeverity` int NOT NULL,
  `SignalId` int DEFAULT NULL,
  `TimeOut` double DEFAULT NULL,
  `Retry` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Enable` tinyint(1) NOT NULL,
  `Visible` tinyint(1) NOT NULL,
  `DisplayIndex` int NOT NULL,
  `CommandType` int NOT NULL,
  `ControlType` smallint DEFAULT NULL,
  `DataType` smallint DEFAULT NULL,
  `MaxValue` double NOT NULL,
  `MinValue` double NOT NULL,
  `DefaultValue` double DEFAULT NULL,
  `ModuleNo` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_TBLControl_1` (`EquipmentTemplateId`),
  KEY `IDX_TBLControl_2` (`EquipmentTemplateId`,`ControlId`)
);

CREATE TABLE `tbl_controllogaction` (
  `LogActionId` int NOT NULL,
  `ActionId` int NOT NULL,
  `ActionName` varchar(50) DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `ControlId` int DEFAULT NULL,
  `ActionValue` varchar(255) DEFAULT NULL,
   PRIMARY KEY `PK_LogActionId_ActionId` (LogActionId,ActionId)
);

CREATE TABLE `tbl_controlmeanings` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `ControlId` int NOT NULL,
  `ParameterValue` smallint NOT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_TBLControlMeanings_1` (`EquipmentTemplateId`,`ControlId`)
);

CREATE TABLE `tbl_dataentry` (
  `EntryId` int NOT NULL,
  `EntryCategory` int DEFAULT NULL,
  `EntryName` varchar(128) DEFAULT NULL,
  `EntryTitle` varchar(128) DEFAULT NULL,
  `EntryAlias` varchar(255) DEFAULT NULL,
  `Enable` tinyint(1) NOT NULL DEFAULT '1',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryId`),
  UNIQUE KEY `EntryId` (`EntryId`)
);

CREATE TABLE `tbl_dataitem` (
  `EntryItemId` int NOT NULL,
  `ParentEntryId` int NOT NULL DEFAULT '0',
  `ParentItemId` int NOT NULL DEFAULT '0',
  `EntryId` int NOT NULL,
  `ItemId` int NOT NULL,
  `ItemValue` varchar(128) NOT NULL,
  `ItemAlias` varchar(255) DEFAULT NULL,
  `Enable` tinyint(1) NOT NULL DEFAULT '1',
  `IsSystem` tinyint(1) NOT NULL DEFAULT '1',
  `IsDefault` tinyint(1) NOT NULL DEFAULT '0',
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  `ExtendField5` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryItemId`),
  UNIQUE KEY `EntryItemId` (`EntryItemId`),
  UNIQUE KEY `tbl_dataitem_Idx1` (`EntryId`,`ItemId`),
  KEY `IDX_DataItem_EntryId` (`EntryId`)
);

CREATE TABLE `tbl_datastruct` (
  `EntryId` int NOT NULL,
  `StructType` int DEFAULT NULL,
  `StructName` varchar(128) DEFAULT NULL,
  `StructDescription` varchar(256) DEFAULT NULL,
  `AttributeId` int DEFAULT NULL,
  `AttributeName` varchar(128) DEFAULT NULL,
  `AttributeType` varchar(128) DEFAULT NULL,
  `TypeDefine` varchar(256) DEFAULT NULL,
  `Description` varchar(256) DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`EntryId`)
);

CREATE TABLE `tbl_dbversionrecord` (
  `SerialNo` int NOT NULL AUTO_INCREMENT,
  `UpdateTime` datetime NOT NULL,
  `Version` varchar(30) NOT NULL,
  `Module` varchar(255) DEFAULT NULL,
  `LastModifyTime` datetime NOT NULL,
  `Feature` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialNo`)
);

CREATE TABLE `tbl_department` (
  `DepartmentId` int NOT NULL,
  `DepartmentName` varchar(128) NOT NULL,
  `DepartmentLevel` varchar(20) DEFAULT NULL,
  `DepartmentFunction` varchar(40) DEFAULT NULL,
  `ParentDeprtId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdateDate` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`DepartmentId`)
);

CREATE TABLE `tbl_dynamicconfig` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL,
  `ConfigTime` datetime NOT NULL,
  `StationId` int NOT NULL,
  `HostId` int NOT NULL,
  `SyncFlag` int NOT NULL DEFAULT '-2',
  `SyncXml` text NOT NULL,
  `SyncTime` datetime DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `IDX_DynamicConfig_1` (`ConfigTime`,`HostId`)
);

CREATE TABLE `tbl_employee` (
  `EmployeeId` int NOT NULL,
  `DepartmentId` int DEFAULT NULL,
  `EmployeeName` varchar(128) NOT NULL,
  `EmployeeType` int DEFAULT NULL,
  `EmployeeTitle` int DEFAULT NULL,
  `JobNumber` varchar(20) NOT NULL,
  `Gender` int DEFAULT NULL,
  `Mobile` varchar(50) DEFAULT NULL,
  `Phone` varchar(50) DEFAULT NULL,
  `Email` varchar(128) DEFAULT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `PostAddress` varchar(255) DEFAULT NULL,
  `Enable` bit(1) NOT NULL DEFAULT b'1',
  `Description` varchar(255) DEFAULT NULL,
  `IsAddTempUser` bit(1) NOT NULL DEFAULT b'0',
  `UserValidTime` int DEFAULT '172800',
  PRIMARY KEY (`EmployeeId`),
  KEY `DepartmentId` (`DepartmentId`)
);

CREATE TABLE `tbl_enumdata` (
  `EntryId` int NOT NULL,
  `AttributeType` int DEFAULT NULL,
  `AttributeName` varchar(128) DEFAULT NULL,
  `AttributeDescription` varchar(256) DEFAULT NULL,
  `EnumId` int DEFAULT NULL,
  `EnumType` varchar(128) DEFAULT NULL,
  `EnumValue` int DEFAULT NULL,
  `EnumDefine` varchar(256) DEFAULT NULL,
  `Description` varchar(256) DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`EntryId`)
);

CREATE TABLE `tbl_equipment` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `EquipmentNo` varchar(128) NOT NULL,
  `EquipmentModule` varchar(128) DEFAULT NULL,
  `EquipmentStyle` varchar(128) DEFAULT NULL,
  `AssetState` int DEFAULT NULL,
  `Price` double DEFAULT NULL,
  `UsedLimit` double DEFAULT NULL,
  `UsedDate` datetime DEFAULT NULL,
  `BuyDate` datetime DEFAULT NULL,
  `Vendor` varchar(255) DEFAULT NULL,
  `Unit` varchar(255) DEFAULT NULL,
  `EquipmentCategory` int NOT NULL,
  `EquipmentType` int NOT NULL,
  `EquipmentClass` int DEFAULT NULL,
  `EquipmentState` int NOT NULL,
  `EventExpression` varchar(255) DEFAULT NULL,
  `StartDelay` double DEFAULT NULL,
  `EndDelay` double DEFAULT NULL,
  `Property` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `EquipmentTemplateId` int DEFAULT NULL,
  `HouseId` int DEFAULT NULL,
  `MonitorUnitId` int NOT NULL,
  `WorkStationId` int DEFAULT NULL,
  `SamplerUnitId` int NOT NULL,
  `DisplayIndex` int NOT NULL,
  `ConnectState` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `ParentEquipmentId` varchar(255) DEFAULT NULL,
  `RatedCapacity` varchar(255) DEFAULT NULL,
  `InstalledModule` text NOT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `InstallTime` datetime DEFAULT NULL,
  `EquipmentSN` varchar(255) DEFAULT NULL,
  `SO` varchar(255) DEFAULT NULL,
  `ResourceStructureId` int DEFAULT '0',
  `ExtValue` json DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`),
  UNIQUE KEY `EquipmentId` (`EquipmentId`),
  KEY `IDC_EquipmentId_MonitorUnit_ID` (`MonitorUnitId`),
  KEY `IDX_EquipmentId_1` (`MonitorUnitId`,`SamplerUnitId`),
  KEY `IDX_Equipment_ResourceStructureId` (`ResourceStructureId`),
  KEY `IDX_Equipment_StationId` (`StationId`,`HouseId`),
  KEY `IDX_EquipmentTemplateId` (`EquipmentTemplateId`)
);

CREATE TABLE `tbl_equipmentidmap` (
  `EquipmentIdMapId` int PRIMARY KEY AUTO_INCREMENT,
  `OldStationId` int DEFAULT NULL,
  `OldEquipmentId` int DEFAULT NULL,
  `KoloStationId` int DEFAULT NULL,
  `KoloEquipmentId` int DEFAULT NULL,
  `OPostCode` int DEFAULT NULL
);

CREATE TABLE `tbl_equipmentkeyvalue` (
  `EquipmentType` int NOT NULL,
  `EquipmentCategory` int NOT NULL,
  `MinValue` int DEFAULT NULL,
  `CurrentValue` int DEFAULT NULL,
  PRIMARY KEY (`EquipmentType`,`EquipmentCategory`)
);

CREATE TABLE `tbl_equipmentmaintain` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentState` int DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendFiled1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`),
  KEY `IDX_EquipmentMaintain_ID` (`StationId`,`EquipmentId`)
);

CREATE TABLE `tbl_equipmentmask` (
  `EquipmentId` int NOT NULL,
  `StationId` int NOT NULL,
  `TimeGroupId` int DEFAULT NULL,
  `Reason` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentprojectinfo` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `InstallTime` datetime DEFAULT NULL,
  `EquipmentSN` varchar(255) DEFAULT NULL,
  `SO` varchar(255) DEFAULT NULL,
   PRIMARY KEY `TBL_EquipmentProjectInfo_IDX1` (`StationId`,`EquipmentId`)
);

CREATE TABLE `tbl_equipmenttemplate` (
  `EquipmentTemplateId` int NOT NULL,
  `EquipmentTemplateName` varchar(128) NOT NULL,
  `ParentTemplateId` int NOT NULL,
  `Memo` varchar(255) NOT NULL,
  `ProtocolCode` varchar(32) NOT NULL,
  `EquipmentCategory` int NOT NULL,
  `EquipmentType` int NOT NULL,
  `Property` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `EquipmentStyle` varchar(128) DEFAULT NULL,
  `Unit` varchar(255) DEFAULT NULL,
  `Vendor` varchar(255) DEFAULT NULL,
  `Photo` varchar(255) DEFAULT NULL,
  `EquipmentBaseType` int DEFAULT NULL,
  `StationCategory` int DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `SecondaryCategory` int DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`),
  UNIQUE KEY `EquipmentTemplateId` (`EquipmentTemplateId`)
);

CREATE TABLE `tbl_equipmenttemplateidmap` (
  `EquipmentTemplateIdMapId` int PRIMARY KEY AUTO_INCREMENT,
  `OldEquipmentTemplateId` int DEFAULT NULL,
  `KoloEquipmentTemplateId` int DEFAULT NULL
);

CREATE TABLE `tbl_equiptemplatebaseconfirm` (
  `EquipmentTemplateId` int NOT NULL,
  `ConfirmTime` datetime NOT NULL,
  `ConfirmUser` int NOT NULL,
  `Reason` text,
  PRIMARY KEY (`EquipmentTemplateId`)
);

CREATE TABLE `tbl_event` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventName` varchar(128) NOT NULL,
  `StartType` int NOT NULL,
  `EndType` int NOT NULL,
  `StartExpression` text,
  `SuppressExpression` text,
  `EventCategory` int NOT NULL,
  `SignalId` int DEFAULT NULL,
  `Enable` tinyint(1) NOT NULL,
  `Visible` tinyint(1) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `DisplayIndex` int DEFAULT NULL,
  `ModuleNo` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_TBLEvent_1` (`EquipmentTemplateId`),
  KEY `IDX_TBLEvent_2` (`EquipmentTemplateId`,`EventId`),
  KEY `IDX_Event_Signal_1` (`EquipmentTemplateId`,`SignalId`)
);

CREATE TABLE `tbl_eventcondition` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EventConditionId` int NOT NULL,
  `EquipmentTemplateId` int NOT NULL,
  `EventId` int NOT NULL,
  `StartOperation` varchar(4) NOT NULL,
  `StartCompareValue` double NOT NULL,
  `StartDelay` int NOT NULL,
  `EndOperation` varchar(4) DEFAULT NULL,
  `EndCompareValue` double DEFAULT NULL,
  `EndDelay` int DEFAULT NULL,
  `Frequency` int DEFAULT NULL,
  `FrequencyThreshold` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EquipmentState` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `EventSeverity` int NOT NULL,
  `StandardName` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_TBLEventCondition_1` (`EquipmentTemplateId`,`EventId`)
);

CREATE TABLE `tbl_eventex` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `EventId` int NOT NULL,
  `turnover` int DEFAULT NULL,
  `ExtendField1` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_TBLEventex_1` (`EquipmentTemplateId`),
  KEY `IDX_TBLEventex_2` (`EquipmentTemplateId`,`EventId`)
);

CREATE TABLE `tbl_eventlogaction` (
  `LogActionId` int NOT NULL PRIMARY KEY,
  `ActionName` varchar(255) NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `TriggerType` int NOT NULL,
  `StartExpression` varchar(255) DEFAULT NULL,
  `SuppressExpression` varchar(255) DEFAULT NULL,
  `InformMsg` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_eventmask` (
  `EquipmentId` int NOT NULL,
  `StationId` int NOT NULL,
  `EventId` int NOT NULL,
  `TimeGroupId` int DEFAULT NULL,
  `Reason` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`EventId`,`StationId`)
);

CREATE TABLE `tbl_eventmaskhistory` (
  `SequenceId` varchar(128) NOT NULL PRIMARY KEY,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_historycontrol` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `ControlId` int NOT NULL,
  `ControlName` varchar(128) NOT NULL,
  `SerialNo` int NOT NULL,
  `ControlSeverity` int NOT NULL,
  `CmdToken` text NOT NULL,
  `ControlPhase` int NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(255) DEFAULT NULL,
  `ControlResultType` int DEFAULT NULL,
  `ControlResult` varchar(255) DEFAULT NULL,
  `ControlExecuterId` int DEFAULT NULL,
  `ControlExecuterIdName` varchar(255) DEFAULT NULL,
  `ControlType` int DEFAULT NULL,
  `ActionId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Retry` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `ParameterValues` text NOT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  PRIMARY KEY `TBL_HistoryControl_IDX1` (`StartTime`,`StationId`,`EquipmentId`,`ControlId`,`SerialNo`)
);

CREATE TABLE `tbl_historycontrolmid` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `ControlId` int NOT NULL,
  `ControlName` varchar(128) NOT NULL,
  `SerialNo` int NOT NULL,
  `ControlSeverity` int NOT NULL,
  `CmdToken` text NOT NULL,
  `ControlPhase` int NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(255) DEFAULT NULL,
  `ControlResultType` int DEFAULT NULL,
  `ControlResult` varchar(255) DEFAULT NULL,
  `ControlExecuterId` int DEFAULT NULL,
  `ControlExecuterIdName` varchar(255) DEFAULT NULL,
  `ControlType` int DEFAULT NULL,
  `ActionId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Retry` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `ParameterValues` text NOT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  PRIMARY KEY `TBL_HistoryControlMid_IDX1` (`StartTime`,`StationId`,`EquipmentId`,`ControlId`,`SerialNo`)
);

CREATE TABLE `tbl_historyevent` (
  `SequenceId` varchar(128) NOT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `EventId` int NOT NULL,
  `EventName` varchar(128) NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventSeverityId` int NOT NULL,
  `EventSeverity` varchar(128) NOT NULL,
  `EventLevel` int DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL,
  `CancelTime` datetime DEFAULT NULL,
  `CancelUserId` int DEFAULT NULL,
  `CancelUserName` varchar(128) DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(128) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `ReversalNum` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EventFilePath` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SourceHostId` int DEFAULT NULL,
  `InstructionId` varchar(255) DEFAULT NULL,
  `InstructionStatus` int DEFAULT NULL,
  `StandardAlarmNameId` int DEFAULT NULL,
  `StandardAlarmName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EquipmentCategory` int DEFAULT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `MaintainState` int NOT NULL DEFAULT '0',
  `SignalId` int DEFAULT NULL,
  `RelateSequenceId` varchar(128) DEFAULT NULL,
  `EventCategoryId` int DEFAULT NULL,
  `EventStateId` int DEFAULT NULL,
  `CenterId` int DEFAULT NULL,
  `CenterName` varchar(128) DEFAULT NULL,
  `StructureName` varchar(128) DEFAULT NULL,
  `MonitorUnitName` varchar(128) DEFAULT NULL,
  `StructureId` int DEFAULT NULL,
  `StationCategoryId` int DEFAULT NULL,
  `EquipmentVendor` varchar(128) DEFAULT NULL,
  `ConvergenceEventId` bigint DEFAULT '0',
  `ResourceStructureId` int DEFAULT '0',
  `BaseEquipmentId` int DEFAULT '0',
  PRIMARY KEY `TBL_HistoryEvent_IDX1` (`StartTime`,`StationId`,`EquipmentId`,`EventId`,`EventConditionId`),
  KEY `TBL_HistoryEvent_IDX2` (`StructureId`,`StationCategoryId`,`EquipmentCategory`,`StationId`,`EventStateId`,`EventCategoryId`,`EventConditionId`,`StartTime`),
  KEY `TBL_HistoryEvent_IDX3` (`StartTime`,`BaseTypeId`,`StationId`,`EquipmentId`,`EventSeverityId`,`SignalId`)
);

CREATE TABLE `tbl_historyeventmask` (
  `SequenceId` varchar(128) NOT NULL PRIMARY KEY,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL
);

CREATE TABLE `tbl_historyeventmaskmid` (
  `SequenceId` varchar(128) NOT NULL PRIMARY KEY,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL
);

CREATE TABLE `tbl_historyeventmid` (
  `SequenceId` varchar(128) NOT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `EventId` int NOT NULL,
  `EventName` varchar(128) NOT NULL,
  `EventConditionId` int NOT NULL,
  `EventSeverityId` int NOT NULL,
  `EventSeverity` varchar(128) NOT NULL,
  `EventLevel` int DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL,
  `CancelTime` datetime DEFAULT NULL,
  `CancelUserId` int DEFAULT NULL,
  `CancelUserName` varchar(128) DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(128) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `ReversalNum` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EventFilePath` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SourceHostId` int DEFAULT NULL,
  `InstructionId` varchar(255) DEFAULT NULL,
  `InstructionStatus` int DEFAULT NULL,
  `StandardAlarmNameId` int DEFAULT NULL,
  `StandardAlarmName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EquipmentCategory` int DEFAULT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `MaintainState` int NOT NULL DEFAULT '0',
  `SignalId` int DEFAULT NULL,
  `RelateSequenceId` varchar(128) DEFAULT NULL,
  `EventCategoryId` int DEFAULT NULL,
  `EventStateId` int DEFAULT NULL,
  `CenterId` int DEFAULT NULL,
  `CenterName` varchar(128) DEFAULT NULL,
  `StructureName` varchar(128) DEFAULT NULL,
  `MonitorUnitName` varchar(128) DEFAULT NULL,
  `StructureId` int DEFAULT NULL,
  `StationCategoryId` int DEFAULT NULL,
  `EquipmentVendor` varchar(128) DEFAULT NULL,
  `ConvergenceEventId` bigint DEFAULT '0',
  `ResourceStructureId` int DEFAULT '0',
  `BaseEquipmentId` int DEFAULT '0',
  PRIMARY KEY `TBL_HistoryEventMid_IDX1` (`StartTime`,`StationId`,`EquipmentId`,`EventId`,`EventConditionId`)
);

CREATE TABLE `tbl_historypassword` (
  `UserId` int NOT NULL,
  `Password` varchar(128) DEFAULT NULL,
  `RecordTime` datetime NOT NULL,
  PRIMARY KEY (`RecordTime`,`UserId`)
);

CREATE TABLE `tbl_historyselection` (
  `HistorySelectionId` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL,
  `SelectionType` varchar(128) NOT NULL,
  `SelectionName` varchar(255) NOT NULL,
  `SelectionContent` longtext NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `QueryInformation` longtext,
  PRIMARY KEY (`HistorySelectionId`)
);

CREATE TABLE `tbl_hourlysignal` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `RecordTime` datetime NOT NULL,
  `DataType` int DEFAULT NULL,
  `FloatValue` double DEFAULT NULL,
  `StringValue` varchar(255) DEFAULT NULL,
  `ReportTime` datetime NOT NULL,
  `SignalPropertyId` int NOT NULL DEFAULT '0',
  PRIMARY KEY `TBL_HourlySignal_IDX1` (`RecordTime`,`StationId`,`EquipmentId`,`SignalId`)
);

CREATE TABLE `tbl_house` (
  `HouseId` int NOT NULL,
  `StationId` int NOT NULL,
  `HouseName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdateDate` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`HouseId`,`StationId`)
);

CREATE TABLE `tbl_logicclassentry` (
  `EntryId` int NOT NULL,
  `EntryCategory` int DEFAULT NULL,
  `LogicClassId` int DEFAULT NULL,
  `LogicClass` varchar(128) DEFAULT NULL,
  `StandardType` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryId`,`StandardType`)
);

CREATE TABLE `tbl_loginformlist` (
  `LoginFormListId` int PRIMARY KEY AUTO_INCREMENT,
  `LogActionId` int NOT NULL,
  `InformerId` int NOT NULL,
  `UserId` int DEFAULT NULL,
  `InfoType` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_logininfo` (
  `UserId` int NOT NULL,
  `LoginType` int NOT NULL,
  `LoginTime` datetime NOT NULL,
  `IPAddress` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LoginTime`,`LoginType`,`UserId`)
);

CREATE TABLE `tbl_logininfomid` (
  `UserId` int NOT NULL,
  `LoginType` int NOT NULL,
  `LoginTime` datetime NOT NULL,
  `IPAddress` varchar(255) DEFAULT NULL,
   PRIMARY KEY (`UserId`, `LoginType`, `LoginTime` )
);

CREATE TABLE `tbl_middletbl` (
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `SerialId` int NOT NULL,
  `BusinessTypeName` varchar(255) NOT NULL,
  `ExpressionName` varchar(255) NOT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(128) NOT NULL,
  `SignalCategory` int NOT NULL,
  `DataType` int NOT NULL,
  `FloatValue` double DEFAULT NULL,
  `StringValue` varchar(128) DEFAULT NULL,
  `DateTimeValue` datetime DEFAULT NULL,
  `SampleTime` datetime NOT NULL,
  `ShowPrecision` varchar(20) DEFAULT NULL,
  `Unit` varchar(64) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `EventSeverityName` varchar(128) DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `ThresholdType` int DEFAULT NULL,
  `BusinessState` int DEFAULT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  `BaseMeaning` varchar(255) DEFAULT NULL,
  PRIMARY KEY `TBL_MiddleTbl_IDX1` (`ExpressionId`,`SampleTime`,`StationId`,`EquipmentId`,`SignalId`),
  KEY `TBL_MiddleTbl_IDX2` (`BusinessTypeId`,`ExpressionId`,`StationId`,`EquipmentId`,`SignalId`,`SampleTime`,`FloatValue`)
);

CREATE TABLE `tbl_midxxxxmid` (
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `SerialId` int NOT NULL,
  `BusinessTypeName` varchar(255) NOT NULL,
  `ExpressionName` varchar(255) NOT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(128) NOT NULL,
  `SignalCategory` int NOT NULL,
  `DataType` int NOT NULL,
  `FloatValue` double DEFAULT NULL,
  `StringValue` varchar(128) DEFAULT NULL,
  `DateTimeValue` datetime DEFAULT NULL,
  `SampleTime` datetime NOT NULL,
  `ShowPrecision` varchar(20) DEFAULT NULL,
  `Unit` varchar(64) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `EventSeverityName` varchar(128) DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `ThresholdType` int DEFAULT NULL,
  `BusinessState` int DEFAULT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  `BaseMeaning` varchar(255) DEFAULT NULL,
  `ComeFromTableName` varchar(64) NOT NULL,
  PRIMARY KEY `TBL_MidXxxxMid_IDX1` (`ExpressionId`,`SampleTime`,`StationId`,`EquipmentId`,`SignalId`)
);

CREATE TABLE `tbl_monitorunitprojectinfo` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `InstallTime` datetime DEFAULT NULL,
  PRIMARY KEY `TBL_MUProjectInfo_IDX1` (`StationId`,`MonitorUnitId`)
);

CREATE TABLE `tbl_mufullcfgstate` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `ConfigFileCode` varchar(32) NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `State` int NOT NULL,
  PRIMARY KEY (`MonitorUnitId`,`StationId`)
);

CREATE TABLE `tbl_musyncrecord` (
  `RecordId` int NOT NULL AUTO_INCREMENT,
  `StationId` int NOT NULL,
  `TaskId` int DEFAULT NULL,
  `MonitorUnitId` int NOT NULL,
  `SyncResult` int NOT NULL,
  `SyncTime` datetime DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`RecordId`,`StationId`)
);

CREATE TABLE `tbl_musynctask` (
  `TaskId` int NOT NULL AUTO_INCREMENT,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `SyncState` int NOT NULL,
  `SyncRule` int NOT NULL,
  `PlanTime` datetime DEFAULT NULL,
  `BeginTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `MaxRetryCount` int NOT NULL,
  `RetryCount` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`TaskId`)
);

CREATE TABLE `tbl_newinstation` (
  `IpAddress` varchar(128) NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `Id` mediumint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`Id`)
);

CREATE TABLE `tbl_newsystemconfigs` (
  `SystemConfigId` int NOT NULL,
  `SystemConfigKey` varchar(255) DEFAULT NULL,
  `SystemConfigValue` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY `PK_TBL_NewSystemConfigs_ID` (`SystemConfigId`)
);

CREATE TABLE `tbl_notificationsn` (
  `NotificationSnId` int PRIMARY KEY AUTO_INCREMENT,
  `SerialNo` decimal(14,0) DEFAULT NULL
);

CREATE TABLE `tbl_notifycommand` (
  `SequenceId` decimal(22,17) NOT NULL,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `CommandId` int NOT NULL,
  `CommandSeverity` int DEFAULT NULL,
  `CmdToken` text,
  `CommandPhase` int DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `CommandResultType` int DEFAULT NULL,
  `CommandResult` varchar(255) DEFAULT NULL,
  `CommandExecuterId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SequenceId`)
);

CREATE TABLE `tbl_onlineuser` (
  `OnlineUserId` int PRIMARY KEY AUTO_INCREMENT,
  `BusinessModuleType` int DEFAULT NULL,
  `LogonId` varchar(255) DEFAULT NULL,
  `UserId` int NOT NULL,
  `UserName` varchar(255) DEFAULT NULL,
  `LoginTime` datetime DEFAULT NULL,
  `LastUpdateTime` datetime DEFAULT NULL,
  `loginIP` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Status` int DEFAULT NULL,
  `Token` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_operation` (
  `OperationId` int NOT NULL,
  `OperationCategory` int DEFAULT NULL,
  `OperationName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `MenusItemId` int DEFAULT NULL,
  PRIMARY KEY (`OperationId`)
);

CREATE TABLE `tbl_operationdetail` (
  `OperationDetailId` int PRIMARY KEY AUTO_INCREMENT,
  `UserId` int NOT NULL,
  `ObjectId` varchar(128) DEFAULT NULL,
  `ObjectType` int NOT NULL,
  `PropertyName` varchar(128) DEFAULT NULL,
  `OperationTime` datetime NOT NULL,
  `OperationType` varchar(64) NOT NULL,
  `OldValue` varchar(4000) DEFAULT NULL,
  `NewValue` varchar(4000) DEFAULT NULL
);

CREATE TABLE `tbl_operationdetailmid` (
  `OperationDetailMidId` int PRIMARY KEY AUTO_INCREMENT,
  `UserId` int NOT NULL,
  `ObjectId` varchar(128) DEFAULT NULL,
  `ObjectType` int NOT NULL,
  `PropertyName` varchar(128) DEFAULT NULL,
  `OperationTime` datetime NOT NULL,
  `OperationType` varchar(64) NOT NULL,
  `OldValue` varchar(255) DEFAULT NULL,
  `NewValue` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_operationgroup` (
  `GroupId` int NOT NULL,
  `GroupName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GroupId`)
);

CREATE TABLE `tbl_operationgroupmap` (
  `OperationId` int NOT NULL,
  `GroupId` int NOT NULL,
  PRIMARY KEY (`GroupId`,`OperationId`)
);

CREATE TABLE `tbl_operationrecord` (
  `UserId` int NOT NULL,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `Operation` int NOT NULL,
  `OperationTime` datetime NOT NULL,
  `OperationType` int DEFAULT NULL,
  `OperationContent` varchar(3000) NOT NULL,
  PRIMARY KEY `IDX_operationrecord` (`OperationTime`,`UserId`,`Operation`)
);

CREATE TABLE `tbl_operationrecordmid` (
  `UserId` int NOT NULL,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `Operation` int NOT NULL,
  `OperationTime` datetime NOT NULL,
  `OperationType` int DEFAULT NULL,
  `OperationContent` varchar(255) NOT NULL,
  PRIMARY KEY `IDX_operationrecord` (`OperationTime`,`UserId`,`Operation`)
);

CREATE TABLE `tbl_originbussinesscategorymap` (
  `EquipmentTemplateId` int NOT NULL,
  `OriginCategory` int NOT NULL,
  PRIMARY KEY (`EquipmentTemplateId`)
);

CREATE TABLE `tbl_primaryalarm` (
  `FilterId` int NOT NULL,
  `StationCategory` int NOT NULL,
  `PrimaryStationId` int NOT NULL,
  `PrimaryEquipmentId` int NOT NULL,
  `PrimaryBaseTypeId` int NOT NULL,
  PRIMARY KEY `TBL_PrimaryAlarm_ID1` (`FilterId`,`StationCategory`,`PrimaryStationId`,`PrimaryEquipmentId`,`PrimaryBaseTypeId`)
);

CREATE TABLE `tbl_primarykeyidentity` (
  `TableId` int NOT NULL,
  `TableName` varchar(30) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`TableId`)
);

CREATE TABLE `tbl_primarykeyvalue` (
  `TableId` int NOT NULL,
  `PostalCode` int NOT NULL,
  `MinValue` int DEFAULT NULL,
  `CurrentValue` int DEFAULT NULL,
  PRIMARY KEY (`PostalCode`,`TableId`)
);

CREATE TABLE `tbl_primarysecondmaprule` (
  `FilterId` int NOT NULL,
  `RuleName` varchar(255) NOT NULL,
  `ProcessDelay` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY `TBL_PrimarySecondMapRule_ID1` (`FilterId`)
);

CREATE TABLE `tbl_projectstatehouse` (
  `HouseId` int NOT NULL,
  `StationId` int NOT NULL,
  `HouseName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Reason` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  `LastUpdateDate` datetime NOT NULL,
  PRIMARY KEY (`HouseId`,`StationId`)
);

CREATE TABLE `tbl_projectstateoperation` (
  `OperationId` int NOT NULL AUTO_INCREMENT,
  `OperationType` int NOT NULL,
  `Operation` varchar(100) DEFAULT NULL,
  `StationId` int DEFAULT NULL,
  `HouseId` int DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `Reason` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  `OperationDate` datetime NOT NULL,
  PRIMARY KEY (`OperationId`)
);

CREATE TABLE `tbl_projectstatestation` (
  `StationId` int NOT NULL,
  `Reason` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  `LastUpdateDate` datetime NOT NULL,
  PRIMARY KEY (`StationId`)
);

CREATE TABLE `tbl_replicatelogs` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `LogStr` text NOT NULL,
  `InsertTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`LogId`)
);

CREATE TABLE `tbl_rmuworkstationservice` (
  `WorkStationId` int NOT NULL,
  `WorkStationName` varchar(255) DEFAULT NULL,
  `SCLoginIP` varchar(64) DEFAULT NULL,
  `SCServicePort` varchar(64) DEFAULT NULL,
  `SCLoginPort` varchar(64) DEFAULT NULL,
  `SUURLSuffix` varchar(64) DEFAULT NULL,
  `HeartPeriod` varchar(64) DEFAULT NULL,
  `UserName` varchar(64) DEFAULT NULL,
  `PassWord` varchar(64) DEFAULT NULL,
  `FtpUser` varchar(64) DEFAULT NULL,
  `FtpPass` varchar(64) DEFAULT NULL,
  `SUServiceIP` varchar(64) DEFAULT NULL,
  `SUServicePort` varchar(64) DEFAULT NULL,
  `SUVer` varchar(64) DEFAULT NULL,
  `AskConfigInterval` varchar(64) DEFAULT NULL,
  `LastAskFactoryTime` datetime DEFAULT NULL,
  `ExtendField1` varchar(128) DEFAULT NULL,
  `ExtendField2` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`WorkStationId`),
  KEY `TBL_RMUWorkStationService_IDX1` (`WorkStationId`)
);

CREATE TABLE `tbl_sampleridmap` (
  `SamplerIdMapId` int PRIMARY KEY AUTO_INCREMENT,
  `OldSamplerId` int DEFAULT NULL,
  `KoloSamplerId` int DEFAULT NULL
);

CREATE TABLE `tbl_secondaryalarm` (
  `FilterId` int NOT NULL,
  `StationCategory` int NOT NULL,
  `SecondaryStationId` int NOT NULL,
  `SecondaryEquipmentId` int NOT NULL,
  `SecondaryBaseTypeId` int NOT NULL,
  PRIMARY KEY `TBL_SecondaryAlarm_ID1` (`FilterId`,`StationCategory`,`SecondaryStationId`,`SecondaryEquipmentId`,`SecondaryBaseTypeId`)
);

CREATE TABLE `tbl_serialno` (
  `TableName` varchar(125) NOT NULL PRIMARY KEY,
  `SerialNo` decimal(12,0) NOT NULL
);

CREATE TABLE `tbl_signal` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `SignalId` int NOT NULL,
  `Enable` tinyint(1) NOT NULL,
  `Visible` tinyint(1) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SignalName` varchar(128) NOT NULL,
  `SignalCategory` int NOT NULL,
  `SignalType` int NOT NULL,
  `ChannelNo` int NOT NULL,
  `ChannelType` int NOT NULL,
  `Expression` text,
  `DataType` int DEFAULT NULL,
  `ShowPrecision` varchar(20) DEFAULT NULL,
  `Unit` varchar(64) DEFAULT NULL,
  `StoreInterval` double DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  `PercentThreshold` double DEFAULT NULL,
  `StaticsPeriod` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `ChargeStoreInterVal` double DEFAULT NULL,
  `ChargeAbsValue` double DEFAULT NULL,
  `DisplayIndex` int NOT NULL,
  `MDBSignalId` int DEFAULT NULL,
  `ModuleNo` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_TBLSignal_1` (`EquipmentTemplateId`),
  KEY `IDX_TBLSignal_2` (`EquipmentTemplateId`,`SignalId`),
  KEY `IDX_TBLSignal_3` (`EquipmentTemplateId`,`BaseTypeId`),
  KEY `IDX_BaseTypeId` (`BaseTypeId`)
);

CREATE TABLE `tbl_signalmeanings` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `SignalId` int NOT NULL,
  `StateValue` smallint NOT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `BaseCondId` decimal(12,0) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  UNIQUE KEY `CLUSTERED` (`EquipmentTemplateId`,`SignalId`,`StateValue`),
  KEY `IDX_TBLSignalMeanings_1` (`EquipmentTemplateId`,`SignalId`)
);

CREATE TABLE `tbl_signalproperty` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `SignalId` int NOT NULL,
  `SignalPropertyId` int NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  UNIQUE KEY `index_27` (`EquipmentTemplateId`,`SignalId`,`SignalPropertyId`),
  KEY `IDX_TBLSignalProperty_1` (`EquipmentTemplateId`,`SignalId`)
);

CREATE TABLE `tbl_signalstatistics` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(128) NOT NULL,
  `StatisticsTime` datetime NOT NULL,
  `MinValue` double DEFAULT NULL,
  `MinTime` datetime DEFAULT NULL,
  `MaxValue` double DEFAULT NULL,
  `MaxTime` datetime DEFAULT NULL,
  `AvgValue` double DEFAULT NULL,
  `AvgTime` datetime DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
  PRIMARY KEY `TBL_SignalStatistics_IDX1` (`StatisticsTime`,`StationId`,`EquipmentId`,`SignalId`)
);

CREATE TABLE `tbl_signalstatisticsmid` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(128) NOT NULL,
  `StatisticsTime` datetime NOT NULL,
  `MinValue` double DEFAULT NULL,
  `MinTime` datetime DEFAULT NULL,
  `MaxValue` double DEFAULT NULL,
  `MaxTime` datetime DEFAULT NULL,
  `AvgValue` double DEFAULT NULL,
  `AvgTime` datetime DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `BaseTypeName` varchar(128) DEFAULT NULL,
    PRIMARY KEY `TBL_SignalStatisticsMid_IDX1` (`StatisticsTime`,`StationId`,`EquipmentId`,`SignalId`)
);

CREATE TABLE `tbl_spaceusedresult` (
  `SpaceUsedResultId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `tablename` text,
  `rows` decimal(17,0) DEFAULT NULL,
  `reserved` decimal(17,0) DEFAULT NULL,
  `data` decimal(17,0) DEFAULT NULL,
  `indexp` decimal(17,0) DEFAULT NULL,
  `unused` decimal(17,0) DEFAULT NULL
);

CREATE TABLE `tbl_specialtygroup` (
  `SpecialtyGroupId` int NOT NULL,
  `SpecialtyGroupName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SpecialtyGroupId`)
);

CREATE TABLE `tbl_specialtygroupmap` (
  `SpecialtyGroupId` int NOT NULL,
  `EntryItemId` int NOT NULL,
  `Operation` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryItemId`,`SpecialtyGroupId`)
);

CREATE TABLE `tbl_station` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `Latitude` decimal(22,17) DEFAULT NULL,
  `Longitude` decimal(22,17) DEFAULT NULL,
  `SetupTime` datetime DEFAULT NULL,
  `CompanyId` int DEFAULT NULL,
  `ConnectState` int NOT NULL DEFAULT '2',
  `UpdateTime` datetime NOT NULL,
  `StationCategory` int NOT NULL,
  `StationGrade` int NOT NULL,
  `StationState` int NOT NULL,
  `ContactId` int DEFAULT NULL,
  `SupportTime` int DEFAULT NULL,
  `OnWayTime` double DEFAULT NULL,
  `SurplusTime` double DEFAULT NULL,
  `FloorNo` varchar(50) DEFAULT NULL,
  `PropList` varchar(255) DEFAULT NULL,
  `Acreage` double DEFAULT NULL,
  `BuildingType` int DEFAULT NULL,
  `ContainNode` tinyint(1) NOT NULL DEFAULT '0',
  `Description` varchar(255) DEFAULT NULL,
  `BordNumber` int DEFAULT NULL,
  `CenterId` int NOT NULL,
  `Enable` tinyint(1) NOT NULL DEFAULT '1',
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `InstallTime` datetime DEFAULT NULL,
  PRIMARY KEY (`StationId`),
  UNIQUE KEY `StationId` (`StationId`)
);

CREATE TABLE `tbl_stationbasemap` (
  `StationBaseType` int NOT NULL,
  `StationCategory` int NOT NULL,
  `StandardType` int NOT NULL,
  PRIMARY KEY (`StandardType`,`StationBaseType`,`StationCategory`)
);

CREATE TABLE `tbl_stationbasetype` (
  `Id` int NOT NULL,
  `StandardId` int NOT NULL,
  `Type` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`Id`,`StandardId`)
);

CREATE TABLE `tbl_stationidmap` (
  `StationIdMap` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `OldStationId` int DEFAULT NULL,
  `KoloStationId` int DEFAULT NULL,
  `OPostCode` int DEFAULT NULL
);

CREATE TABLE `tbl_stationmask` (
  `StationId` int NOT NULL,
  `TimeGroupId` int DEFAULT NULL,
  `Reason` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  PRIMARY KEY (`StationId`)
);

CREATE TABLE `tbl_stationprojectinfo` (
  `StationId` int NOT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `InstallTime` datetime DEFAULT NULL,
  PRIMARY KEY `TBL_StationProjectInfo_IDX1` (`StationId`)
);

CREATE TABLE `tbl_stationstructure` (
  `StructureId` int NOT NULL,
  `StructureGroupId` int NOT NULL,
  `ParentStructureId` int NOT NULL,
  `StructureName` varchar(128) NOT NULL,
  `IsUngroup` bit(1) NOT NULL,
  `StructureType` int NOT NULL,
  `MapZoom` double DEFAULT NULL,
  `Longitude` decimal(22,17) DEFAULT NULL,
  `Latitude` decimal(22,17) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LevelPath` varchar(200) NOT NULL,
  `Enable` bit(1) NOT NULL,
  PRIMARY KEY (`StructureId`)
);

CREATE TABLE `tbl_stationstructureidmap` (
  `StationStructureIdMap` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `OldStructureId` int DEFAULT NULL,
  `KoloStructureId` int DEFAULT NULL,
  `OPostCode` int DEFAULT NULL
);

CREATE TABLE `tbl_stationstructuremap` (
  `StructureId` int NOT NULL,
  `StationId` int NOT NULL,
  PRIMARY KEY (`StationId`,`StructureId`)
);

CREATE TABLE `tbl_stationswatchmap` (
  `SwatchStationId` int NOT NULL,
  `StationId` int NOT NULL,
    PRIMARY KEY (`SwatchStationId`,`StationId`)
);

CREATE TABLE `tbl_stationvendormap` (
  `StationId` int NOT NULL,
  `VendorName` varchar(50) NOT NULL,
  PRIMARY KEY (`StationId`,`VendorName`)
);

CREATE TABLE `tbl_stationvip` (
  `StationID` int NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`StationID`)
);

CREATE TABLE `tbl_swatchstation` (
  `SwatchStationId` int NOT NULL AUTO_INCREMENT,
  `SwatchStationName` varchar(128) NOT NULL,
  `StationId` int NOT NULL,
  `CreateTime` datetime NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY `SwatchStationId` (`SwatchStationId`)
);

CREATE TABLE `tbl_sysconfig` (
  `ConfigKey` varchar(255) NOT NULL,
  `ConfigValue` varchar(1024) DEFAULT NULL,
  PRIMARY KEY `IDX_SysConfig_1` (`ConfigKey`)
);

CREATE TABLE `tbl_timegroup` (
  `TimeGroupId` int NOT NULL,
  `TimeGroupCategory` int NOT NULL,
  `TimeGroupName` varchar(128) NOT NULL,
  `TimeGroupType` smallint NOT NULL,
  `TimeGroupException` bit(1) NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL,
  `LastUpdateDate` datetime DEFAULT NULL,
  PRIMARY KEY (`TimeGroupId`)
);

CREATE TABLE `tbl_timegroupset` (
  `TimeGroupSetId` int NOT NULL,
  `TimeGroupSetName` varchar(128) NOT NULL,
  PRIMARY KEY (`TimeGroupSetId`)
);

CREATE TABLE `tbl_timegroupspan` (
  `TimeSpanId` int NOT NULL,
  `TimeGroupId` int NOT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Week` smallint DEFAULT NULL,
  `TimeSpanChar` varchar(255) NOT NULL,
  `LastUpdateDate` datetime DEFAULT NULL,
  PRIMARY KEY (`TimeSpanId`)
);

CREATE TABLE `tbl_userrole` (
  `RoleId` int NOT NULL,
  `RoleName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`RoleId`)
);

CREATE TABLE `tbl_userrolemap` (
  `UserId` int NOT NULL,
  `RoleId` int NOT NULL,
  PRIMARY KEY (`RoleId`,`UserId`)
);

CREATE TABLE `tbl_userroleright` (
  `RoleId` int NOT NULL,
  `OperationId` int NOT NULL,
  `OperationType` int NOT NULL,
  PRIMARY KEY (`OperationId`,`OperationType`,`RoleId`)
);

CREATE TABLE `tbl_workstation` (
  `WorkStationId` int NOT NULL,
  `WorkStationName` varchar(255) NOT NULL,
  `WorkStationType` int NOT NULL,
  `IPAddress` varchar(64) NOT NULL,
  `ParentId` int NOT NULL DEFAULT '0',
  `ConnectState` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `IsUsed` bit(1) NOT NULL DEFAULT b'1',
  `CPU` double DEFAULT NULL,
  `Memory` double DEFAULT NULL,
  `ThreadCount` int DEFAULT NULL,
  `DiskFreeSpace` double DEFAULT NULL,
  `DBFreeSpace` double DEFAULT NULL,
  `LastCommTime` datetime DEFAULT NULL,
  PRIMARY KEY (`WorkStationId`)
);

CREATE TABLE `tbl_workstationex` (
  `WorkstationExId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `WorkStationId` int DEFAULT NULL,
  `Content` varchar(500) DEFAULT NULL
);

CREATE TABLE `tbl_workstationmap` (
  `WorkstationMapId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `SiteWebWorkStationId` int DEFAULT NULL,
  `KoloWorkStationId` int DEFAULT NULL,
  `SiteWebWorkStationName` varchar(128) DEFAULT NULL,
  `KoloWorkStationName` varchar(128) DEFAULT NULL
);

CREATE TABLE `tbl_writebackentry` (
  `EntryId` int NOT NULL,
  `EntryCategory` int DEFAULT NULL,
  `EntryName` varchar(128) DEFAULT NULL,
  `EntryTitle` varchar(128) DEFAULT NULL,
  `EntryAlias` varchar(255) DEFAULT NULL,
  `Enable` bit(1) NOT NULL DEFAULT b'1',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryId`)
);

CREATE TABLE `tsl_acrossmonitorunitsignal` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `Expression` longtext,
  PRIMARY KEY (`EquipmentId`,`SignalId`,`StationId`)
);

CREATE TABLE `tsl_activeevent` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `SequenceId` varchar(128) NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ResetSequenceId` varchar(128) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `ReversalNum` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  PRIMARY KEY `TSL_ActiveEvent_ID1` (`SequenceId`),
  KEY `TSL_ActiveEvent_ID2` (`ResetSequenceId`),
  KEY `IDX_TSL_ActiveEvent_3` (`StationId`,`EquipmentId`,`EventId`,`EventConditionId`)
);

CREATE TABLE `tsl_channelmap` (
  `SamplerUnitId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `OriginalChannelNo` int NOT NULL,
  `StandardChannelNo` int NOT NULL,
  PRIMARY KEY (`SamplerUnitId`)
);

CREATE TABLE `tsl_dataservercapacity` (
  `DataServerId` int NOT NULL,
  `Capacity` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`DataServerId`)
);

CREATE TABLE `tsl_deviceportmap` (
  `SerialDeviceId` int NOT NULL,
  `SerialPortId` int NOT NULL,
  `PortId` int NOT NULL,
  PRIMARY KEY (`SerialDeviceId`,`SerialPortId`,`PortId`)
);

CREATE TABLE `tsl_ipdevice` (
  `DeviceId` int NOT NULL,
  `DeviceNo` varchar(128) NOT NULL,
  `DeviceName` varchar(128) NOT NULL,
  `ProtocolType` int NOT NULL,
  `IpAddress` varchar(128) NOT NULL,
  PRIMARY KEY (`DeviceId`)
);

CREATE TABLE `tsl_monitorunit` (
  `MonitorUnitId` int NOT NULL,
  `MonitorUnitName` varchar(128) NOT NULL,
  `MonitorUnitCategory` int NOT NULL,
  `MonitorUnitCode` varchar(128) NOT NULL,
  `WorkStationId` int DEFAULT NULL,
  `StationId` int DEFAULT NULL,
  `IpAddress` varchar(128) DEFAULT NULL,
  `RunMode` int DEFAULT NULL,
  `ConfigFileCode` varchar(32) DEFAULT NULL,
  `ConfigUpdateTime` datetime DEFAULT NULL,
  `SampleConfigCode` varchar(32) DEFAULT NULL,
  `SoftwareVersion` varchar(64) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `HeartbeatTime` datetime DEFAULT NULL,
  `ConnectState` int NOT NULL DEFAULT '2',
  `UpdateTime` datetime NOT NULL,
  `IsSync` tinyint(1) NOT NULL DEFAULT '1',
  `SyncTime` datetime DEFAULT NULL,
  `IsConfigOK` tinyint(1) NOT NULL DEFAULT '1',
  `ConfigFileCode_Old` varchar(32) DEFAULT NULL,
  `SampleConfigCode_Old` varchar(32) DEFAULT NULL,
  `AppCongfigId` int DEFAULT NULL,
  `CanDistribute` tinyint(1) NOT NULL,
  `Enable` tinyint(1) NOT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `InstallTime` datetime DEFAULT NULL,
  `FSU` bit(1) DEFAULT b'0',
  PRIMARY KEY (`MonitorUnitId`),
  UNIQUE KEY `MonitorUnitId` (`MonitorUnitId`),
  KEY `IDX_MonitorUnit_WorkStationId` (`WorkStationId`),
  KEY `IDX_MonitorUnit_StationId` (`StationId`)
);

CREATE TABLE `tsl_monitorunitconfig` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AppConfigId` int NOT NULL,
  `SiteWebTimeOut` int NOT NULL,
  `RetryTimes` int NOT NULL,
  `HeartBeat` int NOT NULL,
  `EquipmentTimeOut` int NOT NULL,
  `PortInterruptCount` int NOT NULL,
  `PortInitializeInternal` int NOT NULL,
  `MaxPortInitializeTimes` int NOT NULL,
  `PortQueryTimeOut` int NOT NULL,
  `DataSaveTimes` int NOT NULL,
  `HistorySignalSavedTimes` int NOT NULL,
  `HistoryBatterySavedTimes` int NOT NULL,
  `HistoryEventSavedTimes` int NOT NULL,
  `CardRecordSavedCount` int NOT NULL,
  `ControlLog` tinyint(1) NOT NULL,
  `IpAddressDS` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `tsl_monitorunitcontrol` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `ControlId` int NOT NULL,
  `TargetControl` int NOT NULL,
  `LogicExpression` text,
  PRIMARY KEY (`ControlId`,`EquipmentId`,`StationId`)
);

CREATE TABLE `tsl_monitorunitevent` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `StartExpression` varchar(255) DEFAULT NULL,
  `SuppressExpression` text,
  PRIMARY KEY (`EquipmentId`,`EventId`,`StationId`)
);

CREATE TABLE `tsl_monitorunitextend` (
  `MonitorUnitId` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendFiled1` varchar(255) DEFAULT NULL,
  `ExtendFiled2` varchar(255) DEFAULT NULL,
  `ExtendFiled3` varchar(255) DEFAULT NULL,
  `ExtendFiled4` varchar(255) DEFAULT NULL,
  `ExtendFiled5` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`MonitorUnitId`)
);

CREATE TABLE `tsl_monitorunitipmap` (
  `MonitorUnitId` int NOT NULL PRIMARY KEY,
  `NewIpAddress` varchar(128) DEFAULT NULL,
  `OldIpAddress` varchar(128) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `IsSync` bit(1) NOT NULL DEFAULT b'0',
  `IsConflict` bit(1) NOT NULL DEFAULT b'0',
  `Description` varchar(255) DEFAULT NULL
);

CREATE TABLE `tsl_monitorunitsignal` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `ReferenceSamplerUnitId` int DEFAULT NULL,
  `ReferenceChannelNo` int DEFAULT NULL,
  `Expression` text,
  `InstanceType` int NOT NULL,
  PRIMARY KEY (`EquipmentId`,`SignalId`,`StationId`)
);

CREATE TABLE `tsl_port` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PortId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `PortNo` int NOT NULL,
  `PortName` varchar(128) NOT NULL,
  `PortType` int NOT NULL,
  `Setting` varchar(255) NOT NULL,
  `PhoneNumber` varchar(128) DEFAULT NULL,
  `LinkSamplerUnitId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_Port_1` (`MonitorUnitId`,`PortId`),
  KEY `IDX_Port_MonitorUnitId` (`MonitorUnitId`,`PortId`)
);

CREATE TABLE `tsl_realtimerouting` (
  `DataServerId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  PRIMARY KEY (`DataServerId`,`MonitorUnitId`),
  UNIQUE KEY `IDX_MonitorUnitId_Routing` (`MonitorUnitId`)
);

CREATE TABLE `tsl_routedistribution` (
  `DataServerId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`DataServerId`,`MonitorUnitId`)
);

CREATE TABLE `tsl_sampler` (
  `SamplerId` int NOT NULL AUTO_INCREMENT,
  `SamplerName` varchar(128) NOT NULL,
  `SamplerType` smallint NOT NULL,
  `ProtocolCode` varchar(32) NOT NULL,
  `DLLCode` varchar(255) NOT NULL,
  `DLLVersion` varchar(32) NOT NULL,
  `ProtocolFilePath` varchar(255) NOT NULL,
  `DLLFilePath` varchar(255) NOT NULL,
  `DllPath` varchar(255) NOT NULL,
  `Setting` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SoCode` varchar(255) NOT NULL,
  `SoPath` varchar(255) NOT NULL,
  `UploadProtocolFile` tinyint(1) default 0,
  PRIMARY KEY (`SamplerId`),
  UNIQUE KEY `SamplerId` (`SamplerId`),
  UNIQUE KEY `uniqueProtocolcode` (`ProtocolCode`)
);

CREATE TABLE `tsl_samplerunit` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `SamplerUnitId` int NOT NULL,
  `PortId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `SamplerId` int NOT NULL,
  `ParentSamplerUnitId` int NOT NULL,
  `SamplerType` int NOT NULL,
  `SamplerUnitName` varchar(128) NOT NULL,
  `Address` int NOT NULL,
  `SpUnitInterval` double DEFAULT NULL,
  `DllPath` varchar(128) DEFAULT NULL,
  `ConnectState` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `PhoneNumber` varchar(128) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_SamplerUnit_1` (`MonitorUnitId`,`SamplerUnitId`),
  KEY `IDX_SamplerUnit_MonitorUnitId` (`MonitorUnitId`,`PortId`,`SamplerUnitId`),
  KEY `IDX_SamplerUnit_SamplerUnitId` (`SamplerUnitId`)
);

CREATE TABLE `tsl_subscribesignal` (
  `StationId` int NOT NULL,
  `HostId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `SubscribeType` int NOT NULL,
  `LastSampleDateTime` datetime DEFAULT NULL,
  `LastUpdateDateTime` datetime DEFAULT NULL,
  `SubscribeDateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`HostId`,`SignalId`,`StationId`),
  KEY `IDX_SubscribeSignal_HostId` (`HostId`,`LastUpdateDateTime`)
);

