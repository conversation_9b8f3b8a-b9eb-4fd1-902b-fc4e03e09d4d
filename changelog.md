# 发布记录

所有可记录的更改记录都要记录在本文件中.

## [未发布]

### 新增
- 条件屏蔽中添加设备类型屏蔽条件
- 视频脚本添加"批量回放"菜单项
- 菜单脚本新增排序字段
- 添加对200d7设备子父模板合并的支持
- 防重放加密串中添加token进行计算，且使用POST\PUT\DELETE请求时添加请求体进行计算
- 设备动态数据组态控件添加已结束未确认配置项
- 初始化设备组态时将名称使用{}号包裹，方便批量应用时好替换
- 增加一\五分钟时间粒度的历史信号报表查询
- 设备树层级增加是否有设备离线状态字段
- 历史数据报表中设备选择器与信号基类选择器添加联动
- 历史告警报表始终能查询五六级告警，不受告警开关影响
- 历史告警增加查询菜单，可以进行分页条件查询，字段条件与活动告警一致
- 添加机架视图组态控件
- 通用定制报表支持xlsx格式文件
- 告警屏蔽-告警屏蔽添加告警名称的过滤条件
- 版本管理相关的表添加主键
- 添加树形结构报表菜单，让报表以树状显示，删除报表增加删除报表和树的关系逻辑
- MySQL\达梦\postgres信创脚本将没有添加主键的表都添加主键
- 添加局站工程态状态的更新任务
- 资产扩展字段添加绑定表功能
- 历史信号报表添加层级位置字段
- 资产列表支持绑定设备、绑定表扩展字段导入
- 解冻账号
- 报表支持列的自定义显示

### 更改
- 修复全时段告警屏蔽时活动告警不会结束问题
- 修复信号动态配置时信号由于缓存没有刷新会突然消失问题
- 修复通用定制报表大数据量忽略单位时，提示xls无法超过4000个单元格样式问题
- 实时监控页面添加信号状态字段
- 修复实时数据无法展示含义问题
- 修复事件名称动态配置名称不生效问题
- 修复历史信号报表5分钟存储没有估算的问题
- 修复定制报表历史信号无数据时报错无法导出问题
- 注释通用定制报表中多余的"总计"二字
- 修复活动告警页面中的历史告警报表通过告警等级过滤失效问题
- 修复api-proxy部分请求没有带authorization请求头导致403错误问题
- 修复OceanBase不支持 “FOR UPDATE OF tbl_alias” 语法的问题
- SITEWEB API活动告警查询接口返回增加设备位置字段
- 修复历史信号报表使用信号基类+时间颗粒度预估数据量很慢问题
- 去除mysql的xml中的_databaseId == 'postgresql'的判断，避免OceanBase查询报错
- 修复通用定制报表导出数据为0问题
- 修复资产批量删除时，不会删除扩展字段问题
- 调整opengauss的驱动为5.0.1版本，且给"week"字段加上""号，避免提示语法错误
- 修复增加排序字段后，【报表】菜单漂移问题
- 调整报表(活动告警，历史告警，全部告警，告警数量)支持五六级告警查询
- 修复opengauss在使用获取设备位置的函数时，如果层级已经被删除会报错问题
- 修复电信场景中获取信号标准名称时获取成信号基类问题
- 修复postgres与dameng删除TBL_TimeGroupSpan方法的xml不存在问题
- as实时监控页面的告警页面接口添加基类信号字段
- 修复设置了局房工程态但是告警模拟不生效问题
- 修复设备没有设置工程态时，告警模拟会报错问题
- 初始化中的所有菜单中的IsSystemConfig字段改为1,避免用户可以删除系统自带的菜单
- 修复当前告警报表添加事件过滤条件后，会查询报错问题
- 修复EntryId中ItemId值一致导致活动告警页面加载不了问题
- 修复局站工程态与设备工程态的显示状态在活动告警页面中显示错误问题
- 电信场景中实时监控页面局站分组中如果没有局站则移除该分组
- 修复电信场景获取操作权限错误导致屏蔽管理无法渲染问题
- 去除SpecialtyGroupMap实体中不存在的id字段
- 修复电信场景中获取局房的告警列表错误问题
- 调整电信场景中活动告警页面，如果设备没有映射在层级上会一直打印设备不存在的日志问题
- 修复当alarmChange表中未启用的告警的变更记录超过4000条时导致活动告警页面一直不会刷新问题
- 修复信号基类选择器会出现相同设备基类问题

## [V6.2.6.20250509] - 2025-05-09

### 新增
- 添加设备工程态的TTS与告警通知策略过滤参数
- 添加是否在查询时提示输出参数没有配置系统参数
- 在门禁卡操作增加卡复制按钮，点击弹出卡列表
- 门禁卡删除后保留备份，备份数据可以在卡授权使用
- 门设备删除后保留备份，备份数据可以在卡授权使用
- 增加门禁打标签功能，并在门树型结构显示。如一级保障门禁、IDC门禁、国密、公安等
- 在门禁卡操作增加卡延期，点击后弹出时间选择器
- 系统自诊断页面添加设备在线、离线、屏蔽数量统计显示
- 增加系统拓扑功能
- 增加对xxl-job守护逻辑
- 添加APP-U位管理\一柜一码\能耗概览\视频\FSU管理的操作权限脚本
- 添加是否启用APP菜单的权限参数
- 山西国网电力添加定制控件计算24h内信号的当前值、最大值、最小值
- 历史数据报表添加取值方式的参数选项
- 添加清理微观日志、宏观日志的任务，暂时保留一天
- 增加rabbitmq配置使用
- 高阳统一登录rabbitmq消息队列同步用户
- 关注信号添加信号订阅逻辑
- 修复定制的历史告警报表初始化数据错误导致查询的是活动告警报表问题
- tts语音新增按照告警等级优先播报;tts语音和通知策略新增关键字过滤
- 获取含有上架IT设备信息的机架列表
- 添加删除人员的操作权限脚本
- 添加是否显示TTS语音队列的系统参数
- 添加告警通知邮件主题的系统参数
- 告警等级排名
- 活动告警列表添加确认人字段
- 添加更新层级顺序的接口
- 添加告警通知 飞书告警通知
- xxl-job新增添加密码快到期发送短信通知提醒功能初始化脚本
- SitewebCore更新用户信息时, 如果S6用户不存在就新增用户

### 更改
- 修复告警收敛在规则2发生收敛时候，将tbl_activeevent表的ConvergenceEventId字段更新错误问题
- 修复达梦数据库中添加操作日志报无效的列错误
- 修复达梦数据库中门开门异常问题
- 将报表中的导出参数与i18n翻译的保持一致，避免导出报表时因为不一致导致前端判断异常
- 修复达梦数据库中历史告警报表查询时，数字与字符串对比导致的转换异常
- 修复新增默认驱动模板时没有把其他驱动模板的置为false
- 读取alarmChange表更新activeEvent时添加重试逻辑，避免serialNoId跳跃导致告警不一致
- 修复达梦数据库中“to”关键字报错问题
- 修复达梦，PG定时任务报表执行报错问题
- 修复用电统计报表，第一天的日差值统计错误问题
- 修复PG初始库定时任务无法创建问题
- 去除项目中根节点的名称展示
- 修复资产扩展字段数量过多时，前端查询超时问题
- 修复资产扩展字段为空时，报系统异常问题
- 通用定制报表,信号配置为空添加日志打印
- 修复前端路由跳转控件不能跳转内嵌路由时问题
- 历史曲线控件使用存储周期的信号值
- 修复资产扩展字段无法展示值问题
- 修复历史曲线控件遇到同一个信号同一个时间点有多条数据时的报错问题
- 修复资产导入时不会修改资产以及扩展字段问题
- 调整视频菜单初始化脚本中的端口80为9100,避免80端口太常用被占用
- 修复资产新增时，equipmentNo为null时，无法覆盖资产序列号字段问题
- 资产导入时不更新类型为绑定设备的扩展字段
- 修复PG场景更新资产会提示重复更新字段问题
- 修复自诊断虚拟监控单元获取实时路由时会报错问题
- 修复设备中部分实时信号是Long类型时导致整个设备信号都加载不出来问题
- 修复重要信号没有走信号订阅逻辑
- 通用定制报表添加表头时间格式数据字段、修复导出的excel单元格格式不正确，无法进行excel统计图设置问题
- 修复自诊断告警xxl-job自诊断告警一直在更新结束时间问题
- 修复设备被删除时，如果这个设备被绑定了区域权限，导致层级树无法展示问题
- 修复信创版本中部分报表中设备类型的ParameterControlId字段错误问题
- 修复opengauss与达梦数据库中更新同层级指标会报错问题
- 修复获取告警等级排名接口当查询的特定时间段内系统中没有告警时会报计算异常错误问题
- 修复电信场景中实时监控页面模拟量的信号值没有保留对应的单位与精度问题

## [V6.2.5.20240829] - 2024-08-29

### 新增
- 电信场景组态页中的告警相关接口添加是否只统计标准化告警系统功能
- 告警收敛发送时通过WebSocket推送提示框
- Siteweb双机切换后重置活动告警缓存
- APP支持查看采集器MAC地址(FSU列表)
- 监控主站支持对设备信息手动录入
- 添加告警收敛支持自研双机逻辑
- 新增设置单独红色和不同的告警颜色系统参数
- 添加用户级的用户主题
- 告警视频联动，配置告警和摄像头导入导出
- 电信场景添加告警模版修改与告警模版删除操作权限脚本
- 完成江苏移动智能运维代码的迁移与功能开发
- 告警视频联动增加系统参数视频平台配置项
- 所有告警报表添加注释字段
- app告警推送添加告警类型过滤条件
- 增加是否开启告警收敛弹窗系统参数以及对应功能实现
- 添加设备基类修改后组态页面的提示
- 添加控制命令与动态配置成功时发送redis通知消息
- 告警数量统计报表添加设备级与设备类型级选项
- 设备表新增photo字段，避免模板相同相互覆盖图片
- 历史数据报表添加年的时间颗粒度
- 活动告警列表添加开始时间段的查询
- S6后台定时向redis报送心跳
- 添加菜单内嵌功能
- 添加用户长时间未登录的锁定逻辑
- 添加token过期时间的设置(小时)
- 添加设备工程状态功能添加设备工程状态功能
- 实时告警页面添加告警的工程状态功能过滤条件
- 支持同步uicore组织结构
- 支持同步uicore用户工号
- 潍柴动力添加使用工号发送短信通知（定制）
- tts语音添加前端轮询时间间隔系统参数
- 上传文件使用白名单策略
- 默认开启防重放攻击
- 告警变更时添加redis发布消息
- 活动告警接口添加使用设备id+事件id的条件过滤，给视频组使用
- 添加历史告警定制页的初始化脚本
- 添加统计用户未读消息数接口
- 通过层级ids获取机架容量统计 
- 视频菜单脚本添加AR管理菜单项
### 更改
- 当前告警报表与历史告警报表中设备位置修正
- 将系统中的WebSocket整合成一个通用的
- 容量缓存调整为使用双机切换事件刷新
- 修复活动告警中告警位置的过滤条件里层级顺序不一致问题与可点击层级错误问题
- 修复双机情况下的Master判断错误
- 添加蓄电池定时任务判断逻辑
- 文档下载接口添加token校验，避免安全漏洞扫描
- liquibase更新，设备属性拓展
- 修复自定义菜单中由于fullId重复导致删除异常情况
- mqtt版本升级至v5
- 修复从redis中获取设备\局站\监控单元的在线状态时还会往mysql中获取状态的异常情况
- 通用定制报表默认历史数据，使用5分钟历史数据查询开关控制
- 将JVM参数外置
- 修复新增设备的信号在历史数据报表中查询异常问题
- 电信场景组态页中告警相关接口从缓存中获取
- uicore绑定超管映射s6绑定管理员角色
- 优化个推推送时异常提示
- 修复历史信号报表部分信号没有数据导致表头缺失问题
- 修复mqtt连接失败没有异常提示问题
- 修复历史数据报表（五分钟）在定时报表中导出失败问题
- 两秒刷新活动告警的频率从200调整成4000
- 将告警确认与告警强制结束调整回使用存储过程
- 修复历史信号报表中设备被删除了导致单元格错位的情况
- 将设备、局站、监控单元的连接状态改为从6379端口的redis中获取
- 修复信号配置修改时复制控制命令报错问题
- 超过2kb的内容自动启用gzip压缩
- 修复设备没有绑定层级时发送告警通知异常问题
- 修复设备扩展字段不展示问题
- 修复redis订阅消息订阅了实时信号的redis的问题
- 修复控制命令下发界面滚动条无法拖动问题
- 告警通知发送记录报表去除告警箱通知的过滤选项
- 修复从redis中获取设备状态时自诊断设备一直为未注册问题
- 修复蓄电池的收敛告警导出不了的问题
- 修复告警模拟出来的告警中设备类型为空问题
- 修复管理员账号长时间不登录也会锁定问题
- 修复设备驱动模板中节点删除后兄弟节点都消失问题
- 修复历史信号(五分钟)报表原始颗粒度预估十分不准问题
- 修复部分模板没有信号时刷新缓存报错问题
- 修复拥有的层级权限被删除后报错问题
- 修复gzip压缩不生效问题
- 去除top收敛中的基类限制
- 默认开启组态页面自适应功能
- 初始化菜单脚本中默认关闭的机架支路与告警收敛菜单
- 修复组态中获取设备连接状态错误问题
- 修复告警备注也会发送通知
- 历史信号报表导出的数据四舍五入保留两位小数
- 修复告警结束会发送两条通知问题
- 修复勾选了告警操作类型条件后，无法收到告警开始类型的个推消息
- 修复图标数据接口更新时method字段时更新成url字段问题
- 修复部分菜单PATH字段为'‘时登录跳转不进问题
- 在redis中获取连接状态解析失败时添加错误日志
- 修复机架管理中标签绑定率与机架u位使用率达梦数据库统计排序错误
- 修复it设备批量下架时容量数据不更新问题
### 删除

## [V6.2.4.20240227] - 2024-02-27

### 新增
- 增加初始化脚本，租户管理菜单，工作流URL的系统参数
- as专家建议迁移到s6
- 新增AI机房节能的图标与调整其英文名的大小写
- 能耗标准接口增加碳强度效率和用能效率接口，容量配置增加更新通用容量系统所有容量属性中基于指标计算的值的定时任务
- 增加AI能耗报表菜单
- 报表添加是否公开选项 
- 角色组态首页与用户组态首页支持通过组态选择器选择组态
- 告警屏蔽添加通过基类、层级添加屏蔽与解除屏蔽
- 人员导出添加工号列与职级列
- 添加用户在线列表功能开关
- 添加通过POST请求方式获取设备告警的接口，避免设备数量过多导致异常
- 添加机器人场景管理员的菜单权限脚本
- 实时监控告警状态添加告警未结束条件
- 添加内蒙4A单点登录
- 锂电池热失控预警
- 删除通用对象时同时删除其指标与组态
- 添加电信场景报表菜单图标脚本
- 添加license导入错误时的提示
- 添加预警列表组态控件 
- 设备查询接口支持多层级查询
- 给锂电池热失控预警功能增加后台开关
- 关注信号添加排序功能 
- 关注信号添加后端导出excel
- 打包默认关闭swagger文档
- 平安消息添加邮件通知与短信语音通知选项
- 添加ApiProxy的连接参数配置
- 仅保留近半年的平安消息发送记录
- apiProxy添加慢接口日志记录，apiProxy接口参数，不传默认赋值null
- 历史告警报表添加限制超过10W数据使用CSV导出，超过100W数据提示用户缩小查询条件
- 电信首页：局站分类统计、监控总览、终端统计、市电/监控可用率、电子地图、idc导航控件添加层级绑定权限过滤
- 新增支持app标签录入,标签绑定it设备,解绑
- 添加条件屏蔽菜单脚本
- 条件屏蔽添加通过条件创建屏蔽记录
- 电信首页：Top10局站告警统计、关键告警统计接口、监控总览、监控可用度、告警统计控件添加层级绑定权限过滤
- TTS语音添加过滤条件选择器，支持更多的过滤方式 
- 活动告警列表添加设备基类名称关键字的过滤条件支持
- 利用告警操作日志的详情字段记录备注内容
- 账户管理从列表展示变成部门树展示
- 添加查询展示参数接口用于定时报表查询是否展示参数
- 新增省地市油机可发电时长组态接口
- 添加机房AI节能、站点AI节能、中央空调AI节能的licenseFeature初始化脚本
- 系统参数进行加密避免安全漏洞
- 添加控件模板管理
- 通过条件获取设备列表接口添加层级信息
- 添加用户强制下线功能
- SiteWeb API增加获取设备告警配置接口
- 在线文档迁移至服务管理台
- 兼容ui-core的token
- 视频菜单脚本添加设备校时菜单
- 关注信号模板增加是否公开选项
- 设备报表
- 定时报表增加排序，默认按创建时间倒序
- 条件屏蔽中告警屏蔽页添加层级名称与设备名称
- 通知发送记录报表发送方式添加企业微信通知下拉筛选项
- 活动告警、历史告警增加翻转次数字段显示
- TTS语音翻转次数>1，不播报。
- 机架列表添加批量修改位置接口
- 菜单权限操作权限分组 
- 添加自定义token的请求头
- 专家建议增加接口，根据知识库id删除对应的所有关联案例
- 添加历史数据(原始数据)报表
- 告警趋势接口增加选择层级
- uicore mqtt整合
- 添加redis配置节可将业务数据的redis单独部署 
- 历史信号报表添加信号条数限制，避免过多导致无法查询
- 机架和动环监控设备映射关系配置
- 添加菜单权限分组脚本
- 增加ComputerRackEquipmenteMap_V0.1.xml
- 新疆移动4A单点登录
- 局站、监控单元、设备的连接状态如果存储在redis中则往redis中取
- 导入机架时添加u位设备的支持
- IT设备模型添加模型分类
- 告警通知针对确认告警也支持延时，若告警确认时间减去告警开始时间之差小于延时时间，则停止告警通知任务
- 告警通知针对结束告警也支持延时，若告警持续时间（告警结束时间减去告警开始时间）小于延时时间，则停止告警通知任务
- 告警通知策略增加策略网关服务配置
- 告警视频联动抓图
- 告警管理列表添加告警触发值列
- 条件屏蔽-设备屏蔽和告警屏蔽模板增加是否公开选项
- 接入管理菜单添加图表
- 告警管理页面统计条是否显示等级名称添加系统参数控制
- 实时指标增加设备在线状态，增加模拟修改指标历史数据接口
- 江西国税项目，从S6资产管理生成IT设备模型和IT设备信息
- 增加视频菜单
- 通知策略添加APP推送功能
- AI平台的专用账号,用于AI平台调用S6的接口使用
- 增加纽贝尔门禁添加只能绑定一个准进时间组限制
- 报表查询参数添加排序逻辑
- 添加根据基类信号和设备来查询历史信号功能
- 通知策略添加APP推送功能 
- mqtt推送消息到工作流创建告警工单
- 告警视频联动菜单
- 告警视频联动抓图异常处理
- tts添加已结束不播报功能
- 账号终端设备绑定功能
- 已授权得人脸与指纹不允许修改
- 增加英文版视频菜单
- 增加腾讯模板初始化脚本
- siteweb api增加告警等级、设备基类查询接口
- siteweb api实时信号查询接口返回增加采样时间、原始值、单位字段
- siteweb api发送控制、重发控制、确认控制接口记录日志
- 添加s6菜单转uicore菜单接口
- graphicpage表添加更新时间字段用于查询同一批更新的设备组态模板
- 通用定制报表支持含义显示
### 更改
- 修复刷卡门禁报表查询异常
- 更改排序方式，优化大量告警时的查询性能
- 统一文件夹目录下文件的时间格式
- 去除tbl_eventmask表的EventMaskId字段
- 修复分时段屏蔽时导致无法查询告警屏蔽报表
- 修复设备屏蔽查询报表无法展示设备位置
- 修复添加系统参数无法显示问题
- 历史信号报表添加是否带单位选项
- 获取楼宇设备类状态统计接口去除基类限制
- 调整部分英文脚本
- 调整操作权限脚本与索引脚本
- 修复表格控件权限错误问题
- 修复实时监控中分时段屏蔽的告警不生效问题
- 修复后台联动时获取操作人异常
- 修复后台线程、定时任务记录审计日志时报错问题
- 添加“AI运行策略”的操作权限脚本
- 重新整理系统参数配置的类型
- 修复机架自动上下架没有计算容量问题
- 调整个推推送告警条件配置：告警等级字段修改为eventLevel，设备类型修改为equipmentBaseType
- 修改通用对象表的主键初始化值，避免与组态初始化脚本冲突
- 修复记录审计日志死锁问题
- 修复历史信号报表使用原始值没保留精度问题
- 修复历史指标报表没有精度报错问题
- 修复获取层级类型异常
- 修复获取部分主键id报错问题
- 修复历史指标存在负数，聚合最小值不正确问题
- 调整设备信号配置获取逻辑，避免循环次数过多
- 修复定时报表中展示了未勾选的输出参数
- 修复告警通知过滤条件选择设备基类无法发送问题
- 修复告警屏蔽如果TimeGroupId相同报错,导致告警渲染不出来问题
- 修复模板信号配置为空时报错问题
- 在告警通知策略中，仅保留一种短信通知节点
- 修复告警灯接口传参错误
- 修复历史告警报表持续时间计算错误问题
- 修复消息推送列表找不到消息问题
- 调整当前告警报表与历史告警报表中的列头字段
- 修复区域权限统计局站异常问题，监控总览百分比错误问题，个推用户重复绑定手机唯一标识问题
- 电池配置改成批量插入避免过多影响性能
- 修复油机保养预警配置表达式检查逻辑
- 修复license部分模块未激活时登录不了的情况
- 台玻定制日报表生成日期改为创建日的前一天
- 修复个推用户重复绑定手机唯一标识问题
- 修复监控可用度根据层级统计错误问题
- 优化非管理员角色查询设备与层级缓慢问题
- 修复个推消息，华为手机角标无法实时增加问题
- 修复告警收敛列表无法查询问题
- 修复专家建议新增关联案例时，告警开始、结束、操作时间没有精确到时分秒问题
- 修复首页组态部分控件因为层级id（s2映射局房、局站时）相同导致查出异常数据问题
- 修复层级名称修改，机架表位置字段数据不同步问题
- 修复定时报表获取用户id异常
- 修复机架总U位变化曲线统计异常问题
- 修复单个机架添加没有启用时间问题
- 修复u位管理器名称无法显示问题
- 修复TTS语音模板转换错误问题
- 设备分类统计选了层级按区域统计，没选层级按片区统计
- 专家建议优化
- 修复设备分类统计层级统计数量异常问题 
- Top10局站告警统计接口添加stationId字段，避免无法正确跳转到电信的告警页面
- 修复电信场景下操作权限失效问题
- 历史数据报表去除原始数据颗粒度
- 修复设备批量应用单独应用图片报错问题
- 修复定时任务报表删除错误问题
- 修复TTS语音告警结束或确认有时不播报问题
- 修复主键冲突
- 修复电信首页中断统计接口统计采集单元数量异常问题
- 修复初始化项目时缺少GraphicTemplate表
- 修复token的存放不受拆分redis的yml配置影响问题
- 修复角色没有操作权限分组时的报错问题
- 修复fsu中断统计使用monitorunit统计数量
- 修复U位自动上下架时会产生两条上下架记录问题
- 修复用户查询局站添加null数据导致空指针异常
- 修复查询用户片区权限时的sql错误
- 中煤单点登录和数据同步
- 获取层级告警统计接口objectId改多选
- 修复机架管理面板中IT设备类型分类统计报错问题
- 添加初始化外部链接脚本，修复外部链接菜单无法显示问题
- 更新graphicpagetemplate.init.sql
- 机架管理中的饼图展示前TOP8
- 修复uicore更新角色时，角色不存在报错问题
- 修复CHD200G4指纹机授权失败问题
- 修复自定义菜单中部分菜单项为null的情况
- 修复代理终端新增与踢出权限点出现多个问题
- 修复通知策略网关服务报错
- 修复历史信号报表（原始数据）中存储类型无效问题
- 温场控件迁移到V2新增接口
- 修复历史信号报表中聚合列的计算错误
- 修复人员管理别名不显示问题
- 修复蓄电池在线状态不正确问题
- 告警抓图相关接口和菜单
- 修复区域权限、操作权限、菜单权限在没有选择权限点时没提示问题
- 修复部分菜单项失焦时不被选中问题
- 修复告警视频联动抓图报错
- 修复局站、监控单元、设备的连接状态重复获取问题
- 设备组态页优化组态下拉选项 
- 修复报表【是否公开】在公开时编辑会被其他用户设置问题
- 修复告警屏蔽报表中事件条件不生效问题
- 调整菜单脚本中的权限单词统一为permission,专业单词统一用Major,屏蔽单词统一用Suppression
- 修复tts语音空指针异常；修复告警通知网关服务配置接口传空报错
- 修复英文版菜单权限中缺少能耗、空调节能、AI机房的菜单权限问题
- 修复告警联动视频弹窗不弹的bug 
- 修复英文版记录审计日志时类型字段太短报错问题
- 修复英文版指标缺少指标定义脚本导致指标添加错误问题
- 英文版脚本屏蔽掉电池管理和空调AI菜单
- 修复局站的在线状态缓存不刷新问题
- 同步中英文版的报表参数，避免不一致导致查询问题
- 监控总览接口中Interrupt单词修改成Offline
- 设备模板组态类型通过字典表获取
- 修复指纹与人脸信息存在授权信息时的提示错误问题
- 门禁管理中准进时间组添加国际化
- 修复卡指纹授权时有时没有指纹授权记录问题
- 修复准进时间组个数计算错误，导致添加准进时间组失败
- 修复systemconfig表主键重复 
- 输出告警修改为导出告警,并修改起英文为"Export  Events"
- 告警收敛问题修正
- 补充英文版db changelog缺少的表
- 创建告警工单接口改为post；增加mqtt订阅工作流获取告警工单单号和状态
- 菜单中Equipment单词统一修改成Device
- 修复departmentId为null,告警通知策略不显示
- 告警工单号和状态更新到tbl_activeevent表和ACTIVE_EVENT_HASH_MAP缓存里
- 修复uicore同步时多创建问题
- 优化告警通知延时逻辑，告警延时仅针对告警开始
- 修复虚拟设备构造批量导入时采集单元名重复报错问题
- 修复视频导入错误
- 调整视频脚本中的英文翻译，Video Management改为Video
- 修复指标表达式检查没有翻译的问题
- 统一容量中U-bit的翻译为U Position
- 大楼告警数量统计、大楼设备类型告警数量统计改为统计未结束告警数量
- 修复告警通知中邮件接收人设置多个发送失败问题
- 修复批量设置门时无法设置多个时间组问题
- 修复CHD805\806下发卡授权+指纹时没有添加用户
- 修复建表AccountTerminalDeviceMap报错
- 修复门绑定的准进时间组排序错误问题
- 修复视频配置新增报错
- 将/login接口改成post请求，修复JSON劫持漏洞
- 修复告警抓图列表分页总行数总页数错误
- 修复告警屏蔽批量更新时TimeGroupId一样问题
- 修复机架设备配置菜单初始化脚本重复报错
- 修复系统内置账号长时间不登录会被锁定问题
### 删除

## [V6.2.********] - 2023-06-16

### 新增
- 屏蔽设备列表、屏蔽告警列表、告警过滤选择器添加区域权限
- 班次列表与班组列表添加部门权限
- 增加获取所有有权限资源接口
- 定时任务清理半年前的告警操作日志
- 通知策略添加企业微信应用通知功能
- 报表添加个人权限(每个用户都只能看到自己创建的报表模板，不能看到其他人创建的报表)

### 更改
- 重新开放历史数据报表(5分钟存储)报表
- findThisHierarchyResourceStructureIdsByUserId方法去重
- findResourceStructureIdsByUserIdAndResourceStructureIds方法重写，因为业务变化，传参filterResourceStructureIds必有权限
- chartapi表初始化脚本更新
- 重新开放历史数据报表(5分钟存储)报表
- 修复虚拟设备拆分树加载异常问题
- 指标关联测点显示层级全路径
- 去除项目中的proc_pager存储过程
- 去除操作日志记录存储过程
- 修复英文版菜单项IT设备模型脚本中英文错误
- 修复刷卡记录报表中持卡人有时无法显示问题

### 删除

## [V6.2.20230602] - 2023-06-02

### 新增
- 碳排放强度录入相关接口
- 碳配额录入增加父子节点的配额大小校验
- 增加在线文档菜单
- 批量屏蔽表格新增屏蔽启用状态列标识屏蔽是否已启用
- 租户管理接口
- 租户管理接口名字语义化，图表接口参数改为复数，增加接口获取有权限的机架列表

### 更改
- 电信版首页脚本更新，关键告警统计和监控可用度接口DataType改为3，监控可用度开启区域缩放
- 空调群控统计视图更改历史温度曲线接口独立请求
- 修复门禁报表查询参数接口404问题
- 定时报表下载分页查询
- 定时报表支持指标报表
- 能耗标准接口结果增加指标Id和指标名称字段
### 删除

## [V6.2.20230414] - 2023-04-28

### 新增
- 通用定制报表实时数据添加excel导出是否添加信号单位开关
- TTS语音播报添加重试机制避免在特殊场景下语音播报断开
- 添加前端埋点功能
- 历史信号报表数据量超过20W增加CSV文件导出
- 增加组态控件多方案和文本数字
- 台玻定制报表导出表头增加当天日期
- 历史信号报表导出的CSV文件七天未处理自动删除
- 系统参数初始化脚本新增【是否默认使用五分钟存储库查询历史信号数据】系统参数
- 删除人员增加门禁门卡关联判断
- 修复告警级别字段为空抛异常
- 信号基类添加基类信号类型字段
- 新增所有告警报表，用于查询历史告警与活动告警信息
- 定时任务清理半年前的埋点日志、告警通知记录日志、定时报表数据
- ### 更改
- 补充英文版资产管理图标
- 修复菜单中英文转换错误
- 使用HTTP请求的方式实现TTS语音播报
- 修复图标数据分离控件请求频繁导致服务未连接
- 通知策略中的通知时间统一格式化成yyyy-MM-dd HH:mm:ss格式
- 历史信号报表采样预估时间调整成:(昨天的现在这个点)与(昨天的现在这个点加一个小时)
- 视频场景增加后台管理、配置管理、个人中心菜单
- 修复部分单点登录功能创建账号记录操作详情日志异常
- 修复历史信号报表估算没选信号与信号基类报错问题
- 调整proxy接口的Accept请求头json优先级
- 修复告警屏蔽报表中屏蔽时间段类型为转换含义与接触屏蔽设备位置不显示问题
- 修改用户配置接口的url，避免请求被代理至as导致异常
- 修复tts语音完整位置转换错误
- 前端埋点接口取消token校验，避免401状态码记录失败
### 删除

## [V6.2.20230414] - 2023-04-28

### 新增
- 资产管理添加swagger文档
- 添加电信场景s2资产管理菜单
- 添加用户配置功能
- 新增系统参数分类功能
- 报表功能国际化
- 机架新增接口批量设置客户，按客户统计机架数量图表
### 更改
- 修改idc资产管理请求地址，避免nginx转发
- 解决项目启动需要首次接收到HTTP请求才会初始化DispatcherServlet问题 
- 解决修改蓄电池放电周期不生效问题
- 通用定制报表删除自动统计累计值逻辑（可自行在报表助手添加sum函数）
- 调整历史报表获取信号含义逻辑，避免重复转换
- 修复批量应用设备组态时，应用设备名问题
- webSocket连接从用户级改成会话级
- 最大用户并发数无论是否开启都可正确获取用户权限
### 删除
- 删除即时报表菜单

## [V6.2.20230414] - 2023-04-14

### 新增
- 视频脚本添加Alias字段
- BA控制命令websocket推送添加系统参数开关配置
- BA控制命令报表添加权限控制
- 重置密码时重置密码有效时间
- 当前告警，历史告警报表告警等级支持多选
- 图表数据接口添加统计某类设备在线、离线、告警数量接口
- 能耗数据SDK标准接口
- 电信场景增加区域权限菜单
- 新增通过系统参数类型查询系统参数的接口
- WebSocket增加心跳机制，避免Nginx90秒超时断开
- 使用系统参数动态切换是否默认使用五分钟库
- 电信场景添加资产管理菜单

### 更改
- 修复资产列表新增无法关联扩展字段问题
- 获取所有数据地图绑定数据接口区域权限问题修复
- 修改LDAP认证逻辑
- 修复license有时导入异常情况
- 修复控制命令推送插入重复数据问题
- 修复设备支路在支路id不存在时的报错情况
- 修复空调节能状态视图设备开关状态获取错误；温度曲线数据不对应错误
- 修复系统自诊断中二级园区CenterId与ResourceStructureId不同步问题
- 在启用并发登录情况下，允许调用单点登录相关3个接口
- 默认关闭历史数据报表(5分钟存储)报表
- 修复后台联动信号计算问题
- 修复历史指标数值太长使用科学计数法，保留小数位数值不正确问题
- 修复预警CPU过高问题

## [V6.2.20230330] - 2023-03-30

### 新增

- 后台联动增加对告警结束的支持
- 添加运营商多站点的组态初始化脚本
- 新增当月或当天差值指标计算函数
- 添加通过班组ids查询当前班组正在值班的人员接口
- 新增多班组值班人员接口初始化脚本
- 增加系统参数未结束告警定时弹窗触发时间（重庆涪陵融媒体项目）
- 后台联动增加层级、事件确认、事件存在节点，支持层级递归判断
- 关键告警、TOP10局站告警、监控可用度、添加片区权限
- 人员管理增加职级字段
- 新增菜单路由控件初始化脚本更新
- 获取所有设备及批量获取设备等接口重构，不再依赖于必须配置设备基类
- IDC新增资产管理模块 
- ActiveEventDTO中增加BaseTypeId字段
- 添加中英文切换功能
- 为兼容动态配置场景下修改事件等级，故在CoreEventSeverity中增加EventSeverity字段
- 满足山东移动多个S6系统之间SC与SS链接跳转需求
- 历史曲线组态加入到动环分类
- 设备分类统计添加层级过滤条件
- 添加消息模块，支持个推手机消息推送
- 添加获取设备的历史信号曲线接口
- TTS语音添加活动告警循环播报与确认告警不播报功能
- 通知策略增加部门权限过滤
- 告警视频联动增加部门权限过滤
- 个推推送添加角标展示配置
- 添加ba控制命令websocket推送
- 添加ba控制命令报表
- AI空调菜单
- 新增空调节能菜单

### 更改
- 修复apiproxy导致数组矩阵扁平化
- 修复发送控制命令时会异常退出问题
- 修复定时报表选择报表后，开始时间、结束事件隐藏问题
- 报表操作人添加部门权限
- 修复部分账号tts不推送问题(区域权限问题导致)
- 当前告警操作日志、告警数量统计报表、告警屏蔽日志报表、告警通知记录报表、电池放电报表、控制记录报表、历史告警报表、历史数据报表、用电统计报表、机架上下架报表添加区域权限
- 调整用户操作日志报表，职位替换为工号
- 修复配电websocket关闭时空指针异常
- 历史告警报表、告警操作日志报表、告警屏蔽报表、用户操作日志报表添加部门权限
- 后台联动代码重构 
- 蓄电池报表设备选择控件替换
- 修复监控总览用户查询局站存储过程报错问题
- 修复系统自诊断大数据量查询不出来问题
- 修复定时报表选择用电统计报表无法下载问题，type=4
- 关键告警统计修改为根据类型统计总数 
- 角色授权菜单保存权限与查询权限分开存与取
- 电信场景组态页修改活动告警总览控件接口
- 多班组人员接口初始化脚本更新
- 电子地图更改为片区权限
- 通知策略中声光告警修改为告警箱 
- 修复配电图节点频繁请求websocket造成的性能问题
- 使用原生webSocket改造TTS与告警视频联动
- 登录提示增加密码最大错误次数
- 修复了通用图表API代理post传参失败
- 修复了通用图表API代理ttps失败
- 排班管理由区域权限过滤修改成部门权限过滤
- 修复历史曲线多设备补点异常
- 修复通用定制报表导出重复列问题


## [V6.2.20230216] - 2023-02-16

### 新增

- 支持KeyCloak单点登录
- 添加图表数据分离配置脚本
- 添加储能电站菜单方案
- 储能电站菜单方案添加3d模型脚本
- 支持文件类型黑名单
- 后台获取登录信息增加登录类型
- 后台添加视频平台脚本
- 储能电站菜单方案增加蓄电池安全菜单项
- 告警通知发送记录报表添加接收人列头
- 查找文件夹下所有文件接口添加文件大小字段
- 添加获取与设置层级默认组态功能
- 添加通过层级id获取其子集IDC列表
- 添加报表的展示与隐藏设置
- 电信场景添加中断统计接口
- 系统拓扑图设备添加区域权限过滤
- 设备类新增多tab控件
- 告警视频联动添加告警条件
- 添加监控总览权限存储过程`PBL_QueryStationHaltPoweroffGenPrivilege`
- matrixchart表添加英文版脚本
- 添加监控可用率与市电可用率的组态控件脚本与接口
- 角色首页组态
- 新增MDC指标关系
- 设备列表查询添加区域权限过滤
- 新增告警锁定页面时默认时长(秒)系统参数
- 添加储能电池组态页
- 菜单权限点转换成树结构
- 图片选择器接口添加分页、排序功能、批量删除功能
- 用户首页组态增加部门
- 角色权限添加部门权限
- 多指标控件添加局站/FSU/设备中断统计与用电分析接口的初始化脚本
- 系统拓扑图排序显示
- 增加账号有效和永久设置
- 能耗多维度支持添加根节点流入节点
- 能耗全景图增加结果排序类型选择，查询时间类型选择
- 用电和碳排放查询接口增加用户Id按角色筛选查询数据
- 新增获取自定义维度树根节点流入节点的接口
- 新增用能费用趋势年月日查询接口
- 新增碳配额管理模块增删改查，查询历史用能量接口;
- 新增台玻能耗全景图三路进线用能查询，比功率，子层级用能查询相关接口
- 指标新增前置计算

### 更改

- 修改redis链接配置，支持哨兵模式连接
- influxdb sql注入漏洞修复
- 修复排班管理跨天排班错误通知失败问题
- 修复springSecurity在部分情况下获取不到userId问题
- 修复历史曲线告警补点顺序错误
- 值班人员控件只显示当前班组的值班人员
- 图形验证码使用过后删除
- 后台读取app.json文件获取配置
- Jwt取消过期时间
- 储能菜单组态页sql调整
- 修复没配置菜单权限时调用api/menutrees?userId=1接口报错问题
- 修改密码后删除token
- 修复图表数据分离英文版sql脚本名称一致报错问题
- 排班管理添加员工名称过滤与部门名称过滤
- 修复告警弹窗开启后，配置只显示一级告警，有其他级告警产生时也会弹窗
- 修复自定义菜单修改名称对应的权限名称不修改问题
- IDC场景去除局站状态权限
- 报表中的人员字段取消脱敏
- 修复历史告警报表无法导出问题
- 人员管理导出人员信息表头修改
- 修复scenepermissionmap表脚本初始化错误导致排班菜单异常
- 修复在线用户登录时间不正确问题
- 修复IT设备管理机架不回显问题
- 修复配电组态保存无绑定设备信息的问题
- 更改新的组态模板
- 修复层级类型相同时可能层级数异常问题
- 设备分类统计、局站分类统计、监控总览添加角色权限控制
- 修复告警通知发送记录报表中告警等级显示错误 
- 修复中文账号登录乱码问题
- 修复IPV6地址过长导致登录日志异常问题
- 修改英文版important.event.condition系统参数
- 修复告警通知过滤条件中位置显示异常问题
- 告警视频联动脚本删除视频联动策略 
- 修复告警屏蔽记录缺失层级id问题
- 开启系统参数密码默认快到期提醒
- admin修改密码后增加了有效期三个月，三个月后无法登录。
- 修复历史指标报表单行平均值统计错误问题
- 修复IT设备的容量异常情况 
- 历史数据报表修改报表名称，后面加上（5分钟存储）
- 修复英文版的电信首页组态接口绑定错误
- 局站添加基本查询接口
- 层级设备树添加最大告警等级字段
- 修复第三方接口调用失败（apiproxy）
- 修复用户没有旧角色时报错
- 蓄电池报表曲线赋值错误问题
- 修复能源管理-能效评定下菜单项权限异常问题
- 修复账号冻结提示错误问题
- 电能结果查询格式化
- 更改AI大屏、字典表、能耗指标管理、评定配置、实时预览、预警列表的图表
- 英文版设置为默认设备组态
- 修复设备批量应用在设备比较多时卡顿问题
- 修复活动告警可能缺少第一条的问题
- 空调相关报表中操作人字段变更为保存用户名
- 修复空调节能模块用电分析日统计总量计算问题
- 监控总览接口添加地市、区县分类统计
- 修复菜单协议自助设备英文错误
- 修复历史数据报表名称错误
- 修改关键告警统计接口根据事件类别查询
- 修改空调群控中能耗查询相关数据源
- 能源管理，原电价管理添加业务类型字段
- 原电价相关接口及计算逻辑添加业务类型字段
- 取消“获取所选节点各用能类型的历史值”接口的查询时间类型
- 修改能耗费用查询库名称
- 修改用能费用查询结果过滤条件
- 修改成本分析年月日查询结果展示
- 故障录波功能增加双击切换判断
- 故障录波功能使用quartz定时任务，默认调整为五分钟同步

### 删除
- 删除图形验证码系统参数

- Fixed foldouts in Dutch translation

## [V6.2.20221219] - 2022-12-19

### 新增

- 新增动环排版管理


### 更改

- 更改Pom文件，升级组件版本，修复安全扫描漏洞.
- 修复ApiProxy的token认证问题
- 去掉异常处理中printStackTrace

### 删除

- 删除目前用不到的Groovy，FF4J
