<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <!-- SiteWeb基础表 -->
    <include file="tables/001-foundation-tables.sql" relativeToChangelogFile="true"/>
    <!-- 基类字典及标准化相关数据表 -->
    <include file="tables/002-base-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_equipmentresource开头的数据表 -->
    <include file="tables/003-s2-equipment-resource-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_h5开头的数据表 -->
    <include file="tables/004-s2-h5-tables.sql" relativeToChangelogFile="true"/>
    <!-- S2巡检相关数据表 -->
    <include file="tables/005-s2-patrol-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_menu开头的数据表 -->
    <include file="tables/006-s2-menu-tables.sql" relativeToChangelogFile="true"/>
    <!-- S2报表相关数据表 -->
    <include file="tables/007-s2-report-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_resource开头的数据表 -->
    <include file="tables/008-s2-resource-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_spark开头的数据表 -->
    <include file="tables/009-s2-spark-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_standard开头的数据表 -->
    <include file="tables/010-s2-standard-tables.sql" relativeToChangelogFile="true"/>
    <!-- wo_开头的数据表 -->
    <include file="tables/011-s2-wo-tables.sql" relativeToChangelogFile="true"/>
    <!-- wr_开头的数据表 -->
    <include file="tables/012-s2-wr-tables.sql" relativeToChangelogFile="true"/>
    <!-- 以tbl_开头的一些业务数据表 -->
    <include file="tables/013-s2-business-tables.sql" relativeToChangelogFile="true"/>
    <!-- 门禁业务相关数据表 -->
    <include file="tables/014-door-access-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_sar开头的数据表 -->
    <include file="tables/015-s2-sar-tables.sql" relativeToChangelogFile="true"/>
    <!-- tbl_ics开头的数据表 -->
    <include file="tables/016-s2-ics-tables.sql" relativeToChangelogFile="true"/>
    <!-- B接口业务相关数据表 -->
    <include file="tables/017-b-interface-tables.sql" relativeToChangelogFile="true"/>
    <!-- C接口业务相关数据表 -->
    <include file="tables/050-c-interface-tables.sql" relativeToChangelogFile="true"/>
    <!-- 纽贝尔门禁相关数据表 -->
    <include file="tables/052-NbrDoor-tables.sql" relativeToChangelogFile="true"/>
    <!-- quartz相关数据表 -->
    <include file="tables/101-quartz-tables.sql" relativeToChangelogFile="true"/>
    <!-- xxl-job相关数据表 -->
    <include file="tables/102-xxl-job-tables.sql" relativeToChangelogFile="true"/>
    <!-- 数据库函数和视图 -->
    <include file="tables/103-function-view.sql" relativeToChangelogFile="true"/>
    <!-- S6后台业务数据表 -->
    <include file="tables/104-admin-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6指标相关数据表 -->
    <include file="tables/105-complexindex-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6组态相关数据表 -->
    <include file="tables/106-hmi-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6报表相关数据表 -->
    <include file="tables/107-s6-report-tables.sql" relativeToChangelogFile="true"/>
    <!-- 3D相关数据表 -->
    <include file="tables/108-3d-tables.sql" relativeToChangelogFile="true"/>
    <!-- 配电相关数据表 -->
    <include file="tables/109-power-distribution-tables.sql" relativeToChangelogFile="true"/>
    <!-- 资产相关数据表 -->
    <include file="tables/110-asset-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6电池相关数据表 -->
    <include file="tables/111-battery-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6告警通知相关数据表 -->
    <include file="tables/112-alarmnotify-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6后台联动相关数据表 -->
    <include file="tables/113-linkage-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6告警收敛相关数据表 -->
    <include file="tables/114-alarm-convergence-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6告警视频联动相关数据表 -->
    <include file="tables/115-alarmvideolink-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6摄像头相关数据表 -->
    <include file="tables/116-camera-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6驱动模板相关数据表 -->
    <include file="tables/117-driver-template-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6平安消息相关数据表 -->
    <include file="tables/118-safemessage-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6交接班相关数据表 -->
    <include file="tables/119-shift-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6容量与机架相关数据表 -->
    <include file="tables/120-capacity-rack-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6锂电池热失控预测相关数据表 -->
    <include file="tables/121-thermal-runaway-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6空调群控相关数据表 -->
    <include file="tables/201-aircon-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6能耗相关数据表 -->
    <include file="tables/202-energy-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6油机相关数据表 -->
    <include file="tables/203-generator-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6预警相关数据表 -->
    <include file="tables/204-prealarm-tables.sql" relativeToChangelogFile="true"/>
    <!-- S6站内信数据表 -->
    <include file="tables/122-internalmessage-tables.sql" relativeToChangelogFile="true"/>

    <!-- S2基础数据初始化脚本 -->
    <include file="sql/001-s2-init.sql" relativeToChangelogFile="true"/>
    <!-- S2数据字典初始化脚本 -->
    <include file="sql/002-s2-data-dict.init.sql" relativeToChangelogFile="true"/>
    <!-- S2基类及标准化初始化脚本 -->
    <include file="sql/003-s2-base-init.sql" relativeToChangelogFile="true"/>
    <!-- S2巡检初始化脚本 -->
    <include file="sql/004-s2-patrol-init.sql" relativeToChangelogFile="true"/>
    <!-- S2报表初始化脚本 -->
    <include file="sql/005-s2-report-init.sql" relativeToChangelogFile="true"/>
    <!-- B接口初始化脚本 -->
    <include file="sql/006-s2-b-interface-init.sql" relativeToChangelogFile="true"/>
    <!--    门禁初始化脚本-->
    <include file="sql/014-door-access-init.sql" relativeToChangelogFile="true"/>
    <!--    纽贝尔门禁脚本-->
    <include file="sql/052-NbrDoor-init.sql" relativeToChangelogFile="true"/>
    <!-- xxl-job初始化脚本 -->
    <include file="sql/101-xxl-job.init.sql" relativeToChangelogFile="true"/>
    <!-- S6后台初始化脚本 -->
    <include file="sql/102-backstage.mysql.init.sql" relativeToChangelogFile="true"/>
    <!-- S6指标初始化脚本 -->
    <include file="sql/103-ComplexIndex.init.sql" relativeToChangelogFile="true"/>
    <!-- S6组态相关初始化脚本 -->
    <include file="sql/104-graphicpagetemplate.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/105-graphic.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/106-compdataset.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/107-matrixchart.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/108-Chart.init.sql" relativeToChangelogFile="true"/>
    <!-- BaseUnit初始化脚本 -->
    <include file="sql/109-BaseUnit.init.sql" relativeToChangelogFile="true"/>
    <!-- License初始化脚本 -->
    <include file="sql/110-License.init.sql" relativeToChangelogFile="true"/>
    <!-- S6菜单初始化脚本 -->
    <include file="sql/111-menu.init.sql" relativeToChangelogFile="true"/>
    <!-- S6文件初始化脚本 -->
    <include file="sql/112-DiskFile.init.sql" relativeToChangelogFile="true"/>
    <!-- S6权限组初始化脚本 -->
    <include file="sql/113-permissionGroup.init.sql" relativeToChangelogFile="true"/>
    <!-- S6层级类型初始化脚本 -->
    <include file="sql/114-ResourceStructureType.init.sql" relativeToChangelogFile="true"/>
    <!-- S6房间分类初始化脚本 -->
    <include file="sql/115-RoomCategory.init.sql" relativeToChangelogFile="true"/>
    <!-- S6场景相关初始化脚本 -->
    <include file="sql/116-scene.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/117-scenecompmap.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/118-scenepermissioncategory.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/119-scenepermissionmap.init.sql" relativeToChangelogFile="true"/>
    <!-- S6系统导航初始化脚本 -->
    <include file="sql/120-SystemNavigation.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/121-TtsConfig.init.sql" relativeToChangelogFile="true"/>
    <include file="sql/122-utility.mysql.init.sql" relativeToChangelogFile="true"/>
    <!-- S6报表初始化脚本 -->
    <include file="sql/123-report.init.sql" relativeToChangelogFile="true"/>
    <!-- S6告警通知初始化脚本 -->
    <include file="sql/124-alarm-notify-element.init.sql" relativeToChangelogFile="true"/>
    <!-- S6后台联动初始化脚本 -->
    <include file="sql/125-link-element.init.sql" relativeToChangelogFile="true"/>
    <!-- S6容量初始化脚本 -->
    <include file="sql/126-Capacity.init.sql" relativeToChangelogFile="true"/>
    <!-- 3D初始化脚本 -->
    <include file="sql/127-Dimension.init.sql" relativeToChangelogFile="true"/>
    <!-- S6告警收敛初始化脚本 -->
    <include file="sql/128-eventconvergencerule.init.sql" relativeToChangelogFile="true"/>
    <!-- S6交接班初始化脚本 -->
    <include file="sql/129-Shift.init.sql" relativeToChangelogFile="true"/>
    <!-- S6驱动模板初始化脚本 -->
    <include file="sql/130-tblDriveTemplateInit.sql" relativeToChangelogFile="true"/>
    <!-- S6集成新视频平台初始化脚本 -->
    <include file="sql/150-video-init.sql" relativeToChangelogFile="true"/>
    <!-- S6空调群控初始化脚本 -->
    <include file="sql/201-aircon.init.sql" relativeToChangelogFile="true"/>
    <!-- S6能耗初始化脚本 -->
    <include file="sql/202-energydataentryanddataitem.init.sql" relativeToChangelogFile="true"/>
    <!-- S6预警初始化脚本 -->
    <include file="sql/203-PreAlarm.init.sql" relativeToChangelogFile="true"/>
    <!-- S6站内信初始化数据脚本 -->
    <include file="sql/132-internalmessage-init.sql" relativeToChangelogFile="true"/>
</databaseChangeLog>
