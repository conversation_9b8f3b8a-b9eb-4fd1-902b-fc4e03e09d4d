package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergyReportDayResultDTO extends EnergyReportTrendResultDTO {
    private Double H0;
    private Double H1;
    private Double H2;
    private Double H3;
    private Double H4;
    private Double H5;
    private Double H6;
    private Double H7;
    private Double H8;
    private Double H9;
    private Double H10;
    private Double H11;
    private Double H12;
    private Double H13;
    private Double H14;
    private Double H15;
    private Double H16;
    private Double H17;
    private Double H18;
    private Double H19;
    private Double H20;
    private Double H21;
    private Double H22;
    private Double H23;

    private Double Total;


    public EnergyReportDayResultDTO(EnergyReportTrendResultDTO dto){
        this.setComplexIndexId(dto.getComplexIndexId());
        this.setObjectName(dto.getObjectName());
        this.setSearchTypeName(dto.getSearchTypeName());
        this.setComplexIndexDefinitionId(dto.getComplexIndexDefinitionId());
        this.setObjectTypeId(dto.getObjectTypeId());
        this.setObjectId(dto.getObjectId());
        this.setUnit(dto.getUnit());
        this.setParentObjectId(dto.getParentObjectId());
        this.setParentObjectName(dto.getParentObjectName());
    }

    //赋值总量
    public void SetTotalConsume(){
        Double tmpTotal =
                (H0 == null ? 0d : H0) +
                (H1 == null ? 0d :  H1) +
                (H2 == null ? 0d :  H2) +
                (H3 == null ? 0d :  H3) +
                (H4 == null ? 0d :  H4) +
                (H5 == null ? 0d :  H5) +
                (H6 == null ? 0d :  H6) +
                (H7 == null ? 0d :  H7) +
                (H8 == null ? 0d :  H8) +
                (H9 == null ? 0d :  H9) +
                (H10 == null ? 0d : H10) +
                (H11 == null ? 0d : H11) +
                (H12 == null ? 0d : H12) +
                (H13 == null ? 0d : H13) +
                (H14 == null ? 0d : H14) +
                (H15 == null ? 0d : H15) +
                (H16 == null ? 0d : H16) +
                (H17 == null ? 0d : H17) +
                (H18 == null ? 0d : H18) +
                (H19 == null ? 0d : H19) +
                (H20 == null ? 0d : H20) +
                (H21 == null ? 0d : H21) +
                (H22 == null ? 0d : H22) +
                (H23 == null ? 0d : H23) ;

        this.setTotal(NumberUtil.doubleAccuracy(tmpTotal,2));
    }
    //赋值平均值
    public void SetAvgConsume(){
        SetTotalConsume();
        Double tempTotal = this.getTotal();
        if (tempTotal == null ||  tempTotal == 0) {
            this.setTotal(0d);
            return;
        }

        Integer tempValidCount = 0;
        if (H0 != null) tempValidCount++;
        if (H1 != null) tempValidCount++;
        if (H2 != null) tempValidCount++;
        if (H3 != null) tempValidCount++;
        if (H4 != null) tempValidCount++;
        if (H5 != null) tempValidCount++;
        if (H6 != null) tempValidCount++;
        if (H7 != null) tempValidCount++;
        if (H8 != null) tempValidCount++;
        if (H9 != null) tempValidCount++;
        if (H10 != null) tempValidCount++;
        if (H11 != null) tempValidCount++;
        if (H12 != null) tempValidCount++;
        if (H13 != null) tempValidCount++;
        if (H14 != null) tempValidCount++;
        if (H15 != null) tempValidCount++;
        if (H16 != null) tempValidCount++;
        if (H17 != null) tempValidCount++;
        if (H18 != null) tempValidCount++;
        if (H19 != null) tempValidCount++;
        if (H20 != null) tempValidCount++;
        if (H21 != null) tempValidCount++;
        if (H22 != null) tempValidCount++;
        if (H23 != null) tempValidCount++;
        this.setTotal(NumberUtil.doubleAccuracy(tempTotal/tempValidCount,2));
    }

}
