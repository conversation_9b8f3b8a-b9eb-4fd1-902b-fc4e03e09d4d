<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-107" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "report"
            (
            "ReportId" INT AUTO_INCREMENT NOT NULL,
            "ReportName" VARCHAR(255),
            "ReportDescription" VARCHAR(255),
            "ReportSchemaId" INT,
            "ReportSchemaCategoryId" INT,
            "UpdateUserId" INT,
            "UpdateTime" TIMESTAMP(0),
            "ReportDataSourceId" INT,
            "MaxQueryInterval" INT,
            "CreateUserId" INT,
            "Overt" TINYINT,
            "ColumnConfig" VARCHAR(8188),
            NOT CLUSTER PRIMARY KEY("ReportId"),
            CHECK("ColumnConfig" IS JSON ));

            CREATE TABLE "reportdatasource"
            (
            "ReportDataSourceId" INT AUTO_INCREMENT NOT NULL,
            "ReportDataSourceName" VARCHAR(128),
            "ReportDataSourceDescription" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ReportDataSourceId"));

            CREATE TABLE "reportexportparameterpreset"
            (
            "ReportExportParameterPresetId" INT AUTO_INCREMENT NOT NULL,
            "ReportId" INT,
            "ReportSchemaExportParameterId" INT,
            "display" TINYINT,
            NOT CLUSTER PRIMARY KEY("ReportExportParameterPresetId"));

            CREATE TABLE "reportparameterpreset"
            (
            "ReportParameterPresetId" INT AUTO_INCREMENT NOT NULL,
            "ReportId" INT,
            "ReportSchemaQueryParameterId" INT,
            "value" CLOB,
            "display" TINYINT,
            NOT CLUSTER PRIMARY KEY("ReportParameterPresetId"));

            CREATE TABLE "reportschema"
            (
            "ReportSchemaId" INT AUTO_INCREMENT NOT NULL,
            "ReportSchemaName" VARCHAR(255),
            "ReportSchemaDescription" VARCHAR(255),
            "Version" VARCHAR(32),
            "ReportSchemaCategoryId" INT,
            "Author" VARCHAR(128),
            "CreateTime" TIMESTAMP(0),
            "ReportDataSourceId" INT,
            "ViewControlId" INT,
            "MaxQueryInterval" INT,
            NOT CLUSTER PRIMARY KEY("ReportSchemaId"));

            CREATE TABLE "reportschemacategory"
            (
            "ReportSchemaCategoryId" INT AUTO_INCREMENT NOT NULL,
            "ReportSchemaCategoryName" VARCHAR(128),
            "ReportSchemaCategoryDescription" VARCHAR(128),
            "ReportSchemaCategoryPath" VARCHAR(128),
            "SortVal" INT,
            NOT CLUSTER PRIMARY KEY("ReportSchemaCategoryId"));

            CREATE TABLE "reportschemaexportparameter"
            (
            "ReportSchemaExportParameterId" INT AUTO_INCREMENT NOT NULL,
            "ReportSchemaExportParameterName" VARCHAR(128),
            "ReportSchemaExportParameterTitle" VARCHAR(128),
            "ReportSchemaId" INT,
            "IsNull" TINYINT,
            NOT CLUSTER PRIMARY KEY("ReportSchemaExportParameterId"));

            CREATE TABLE "reportschemaqueryparameter"
            (
            "ReportSchemaQueryParameterId" INT AUTO_INCREMENT NOT NULL,
            "ReportSchemaQueryParameterName" VARCHAR(128),
            "ReportSchemaQueryParameterTitle" VARCHAR(128),
            "ReportSchemaId" INT,
            "ParameterControlId" INT,
            "DataSourceExpression" TEXT,
            "DataSourceReturnTableName" VARCHAR(255),
            "IsNull" TINYINT,
            "SortIndex" INT,
            NOT CLUSTER PRIMARY KEY("ReportSchemaQueryParameterId"));

            CREATE TABLE "reporttimingtaskfile"
            (
            "ReportTimingTaskFileId" INT AUTO_INCREMENT NOT NULL,
            "ReportTimingTaskManagementId" INT,
            "reportSchemaId" INT,
            "File" CLOB,
            "FilePath" VARCHAR(255),
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("ReportTimingTaskFileId"));

            CREATE OR REPLACE  INDEX "IDX_ReportTimingTaskManagementId_CreateTime" ON "reporttimingtaskfile"("ReportTimingTaskManagementId" ASC,"CreateTime" ASC);

            CREATE TABLE "reporttimingtaskmanagement"
            (
            "ReportTimingTaskManagementId" INT AUTO_INCREMENT NOT NULL,
            "ReportTimingTaskManagementName" VARCHAR(256),
            "ReportId" INT,
            "ReportName" VARCHAR(256),
            "StorageCycle" VARCHAR(64),
            "StartTimeType" VARCHAR(64),
            "EndTimeType" VARCHAR(64),
            "Status" TINYINT,
            "to" VARCHAR(1024),
            "cc" VARCHAR(1024),
            "CreateUserId" INT,
            "Overt" TINYINT,
            NOT CLUSTER PRIMARY KEY("ReportTimingTaskManagementId"));

            CREATE TABLE "reporttimingtasktimetype"
            (
            "ReportTimingTaskTimeTypeId" INT AUTO_INCREMENT NOT NULL,
            "TimeTypeName" VARCHAR(45),
            "TimeTypeValue" VARCHAR(45),
            NOT CLUSTER PRIMARY KEY("ReportTimingTaskTimeTypeId"));

            CREATE TABLE "reportfolder" (
            "folderId" INT AUTO_INCREMENT NOT NULL,
            "folderName" VARCHAR(255) NOT NULL,
            "parentId" INT DEFAULT 0,
            "sortIndex" INT DEFAULT NULL,
            "createTime" TIMESTAMP DEFAULT NULL,
            NOT CLUSTER PRIMARY KEY ("folderId")
            );

            CREATE TABLE "reportfoldermap" (
            "id" INT AUTO_INCREMENT NOT NULL,
            "reportId" INT NOT NULL,
            "folderId" INT NOT NULL,
            "sortIndex" INT DEFAULT NULL,
            NOT CLUSTER PRIMARY KEY ("id"),
            CONSTRAINT "uniqReportFolder" UNIQUE ("reportId", "folderId")
            );
        </sql>
    </changeSet>
</databaseChangeLog>