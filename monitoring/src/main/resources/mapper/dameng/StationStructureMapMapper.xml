<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.StationStructureMapMapper">
    <select id="findStationStructureNameMap" resultType="com.siteweb.utility.dto.IdValueDTO">
        SELECT b.StationId AS value, a.StructureName AS label
        FROM tbl_stationstructure a
                 INNER JOIN tbl_stationstructuremap b ON a.StructureId = b.StructureId
    </select>
</mapper>