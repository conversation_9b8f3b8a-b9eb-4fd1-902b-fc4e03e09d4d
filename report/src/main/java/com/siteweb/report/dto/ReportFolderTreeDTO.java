package com.siteweb.report.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 报表文件夹树形结构
 */
@Data
public class ReportFolderTreeDTO {
    /**
     * id
     */
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * "folder" 或 "report"
     */
    private String type;
    /**
     * 排序值
     */
    private Integer sortIndex;

    private List<ReportFolderTreeDTO> children = new ArrayList<>();
}