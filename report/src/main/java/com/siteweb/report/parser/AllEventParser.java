package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.dto.AllEventDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.AllEventParam;
import com.siteweb.report.service.TotalEventService;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Set;

/**
 * 所有告警报表
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Component
public class AllEventParser extends ReportParser {
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    TotalEventService totalEventService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    protected AllEventParser() {
        super(ReportDataSourceEnum.ALL_EVENT.getValue());
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.serialNo"));
        defaultColumns.put(AllEventDTO.Fields.eventSeverity, messageSourceUtil.getMessage("common.report.form.eventSeverity"));
        defaultColumns.put(AllEventDTO.Fields.equipmentBaseName, messageSourceUtil.getMessage("common.report.form.baseEquipmentName"));
        defaultColumns.put(AllEventDTO.Fields.equipmentName, messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(AllEventDTO.Fields.equipmentCategoryName, messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(AllEventDTO.Fields.meanings, messageSourceUtil.getMessage("eventNotification.activeEvent.meanings"));
        defaultColumns.put(AllEventDTO.Fields.startTime, messageSourceUtil.getMessage("common.report.form.startTime"));
        defaultColumns.put(AllEventDTO.Fields.endTime, messageSourceUtil.getMessage("common.report.form.endTime"));
        defaultColumns.put(AllEventDTO.Fields.reversalNum, messageSourceUtil.getMessage("eventNotification.activeEvent.reversalNum"));
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.duration"));
        defaultColumns.put(AllEventDTO.Fields.resourceStructureId, messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(AllEventDTO.Fields.confirmTime, messageSourceUtil.getMessage("common.report.form.confirmTime"));
        defaultColumns.put(AllEventDTO.Fields.description, messageSourceUtil.getMessage("common.report.form.Comment"));
        defaultColumns.put(AllEventDTO.Fields.confirmerName, messageSourceUtil.getMessage("common.report.form.confirmerName"));
        defaultColumns.put(AllEventDTO.Fields.eventValue, messageSourceUtil.getMessage("common.report.form.eventValue"));
        defaultColumns.put(AllEventDTO.Fields.eventName, messageSourceUtil.getMessage("common.report.form.eventName"));
        return defaultColumns;
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        AllEventParam param = new AllEventParam(reportVO.getReportParameterPresetList());
        JSONObject thJsonObject = new JSONObject(true);
        JSONArray bodyJsonObject = this.getTableBody(reportVO.getUserId(),param,reportVO.getPageable(),thJsonObject);
        // 添加表头
        JSONObject tableHead = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        thJsonObject.set(ReportStructureEnum.TITLE.value(), tableHead);
        thJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return thJsonObject;
    }

    private JSONArray getTableBody(Integer userId, AllEventParam param, Pageable pageable,JSONObject thJsonObject) {
        JSONArray jsonArray = new JSONArray();
        //设备过滤
        if (CollUtil.isEmpty(param.getEquipmentIds())) {
            Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
            param.setEquipmentIds(new ArrayList<>(equipmentIds));
        }
        Page<AllEventDTO> allEventList = null;
        int index = 1;
        if (ObjectUtil.isNotNull(pageable)) {
            index = (pageable.getPageNumber() * pageable.getPageSize()) + 1;
            Page<AllEventDTO> page = new Page<>(pageable.getPageNumber() + 1L, pageable.getPageSize(),false);
            allEventList = totalEventService.findAllEvent(param, page);
            //查询总数
            page.setTotal(totalEventService.findAllEventCount(param));
            thJsonObject.set(ReportStructureEnum.TOTALPAGES.value(), page.getPages());
            thJsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), page.getTotal());
        }else{
            Page<AllEventDTO> page = new Page<>(1, -1,false);
            allEventList = totalEventService.findAllEvent(param,page);
        }
        for (AllEventDTO event : allEventList.getRecords()) {
            JSONObject tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), index++);
            tdJsonObject.set(AllEventDTO.Fields.eventSeverity, event.getEventSeverity());
            tdJsonObject.set(AllEventDTO.Fields.equipmentName, event.getEquipmentName());
            tdJsonObject.set(AllEventDTO.Fields.resourceStructureId, resourceStructureManager.getFullPath(event.getResourceStructureId()));
            tdJsonObject.set(AllEventDTO.Fields.equipmentBaseName, event.getEquipmentBaseName());
            tdJsonObject.set(AllEventDTO.Fields.equipmentCategoryName, event.getEquipmentCategoryName());
            tdJsonObject.set(AllEventDTO.Fields.eventName, event.getEventName());
            tdJsonObject.set(AllEventDTO.Fields.eventValue, ReportParamParserUtils.getTwoDecimal(event.getEventValue()));
            tdJsonObject.set(AllEventDTO.Fields.meanings, event.getMeanings());
            tdJsonObject.set(AllEventDTO.Fields.startTime, DateUtil.dateToStringAndValidIsNull(event.getStartTime()));
            tdJsonObject.set(AllEventDTO.Fields.confirmTime, DateUtil.dateToStringAndValidIsNull(event.getConfirmTime()));
            tdJsonObject.set(AllEventDTO.Fields.confirmerName, event.getConfirmerName());
            tdJsonObject.set(AllEventDTO.Fields.endTime, DateUtil.dateToStringAndValidIsNull(event.getEndTime()));
            tdJsonObject.set(AllEventDTO.Fields.reversalNum, event.getReversalNum());
            tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), DateUtil.getTimeDifference(event.getStartTime(), event.getEndTime()));
            tdJsonObject.set(AllEventDTO.Fields.description, event.getDescription());

            jsonArray.add(tdJsonObject);
        }
        return jsonArray;
    }

}
