<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="william wu" id="History event table" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_HistoryEvent"  remarks="历史告警表">
            <column name="SequenceId" type="VARCHAR(128)" remarks="流水号">
                <constraints nullable="false" primaryKey="true" unique="true"/>
            </column>
            <column name="StationId" type="INT" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="StationName" type="VARCHAR(255)" remarks="局站名">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="INT" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentName" type="VARCHAR(128)" remarks="设备名">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="INT" remarks="事件ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventName" type="VARCHAR(128)" remarks="事件名">
                <constraints nullable="false"/>
            </column>
            <column name="EventConditionId" type="INT" remarks="条件ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventSeverityId" type="INT" remarks="告警等级ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventSeverity" type="VARCHAR(128)" remarks="告警等级名">
                <constraints nullable="false"/>
            </column>
            <column name="EventLevel" type="INT" remarks="IDC场景告警等级ID">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime" remarks="开始时间">
                <constraints nullable="false"/>
            </column>
            <column name="EndTime" type="datetime" remarks="结束时间">
                <constraints nullable="true"/>
            </column>
            <column name="CancelTime" type="datetime" remarks="取消时间">
                <constraints nullable="true"/>
            </column>
            <column name="CancelUserId" type="INT" remarks="取消人Id">
                <constraints nullable="true"/>
            </column>
            <column name="CancelUserName" type="VARCHAR(128)" remarks="取消人名">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmTime" type="datetime" remarks="确认时间">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmerId" type="INT" remarks="确认人ID">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmerName" type="VARCHAR(128)" remarks="确认任命">
                <constraints nullable="true"/>
            </column>
            <column name="EventValue" type="DOUBLE" remarks="触发值">
                <constraints nullable="true"/>
            </column>
            <column name="ReversalNum" type="INT" remarks="翻转次数">
                <constraints nullable="true"/>
            </column>
            <column name="Meanings" type="VARCHAR(255)" remarks="告警涵义">
                <constraints nullable="true"/>
            </column>
            <column name="EventFilePath" type="VARCHAR(255)" remarks="告警录像文件路径">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="VARCHAR(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="SourceHostId" type="INT" remarks="源主机Id">
                <constraints nullable="true"/>
            </column>
            <column name="InstructionId" type="VARCHAR(255)" remarks="派单号">
                <constraints nullable="true"/>
            </column>
            <column name="InstructionStatus" type="INT" remarks="派单状态">
                <constraints nullable="true"/>
            </column>
            <column name="StandardAlarmNameId" type="INT" remarks="告警标准化Id">
                <constraints nullable="true"/>
            </column>
            <column name="StandardAlarmName" type="VARCHAR(128)" remarks="告警标准名">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeId" type="DECIMAL(12)" remarks="基类ID">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeName" type="VARCHAR(128)" remarks="基类名">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentCategory" type="INT" remarks="设备种类ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentCategoryName" type="VARCHAR(128)" remarks="设备种类名">
                <constraints nullable="true"/>
            </column>
            <column defaultValueNumeric="0" name="MaintainState" type="INT" remarks="工程状态">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="INT" remarks="对应信号Id">
                <constraints nullable="true"/>
            </column>
            <column name="RelateSequenceId" type="VARCHAR(128)" remarks="关联告警流水号">
                <constraints nullable="true"/>
            </column>
            <column name="EventCategoryId" type="INT" remarks="告警分类ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventStateId" type="INT" remarks="告警工程状态">
                <constraints nullable="false"/>
            </column>
            <column name="CenterId" type="INT" remarks="中心ID">
                <constraints nullable="true"/>
            </column>
            <column name="CenterName" type="VARCHAR(128)" remarks="中心名">
                <constraints nullable="true"/>
            </column>
            <column name="StructureName" type="VARCHAR(128)" remarks="分组名">
                <constraints nullable="true"/>
            </column>
            <column name="MonitorUnitName" type="VARCHAR(128)" remarks="监控单元名称">
                <constraints nullable="true"/>
            </column>
            <column name="StructureId" type="INT" remarks="分组ID">
                <constraints nullable="true"/>
            </column>
            <column name="StationCategoryId" type="INT" remarks="局站类型">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentVendor" type="VARCHAR(128)" remarks="设备厂商">
                <constraints nullable="true"/>
            </column>
            <column name="EndValue" type="DOUBLE" remarks="结束触发值">
                <constraints nullable="true"/>
            </column>
            <column name="ConvergenceEventId" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="ResourceStructureId" type="INT" defaultValue="0" remarks="层级ID">
                <constraints nullable="true"/>
            </column>
            <column name="BaseEquipmentId" type="INT" defaultValue="0" remarks="设备基类ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="TBL_HistoryEvent" indexName="TBL_HistoryEvent_IDX1" unique="true">
            <column name="StartTime"></column>
            <column name="StationId"></column>
            <column name="EquipmentId"></column>
            <column name="EventId"></column>
            <column name="EventConditionId"></column>
        </createIndex>
        <createIndex tableName="TBL_HistoryEvent" indexName="TBL_HistoryEvent_IDX2" unique="false">
            <column name="StructureId"></column>
            <column name="StationCategoryId"></column>
            <column name="EquipmentCategory"></column>
            <column name="StationId"></column>
            <column name="EventStateId"></column>
            <column name="EventCategoryId"></column>
            <column name="EventConditionId"></column>
            <column name="StartTime"></column>
        </createIndex>
        <createIndex tableName="TBL_HistoryEvent" indexName="TBL_HistoryEvent_IDX3" unique="false">
            <column name="StartTime"></column>
            <column name="BaseTypeId"></column>
            <column name="StationId"></column>
            <column name="EquipmentId"></column>
            <column name="EventSeverityId"></column>
            <column name="SignalId"></column>
        </createIndex>

        <createIndex tableName="TBL_HistoryEvent" indexName="TBL_HistoryEvent_IDX4" unique="false">
            <column name="ConvergenceEventId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>