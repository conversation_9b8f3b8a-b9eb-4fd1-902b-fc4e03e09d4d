<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.complexindex.mapper.ComplexIndexMapper">

    <resultMap type="com.siteweb.complexindex.entity.ComplexIndex" id="ComplexIndexMap">
        <result property="complexIndexId" column="ComplexIndexId" jdbcType="INTEGER"/>
        <result property="complexIndexName" column="ComplexIndexName" jdbcType="VARCHAR"/>
        <result property="complexIndexDefinitionId" column="ComplexIndexDefinitionId" jdbcType="INTEGER"/>
        <result property="objectId" column="ObjectId" jdbcType="INTEGER"/>
        <result property="objectName" column="ObjectName" jdbcType="VARCHAR"/>
        <result property="calcCron" column="CalcCron" jdbcType="VARCHAR"/>
        <result property="calcType" column="CalcType" jdbcType="INTEGER"/>
        <result property="afterCalc" column="AfterCalc" jdbcType="VARCHAR"/>
        <result property="saveCron" column="SaveCron" jdbcType="VARCHAR"/>
        <result property="expression" column="Expression" jdbcType="VARCHAR"/>
        <result property="unit" column="Unit" jdbcType="VARCHAR"/>
        <result property="accuracy" column="Accuracy" jdbcType="VARCHAR"/>
        <result property="objectTypeId" column="ObjectTypeId" jdbcType="INTEGER"/>
        <result property="remark" column="Remark" jdbcType="VARCHAR"/>
        <result property="label" column="Label" jdbcType="VARCHAR"/>
        <result property="businessTypeId" column="BusinessTypeId" jdbcType="INTEGER"/>
        <result property="checkExpression" column="CheckExpression" jdbcType="VARCHAR"/>
        <result property="businessTypeName" column="BusinessTypeName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="baseSql">
        <trim prefixOverrides=",">
            ComplexIndexId, ComplexIndexName, ComplexIndexDefinitionId, ObjectId, CalcCron, CalcType, AfterCalc, SaveCron, Expression, Unit, Accuracy, ObjectTypeId, Remark, Label, BusinessTypeId, CheckExpression
        </trim>
    </sql>

    <select id="selectList" resultMap="ComplexIndexMap">
        select * from (
            select t1.*, t3.BusinessTypeName from complexindex t1
            LEFT JOIN complexindexbusinesstype t3 ON t1.BusinessTypeId = t3.BusinessTypeId
        ) t
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="findByObjectIdAndObjectTypeId" resultMap="ComplexIndexMap">
        SELECT a.ComplexIndexId, a.ComplexIndexName, a.ObjectId, a.CalcCron, a.CalcType, -- 5
               a.AfterCalc, a.SaveCron, a.Expression, a.Unit, a.Accuracy, a.ObjectTypeId, -- 11
               a.Remark, a.Label, a.BusinessTypeId, a.ComplexIndexDefinitionId, b.BusinessTypeName, -- 16
               a.CheckExpression
        FROM complexindex a
            LEFT JOIN complexindexbusinesstype b ON a.BusinessTypeId = b.BusinessTypeId
            LEFT JOIN complexindexdefinition c ON a.ComplexIndexDefinitionId = c.ComplexIndexDefinitionId
        WHERE a.ObjectId = #{objectId,jdbcType=INTEGER}
            AND a.objectTypeId = #{objectTypeId,jdbcType=INTEGER}
    </select>

    <insert id="insertBatch" keyProperty="complexIndexId" useGeneratedKeys="true">
        insert into complexindex(ComplexIndexName, ComplexIndexDefinitionId, ObjectId, CalcCron, CalcType, AfterCalc, SaveCron, Expression, Unit, Accuracy, ObjectTypeId, Remark, Label, BusinessTypeId, CheckExpression)
        values
        <foreach collection="complexIndexs" item="entity" separator=",">
            (#{entity.complexIndexName}, #{entity.complexIndexDefinitionId}, #{entity.objectId}, #{entity.calcCron}, #{entity.calcType}, #{entity.afterCalc}, #{entity.saveCron}, #{entity.expression}, #{entity.unit}, #{entity.accuracy}, #{entity.objectTypeId}, #{entity.remark}, #{entity.label}, #{entity.businessTypeId}, #{entity.checkExpression})
        </foreach>
    </insert>

    <update id="updateBatch" keyProperty="complexIndexId" useGeneratedKeys="true">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update complexindex
            <set>
                ComplexIndexName = #{item.complexIndexName},
                ObjectId = #{item.objectId},
                Label = #{item.label},
                CalcCron = #{item.calcCron},
                CalcType = #{item.calcType},
                AfterCalc = #{item.afterCalc},
                SaveCron = #{item.saveCron},
                Expression = #{item.expression},
                Unit = #{item.unit},
                Accuracy = #{item.accuracy},
                ObjectTypeId = #{item.objectTypeId},
                Remark = #{item.remark},
                ComplexIndexDefinitionId = #{item.complexIndexDefinitionId},
                BusinessTypeId = #{item.businessTypeId},
                CheckExpression = #{item.checkExpression}
            </set>
            where ComplexIndexId = #{item.complexIndexId}
        </foreach>
    </update>

    <select id="findComplexIndexOfObjectToManualRecord" resultType="com.siteweb.complexindex.entity.ComplexIndex">
        select a.*,b.BusinessTypeName from complexindex a
        inner join complexindexbusinesstype b on a.BusinessTypeId = b.BusinessTypeId where a.ObjectId=#{objectId} and a.ComplexIndexDefinitionId in (
        select ComplexIndexDefinitionId from energy_businessdefinitionmap where ComplexIndexDefinitionTypeId in (1,3)
        ) ;
    </select>
</mapper>

