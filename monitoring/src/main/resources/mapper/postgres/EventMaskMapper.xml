<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EventMaskMapper">

    <select id="findEventMaskByEventId" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT StationId,
               EquipmentId,
               EventId,
               StartTime,
               EndTime,
               Reason,
               UserId,
               TimeGroupId
        FROM TBL_EventMask
        WHERE StationId= #{stationId} and EquipmentId = #{equipmentId} and EventId = #{eventId}
    </select>
    <select id="findEventMaskByEquipmentId" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT StationId, EquipmentId,EventId, StartTime, EndTime, Reason, UserId FROM TBL_EventMask
        WHERE StationId= #{stationId} and EquipmentId = #{equipmentId}
    </select>
    <select id="findSimpleEventMaskDTOsByEquipmentId"
            resultType="com.siteweb.monitoring.dto.SimpleEventMaskDTO">
        SELECT b.StationId, b.EquipmentId, a.EventId, a.EventName, CASE WHEN c.EventId IS NOT NULL THEN 1 ELSE 0 END AS mask,
        CASE WHEN c.EndTime &gt;= now() and c.StartTime &lt;= now() THEN 1 ELSE 0 END AS effective
        FROM tbl_event a INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        LEFT JOIN tbl_eventmask c ON a.EventId = c.EventId AND b.StationId = c.StationId AND b.EquipmentId = c.EquipmentId
        WHERE b.EquipmentId = #{equipmentId}
    </select>
    <select id="findEventMaskDTOById" resultType="com.siteweb.monitoring.dto.EventMaskDTO">
        SELECT a.*,b.UserName,c.EquipmentName,d.EventName,fuct_GetDevicePosition(c.ResourceStructureId) as EquipmentPosition
        FROM tbl_eventmask a LEFT JOIN tbl_account b ON a.userId = b.userId
        LEFT JOIN tbl_equipment c ON a.StationId = c.StationId AND a.EquipmentId = c.EquipmentId
        LEFT JOIN tbl_event d ON c.EquipmentTemplateId = d.EquipmentTemplateId AND a.EventId = d.EventId
        WHERE a.StationId = #{stationId} AND a.EquipmentId = #{equipmentId} AND a.EventId = #{eventId}
    </select>

    <parameterMap id="opSaveEventMaskMap" type="java.util.HashMap">
        <parameter property="StationId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EventId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="StartTime" mode="IN" jdbcType="TIMESTAMP"></parameter>
        <parameter property="EndTime" mode="IN" jdbcType="TIMESTAMP"></parameter>
        <parameter property="UserId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="Reason" mode="IN" jdbcType="VARCHAR"></parameter>
    </parameterMap>

    <parameterMap id="opSaveSeparateEventMaskMap" type="java.util.HashMap">
        <parameter property="StationId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EventId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="TimeMaskChar" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="Week" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="UserId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="Reason" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="ret" mode="OUT" jdbcType="INTEGER"></parameter>
    </parameterMap>
    <select id="findByTimeGroupIds" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT * FROM tbl_eventmask WHERE TimeGroupId IN
        <foreach collection="timeGroupIds" item="timeGroupId" open="(" close=")" separator=",">
            #{timeGroupId}
        </foreach>
    </select>
    <sql id="findEventMaskByKeywordsPageSql">
        SELECT
        a.EquipmentId,
        a.StationId,
        a.EventId,
        a.TimeGroupId,
        a.Reason,
        a.StartTime,
        a.EndTime,
        a.UserId,
        b.UserName,
        c.EquipmentName,
        e.EventName,
        fuct_GetDevicePosition(c.ResourceStructureId) AS EquipmentPosition
        FROM tbl_eventmask a
        LEFT JOIN tbl_account b ON a.userId = b.userId
        LEFT JOIN tbl_equipment c ON a.StationId = c.StationId AND a.EquipmentId = c.EquipmentId
        LEFT JOIN tbl_equipmenttemplate d ON c.EquipmentTemplateId = d.EquipmentTemplateId
        LEFT JOIN tbl_event e ON d.EquipmentTemplateId = e.EquipmentTemplateId AND a.EventId = e.EventId
        <where>
            <if test="eventMaskFilterDTO.keywords != null and eventMaskFilterDTO.keywords != ''">
                AND (c.EquipmentName LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(c.ResourceStructureId) LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR e.EventName like CONCAT('%', #{eventMaskFilterDTO.keywords},'%'))
            </if>
            AND a.equipmentId IN
            <foreach collection="eventMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </where>
        order by
        <choose>
            <when test="eventMaskFilterDTO.field != null and eventMaskFilterDTO.field != ''">
                ${eventMaskFilterDTO.field}
                <if test="eventMaskFilterDTO.order != null and eventMaskFilterDTO.order != ''">
                    ${eventMaskFilterDTO.order}
                </if>
            </when>
            <otherwise>
                a.StartTime desc
            </otherwise>
        </choose>
    </sql>
    <select id="findEventMaskByKeywordsPage" resultType="com.siteweb.monitoring.dto.EventMaskDTO">
        <include refid="findEventMaskByKeywordsPageSql"/>
    </select>
    <select id="findEventMaskByKeywordsPageCount" resultType="java.lang.Long">
        SELECT count(*) from (<include refid="findEventMaskByKeywordsPageSql"/>) as countTable
    </select>

    <sql id="findEventMaskByBaseTypeIdsPageSql">
        SELECT DISTINCT b.StationId, b.EquipmentId, a.EventId, a.EventName, b.equipmentName,d.resourcestructureName,
        CASE WHEN c.EventId IS NOT NULL THEN 1 ELSE
        0 END AS mask,
        CASE WHEN c.EndTime &gt;= now() and c.StartTime &lt;= now() THEN 1 ELSE 0 END AS effective,
        e.EventSeverity
        FROM tbl_event a INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        LEFT JOIN tbl_eventmask c ON a.EventId = c.EventId AND b.StationId = c.StationId AND b.EquipmentId = c.EquipmentId
        INNER JOIN resourcestructure d on d.ResourceStructureId = b.ResourceStructureId
        LEFT JOIN tbl_eventcondition e ON e.EventId = a.EventId and a.EquipmentTemplateId = e.EquipmentTemplateId
        LEFT JOIN tbl_equipmenttemplate f ON f.EquipmentTemplateId = b.EquipmentTemplateId
        <where>
            <if test="eventMaskFilterDTO.eventName != null and eventMaskFilterDTO.eventName != ''">
                AND a.EventName like CONCAT('%', #{eventMaskFilterDTO.eventName},'%')
            </if>
            <if test="eventMaskFilterDTO.resourceStructureIdList != null and eventMaskFilterDTO.resourceStructureIdList.size >0">
                AND d.ResourceStructureId IN
                <foreach collection="eventMaskFilterDTO.resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.equipmentBaseTypeIdList != null and eventMaskFilterDTO.equipmentBaseTypeIdList.size > 0">
                AND f.EquipmentBaseType IN
                <foreach collection="eventMaskFilterDTO.equipmentBaseTypeIdList" item="equipmentBaseTypeId" open="(" close=")" separator=",">
                    #{equipmentBaseTypeId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.equipmentCategoryIdList != null and eventMaskFilterDTO.equipmentCategoryIdList.size > 0">
                AND b.EquipmentCategory IN
                <foreach collection="eventMaskFilterDTO.equipmentCategoryIdList" item="equipmentCategoryId" open="(" close=")" separator=",">
                    #{equipmentCategoryId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.baseTypeIdList != null and eventMaskFilterDTO.baseTypeIdList.size > 0">
                AND e.baseTypeId IN
                <foreach collection="eventMaskFilterDTO.baseTypeIdList" item="baseTypeId" open="(" close=")" separator=",">
                    #{baseTypeId}
                </foreach>
            </if>
            AND b.equipmentId IN
            <foreach collection="eventMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
            <if test="eventMaskFilterDTO.keywords != null and eventMaskFilterDTO.keywords != ''">
                AND (b.EquipmentName LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(d.ResourceStructureId) LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR a.EventName like CONCAT('%', #{eventMaskFilterDTO.keywords},'%'))
            </if>
        </where>
        order by
        <choose>
            <when test="eventMaskFilterDTO.field != null and eventMaskFilterDTO.field != ''">
                ${eventMaskFilterDTO.field}
                <if test="eventMaskFilterDTO.order != null and eventMaskFilterDTO.order != ''">
                    ${eventMaskFilterDTO.order}
                </if>
            </when>
            <otherwise>
                c.StartTime desc
            </otherwise>
        </choose>
    </sql>
    <select id="findSimpleEventMaskByBaseTypeIdsPage" resultType="com.siteweb.monitoring.dto.ConditionEventMaskDTO">
        <include refid="findEventMaskByBaseTypeIdsPageSql"/>
    </select>
    <select id="findEventMaskByBaseTypeIdsPageCount" resultType="java.lang.Long">
        SELECT count(*) FROM (<include refid="findEventMaskByBaseTypeIdsPageSql"/>) countTable
    </select>
    <select id="findEventMaskByEquipmentEventIdIdList" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT equipmentid, stationid, eventid, timegroupid, reason, starttime, endtime, userid FROM tbl_EventMask
        WHERE
        <foreach collection="equipmentEventIdList" item="equipmentEventId" open="(" close=")" separator=" or ">
          (EquipmentId = #{equipmentEventId.equipmentId} AND eventid = #{equipmentEventId.eventId})
        </foreach>
    </select>
    <select id="findEventMaskByBaseTypeIds" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT b.EquipmentId, b.StationId, a.EventId, c.TimeGroupId, c.Reason, c.StartTime, c.EndTime, c.UserId
        FROM tbl_event a INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        INNER JOIN tbl_eventmask c ON a.EventId = c.EventId AND b.StationId = c.StationId AND b.EquipmentId = c.EquipmentId
        INNER JOIN resourcestructure d on d.ResourceStructureId = b.ResourceStructureId
        LEFT JOIN tbl_eventcondition e ON e.EventId = a.EventId and a.EquipmentTemplateId = e.EquipmentTemplateId
        LEFT JOIN tbl_equipmenttemplate f ON f.EquipmentTemplateId = b.EquipmentTemplateId
        <where>
            <if test="eventMaskFilterDTO.eventName != null and eventMaskFilterDTO.eventName != ''">
                AND a.EventName like CONCAT('%', #{eventMaskFilterDTO.eventName},'%')
            </if>
            <if test="eventMaskFilterDTO.resourceStructureIdList != null and eventMaskFilterDTO.resourceStructureIdList.size >0">
                AND d.ResourceStructureId IN
                <foreach collection="eventMaskFilterDTO.resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.equipmentBaseTypeIdList != null and eventMaskFilterDTO.equipmentBaseTypeIdList.size > 0">
                AND f.EquipmentBaseType IN
                <foreach collection="eventMaskFilterDTO.equipmentBaseTypeIdList" item="equipmentBaseTypeId" open="(" close=")" separator=",">
                    #{equipmentBaseTypeId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.baseTypeIdList != null and eventMaskFilterDTO.baseTypeIdList.size > 0">
                AND e.baseTypeId IN
                <foreach collection="eventMaskFilterDTO.baseTypeIdList" item="baseTypeId" open="(" close=")" separator=",">
                    #{baseTypeId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.equipmentCategoryIdList != null and eventMaskFilterDTO.equipmentCategoryIdList.size > 0">
                AND b.EquipmentCategory IN
                <foreach collection="eventMaskFilterDTO.equipmentCategoryIdList" item="equipmentCategoryId" open="(" close=")" separator=",">
                    #{equipmentCategoryId}
                </foreach>
            </if>
            AND b.equipmentId IN
            <foreach collection="eventMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
            <if test="eventMaskFilterDTO.keywords != null and eventMaskFilterDTO.keywords != ''">
                AND (b.EquipmentName LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(d.ResourceStructureId) LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR a.EventName like CONCAT('%', #{eventMaskFilterDTO.keywords},'%'))
            </if>
        </where>
    </select>
    <select id="findEventMaskCreateByBaseTypeIds" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT b.EquipmentId, b.StationId, a.EventId
        FROM tbl_event a INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        INNER JOIN resourcestructure d on d.ResourceStructureId = b.ResourceStructureId
        LEFT JOIN tbl_eventcondition e ON e.EventId = a.EventId and a.EquipmentTemplateId = e.EquipmentTemplateId
        LEFT JOIN tbl_equipmenttemplate f ON f.EquipmentTemplateId = b.EquipmentTemplateId
        <where>
            <if test="eventMaskFilterDTO.eventName != null and eventMaskFilterDTO.eventName != ''">
                AND a.EventName like CONCAT('%', #{eventMaskFilterDTO.eventName},'%')
            </if>
            <if test="eventMaskFilterDTO.resourceStructureIdList != null and eventMaskFilterDTO.resourceStructureIdList.size >0">
                AND d.ResourceStructureId IN
                <foreach collection="eventMaskFilterDTO.resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.equipmentBaseTypeIdList != null and eventMaskFilterDTO.equipmentBaseTypeIdList.size > 0">
                AND f.EquipmentBaseType IN
                <foreach collection="eventMaskFilterDTO.equipmentBaseTypeIdList" item="equipmentBaseTypeId" open="(" close=")" separator=",">
                    #{equipmentBaseTypeId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.baseTypeIdList != null and eventMaskFilterDTO.baseTypeIdList.size > 0">
                AND e.baseTypeId IN
                <foreach collection="eventMaskFilterDTO.baseTypeIdList" item="baseTypeId" open="(" close=")" separator=",">
                    #{baseTypeId}
                </foreach>
            </if>
            <if test="eventMaskFilterDTO.equipmentCategoryIdList != null and eventMaskFilterDTO.equipmentCategoryIdList.size > 0">
                AND b.EquipmentCategory IN
                <foreach collection="eventMaskFilterDTO.equipmentCategoryIdList" item="equipmentCategoryId" open="(" close=")" separator=",">
                    #{equipmentCategoryId}
                </foreach>
            </if>
            AND b.equipmentId IN
            <foreach collection="eventMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
            <if test="eventMaskFilterDTO.keywords != null and eventMaskFilterDTO.keywords != ''">
                AND (b.EquipmentName LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(d.ResourceStructureId) LIKE CONCAT('%', #{eventMaskFilterDTO.keywords}, '%')
                OR a.EventName like CONCAT('%', #{eventMaskFilterDTO.keywords},'%'))
            </if>
        </where>
    </select>
    <select id="findMaxTimeGroupId" resultType="java.lang.Integer">
        SELECT MAX(TimeGroupId)
        FROM tbl_eventmask
    </select>
    <select id="findTimeGroupId" resultType="java.lang.Integer">
        SELECT TimeGroupId
        FROM TBL_EventMask
        WHERE StationId = #{stationId}
          AND EquipmentId = #{equipmentId}
          AND EventId = #{eventId};
    </select>
    <select id="findByEventMaksIdentities" resultType="com.siteweb.monitoring.entity.EventMask">
        SELECT equipmentid, stationid, eventid, timegroupid, reason, starttime, endtime, userid
        FROM tbl_eventmask WHERE
        <if test="eventMaskIdentityDTOList != null and eventMaskIdentityDTOList.size() > 0">
            (StationId, EquipmentId, EventId) IN
            <foreach collection="eventMaskIdentityDTOList" item="item" separator="," open="(" close=")">
                (#{item.stationId}, #{item.equipmentId}, #{item.eventId})
            </foreach>
        </if>
    </select>

    <delete id="deleteEventMask">
        DELETE FROM TBL_TimeGroupSpan WHERE TimeGroupId IN ( SELECT TimeGroupId FROM TBL_EventMask WHERE StationId = #{stationId} and EquipmentId = #{equipmentId} and EventId = #{eventId} ) AND length(TimeSpanChar) = 12;
        DELETE FROM TBL_EventMask WHERE StationId = #{stationId} and EquipmentId = #{equipmentId} and EventId = #{eventId};
    </delete>
    <delete id="deleteByEventMaksIdentities">
        DELETE FROM tbl_eventmask a
        WHERE (a.StationId, a.EquipmentId, a.EventId) IN
        <foreach collection="eventMaskIdentityDTOList" item="item" separator="," open="(" close=")">
            (#{item.stationId}, #{item.equipmentId}, #{item.eventId})
        </foreach>
    </delete>
</mapper>