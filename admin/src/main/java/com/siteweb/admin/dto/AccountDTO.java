package com.siteweb.admin.dto;


import com.siteweb.common.enums.SensitiveType;
import com.siteweb.common.sensitive.jackson.SensitiveInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class AccountDTO {

    private Integer userId;

    @SensitiveInfo(value = SensitiveType.CHINESE_NAME)
    private String userName;

    private String logonId;

    @SensitiveInfo(value = SensitiveType.PASSWORD)
    private String password;

    private boolean enable;

    private Integer maxError;

    private boolean locked;

    private Date validTime;

    private String description;

    private boolean remote;

    private Integer centerId;

    private Date passwordValidTime;

    private String avatar;

    private String themeName;

    private Boolean needResetPwd;

    private String roleIds;

    private String alias;

    private String loginType;

    private Boolean frozen;
}
