package com.siteweb.report.dto;

import cn.hutool.core.convert.TypeConverter;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.lang.reflect.Type;
import java.util.Date;

@Data
@FieldNameConstants
public class HistoryEventReportDTO implements TypeConverter {
    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 设备位置
     */
    private String equipmentPosition;
    /**
     * 设备基类名
     */
    private String baseEquipmentName;
    /**
     * 设备类型名称
     */
    private String equipmentCategoryName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 触发值
     */
    private Double eventValue;
    /**
     * 告警含义
     */
    private String meanings;
    /**
     * 翻转次数
     */
    private Integer reversalNum;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 确认人名
     */
    private String confirmerName;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 取消人名
     */
    private String cancelUserName;


    @Override
    public Object convert(Type targetType, Object value) {
        String eventValueStr = String.valueOf(ReportParamParserUtils.getTwoDecimal(eventValue));
        String startTimeStr = DateUtil.dateToStringAndValidIsNull(startTime);
        String confirmTimeStr = DateUtil.dateToStringAndValidIsNull(confirmTime);
        String endTimeStr = DateUtil.dateToStringAndValidIsNull(endTime);
        String timeDifference = DateUtil.getTimeDifference(startTime, endTime);
        return new String[]{eventSeverity, equipmentName, equipmentPosition, baseEquipmentName,equipmentCategoryName, eventName, eventValueStr, meanings, startTimeStr, confirmTimeStr, confirmerName, endTimeStr, cancelUserName, timeDifference,description};
    }
}
