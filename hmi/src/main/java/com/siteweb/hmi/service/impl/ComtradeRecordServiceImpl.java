package com.siteweb.hmi.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.hmi.entity.ComtradeRecord;
import com.siteweb.hmi.service.ComtradeRecordService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.EquipmentDetail;
import com.siteweb.monitoring.dto.ResourceStructureEquipmentTreeDTO;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/5/20
 */
@Slf4j
@Service
public class ComtradeRecordServiceImpl implements ComtradeRecordService {

    @Autowired
    private EquipmentService equipmentService;
    
    @Autowired
    private ResourceStructureService resourceStructureService;
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    @Autowired
    private ConfigSignalManager configSignalManager;
    
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public List<ComtradeRecord> getComtradeRecordsByEquipmentId(Integer equipmentId, String startTime, String endTime) {
        List<ComtradeRecord> records = new ArrayList<>();
        String localPath = "upload-dir/comtrade/" + equipmentId;
        File directory = new File(localPath);

        if (!directory.exists() || !directory.isDirectory()) {
            return records;
        }

        // 获取所有.cfg文件
        File[] cfgFiles = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(".cfg"));
        if (cfgFiles == null) {
            return records;
        }

        // 获取设备信息
        EquipmentDetail equipmentDetail = equipmentService.getEquipmentDetail(equipmentId);

        if (equipmentDetail == null) {
            return records;
        }
        
        // 解析开始时间和结束时间
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        
        if (startTime != null && !startTime.isEmpty()) {
            try {
                startDateTime = LocalDateTime.parse(startTime, DATETIME_FORMATTER);
            } catch (DateTimeParseException e) {
                try {
                    startDateTime = LocalDateTime.parse(startTime + " 00:00:00", DATETIME_FORMATTER);
                } catch (DateTimeParseException e2) {
                    // 解析失败，忽略筛选条件
                }
            }
        }
        
        if (endTime != null && !endTime.isEmpty()) {
            try {
                endDateTime = LocalDateTime.parse(endTime, DATETIME_FORMATTER);
            } catch (DateTimeParseException e) {
                try {
                    endDateTime = LocalDateTime.parse(endTime + " 23:59:59", DATETIME_FORMATTER);
                } catch (DateTimeParseException e2) {
                    // 解析失败，忽略筛选条件
                }
            }
        }

        for (File cfgFile : cfgFiles) {
            String fileName = cfgFile.getName();
            // 移除.cfg后缀
            String baseName = fileName.substring(0, fileName.length() - 4);
            
            // 解析时间字符串（假设格式为：yyyy-M-d-H-m-s-SSS）
            String[] parts = baseName.split("_")[0].split("-");
            if (parts.length >= 7) {
                try {
                    // 构建标准格式的时间字符串
                    String formattedTime = String.format("%04d-%02d-%02d %02d:%02d:%02d.%s",
                        Integer.parseInt(parts[0]), // year
                        Integer.parseInt(parts[1]), // month
                        Integer.parseInt(parts[2]), // day
                        Integer.parseInt(parts[3]), // hour
                        Integer.parseInt(parts[4]), // minute
                        Integer.parseInt(parts[5]), // second
                        parts[6]  // milliseconds
                    );
                    
                    // 为了时间过滤，解析记录时间（忽略毫秒部分，因为过滤条件不包含毫秒）
                    String timeForCompare = String.format("%04d-%02d-%02d %02d:%02d:%02d",
                        Integer.parseInt(parts[0]), // year
                        Integer.parseInt(parts[1]), // month
                        Integer.parseInt(parts[2]), // day
                        Integer.parseInt(parts[3]), // hour
                        Integer.parseInt(parts[4]), // minute
                        Integer.parseInt(parts[5])  // second
                    );
                    
                    LocalDateTime recordDateTime = null;
                    try {
                        recordDateTime = LocalDateTime.parse(timeForCompare, DATETIME_FORMATTER);
                    } catch (DateTimeParseException e) {
                        // 如果解析失败，跳过时间筛选
                    }
                    
                    // 根据时间范围过滤
                    if (recordDateTime != null) {
                        if (startDateTime != null && recordDateTime.isBefore(startDateTime)) {
                            continue; // 记录时间早于开始时间，跳过
                        }
                        if (endDateTime != null && recordDateTime.isAfter(endDateTime)) {
                            continue; // 记录时间晚于结束时间，跳过
                        }
                    }

                    ComtradeRecord record = new ComtradeRecord();
                    record.setFileName(baseName);
                    record.setTriggerTime(formattedTime);
                    record.setEquipmentId(equipmentId);
                    record.setEquipmentName(equipmentDetail.getEquipmentName());
                    record.setEquipmentPosition(equipmentDetail.getEquipmentPosition());
                    
                    records.add(record);
                } catch (NumberFormatException e) {
                    // 如果解析失败，跳过该记录
                    continue;
                }
            }
        }

        return records;
    }

    @Override
    public ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentTreeForComtrade() {
        return getResourceStructureEquipmentTreeBySignalBaseTypeIds();
    }
    
    /**
     * 根据信号基类ID获取资源结构设备树
     *
     * @return {@link ResourceStructureEquipmentTreeDTO} 层级树
     */
    private ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentTreeBySignalBaseTypeIds(){
        // 获取所有层级
        List<ResourceStructureEquipmentTreeDTO> resourceStructures = resourceStructureService.findResourceStructures()
                .stream()
                .map(ResourceStructureEquipmentTreeDTO::new)
                .toList();
        
        List<EquipmentDTO> filteredEquipmentList;
        
        // 通过信号基类ID获取设备列表
        List<Long> signalBaseTypeIds = getComtradeSignalBaseTypeIds();
        log.info("获取到COMTRADE信号基类ID: {}", signalBaseTypeIds);
        
        // 获取匹配的信号配置
        List<ConfigSignalItem> configSignals = configSignalManager.getConfigSignalsByBaseTypeIds(signalBaseTypeIds);
        log.info("获取到匹配的信号配置数量: {}", configSignals.size());
        
        // 从信号配置中提取设备模板ID
        List<Integer> equipmentTemplateIds = configSignals.stream()
                .map(ConfigSignalItem::getEquipmentTemplateId)
                .distinct()
                .collect(Collectors.toList());
        log.info("获取到设备模板ID集合: {}", equipmentTemplateIds);
        
        // 通过设备模板ID获取设备列表
        filteredEquipmentList = equipmentService.findEquipmentsByEquipmentTemplateIds(equipmentTemplateIds);
        
        // 过滤只保留有设备关联的层级结构
        List<ResourceStructureEquipmentTreeDTO> filteredResourceStructures = filterResourceStructuresWithEquipment(resourceStructures, filteredEquipmentList);
        
        return buildTree(filteredResourceStructures, filteredEquipmentList);
    }
    
    /**
     * 构建资源结构设备树
     * @param resourceStructureList 资源结构列表
     * @param equipmentList 设备列表
     * @return {@link ResourceStructureEquipmentTreeDTO}
     */
    private ResourceStructureEquipmentTreeDTO buildTree(List<ResourceStructureEquipmentTreeDTO> resourceStructureList, List<EquipmentDTO> equipmentList) {
        return resourceStructureService.buildTree(resourceStructureList, equipmentList);
    }
    
    /**
     * 过滤资源结构，只保留有设备关联的层级结构
     * 
     * @param resourceStructures 所有资源结构列表
     * @param equipmentList 设备列表
     * @return 过滤后的资源结构列表
     */
    private List<ResourceStructureEquipmentTreeDTO> filterResourceStructuresWithEquipment(
            List<ResourceStructureEquipmentTreeDTO> resourceStructures, 
            List<EquipmentDTO> equipmentList) {
        
        if (equipmentList == null || equipmentList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取所有有设备的层级ID
        List<Integer> resourceIdsWithEquipment = equipmentList.stream()
                .map(EquipmentDTO::getRId)
                .distinct()
                .collect(Collectors.toList());
        
        // 递归查找所有需要保留的层级（包括有设备的层级和它们的所有父级）
        List<Integer> resourceIdsToKeep = new ArrayList<>();
        for (Integer rId : resourceIdsWithEquipment) {
            addResourceAndParents(rId, resourceStructures, resourceIdsToKeep);
        }
        
        // 过滤资源结构列表
        return resourceStructures.stream()
                .filter(rs -> resourceIdsToKeep.contains(rs.getRId()))
                .collect(Collectors.toList());
    }
    
    /**
     * 递归添加资源结构及其所有父级
     * 
     * @param resourceId 资源结构ID
     * @param resourceStructures 所有资源结构列表
     * @param resourceIdsToKeep 需要保留的资源结构ID列表
     */
    private void addResourceAndParents(Integer resourceId, 
            List<ResourceStructureEquipmentTreeDTO> resourceStructures, 
            List<Integer> resourceIdsToKeep) {
        
        if (resourceId == null || resourceIdsToKeep.contains(resourceId)) {
            return;
        }
        
        // 找到当前资源结构
        ResourceStructureEquipmentTreeDTO currentResource = resourceStructures.stream()
                .filter(rs -> rs.getRId().equals(resourceId))
                .findFirst()
                .orElse(null);
        
        if (currentResource != null) {
            resourceIdsToKeep.add(resourceId);
            
            // 递归添加父级
            if (currentResource.getParentId() != null && currentResource.getParentId() != 0) {
                addResourceAndParents(currentResource.getParentId(), resourceStructures, resourceIdsToKeep);
            }
        }
    }
    
    /**
     * 从系统配置中获取COMTRADE信号基类ID列表
     *
     * @return COMTRADE信号基类ID列表
     */
    private List<Long> getComtradeSignalBaseTypeIds() {
        try {
            SystemConfig config = systemConfigService.findBySystemConfigKey("comtrade.signal.basetypeids");
            if (config == null || config.getSystemConfigValue() == null) {
                log.warn("未找到COMTRADE信号基类ID配置或配置值为空，使用默认值");
                return Arrays.asList(1L, 2L, 3L);
            }
            
            String baseTypeIdsConfig = config.getSystemConfigValue();
            if (CharSequenceUtil.isNotBlank(baseTypeIdsConfig)) {
                return Arrays.stream(baseTypeIdsConfig.split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("从系统配置获取COMTRADE信号基类ID失败，使用默认值", e);
        }
        // 如果获取失败，返回默认值
        return Arrays.asList(1L, 2L, 3L);
    }

}
