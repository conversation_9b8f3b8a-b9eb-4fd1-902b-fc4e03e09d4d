package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.MenuStructureDTO;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.entity.MenuItemStructureMap;
import com.siteweb.admin.entity.MenuStructure;
import com.siteweb.admin.mapper.MenuItemMapper;
import com.siteweb.admin.mapper.MenuStructureMapper;
import com.siteweb.admin.service.MenuItemService;
import com.siteweb.admin.service.MenuItemStructureMapService;
import com.siteweb.admin.service.MenuStructureService;
import com.siteweb.admin.service.PermissionService;
import com.siteweb.admin.vo.MenuStructureVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class MenuStructureServiceImpl implements MenuStructureService {

    @Autowired
    MenuStructureMapper menuStructureMapper;
    @Autowired
    MenuItemService menuItemService;
    @Autowired
    private MenuItemStructureMapService menuItemStructureMapService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    MenuItemMapper menuItemMapper;

    @Override
    public List<MenuStructure> findMenuStructures() {
        return menuStructureMapper.findAllMenuStructures();
    }

    @Override
    public int createMenuStructure(MenuStructure menuStructure) {
         menuStructureMapper.insert(menuStructure);
         return menuStructure.getMenuStructureId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int createMenuStructure(List<MenuStructureVO> menuStructureVOList) {
        for (MenuStructureVO menuStructureVO : menuStructureVOList) {
            //创建目录
            if (ObjectUtil.isNull(menuStructureVO.getMenuItemId())) {
                MenuStructure menuStructure = BeanUtil.toBean(menuStructureVO, MenuStructure.class);
                menuStructureMapper.insert(menuStructure);
                continue;
            }
            //添加菜单项与目录的映射关系
            MenuItemStructureMap menuItemStructureMap = BeanUtil.toBean(menuStructureVO, MenuItemStructureMap.class);
            menuItemStructureMapService.createMenuItemStructureMap(menuItemStructureMap);
            //添加权限点
            this.createPermission(menuItemStructureMap);
        }
        return menuStructureVOList.size();
    }


    /**
     * 添加权限点
     * @param menuItemStructureMap
     */
    void createPermission(MenuItemStructureMap menuItemStructureMap) {
        //获取完整路径
        String fullId = this.findFullId(menuItemStructureMap.getMenuProfileId(), menuItemStructureMap.getMenuItemId());
        //获取完整名称
        String fullTitle = this.findFullTitle(menuItemStructureMap.getMenuProfileId(), menuItemStructureMap.getMenuItemId());
        permissionService.createMenuPermission(menuItemStructureMap.getMenuProfileId(), fullId, fullTitle);
        //如果有子节点需查找其子节点一并添加权限
        List<MenuItem> childMenuItem = menuItemService.findByParentId(menuItemStructureMap.getMenuItemId());
        for (MenuItem menuItem : childMenuItem) {
            MenuItemStructureMap childrenMenuItemStructureMap = BeanUtil.toBean(menuItemStructureMap, MenuItemStructureMap.class);
            childrenMenuItemStructureMap.setMenuItemId(menuItem.getMenuItemId());
            this.createPermission(childrenMenuItemStructureMap);
        }
    }

    @Override
    public String findFullId(Integer menuProfileId, Integer menuItemId){
        MenuItem menuItem = menuItemService.findById(menuItemId);
        StringBuilder fullId = new StringBuilder(menuItem.getMenuItemId().toString());
        while (ObjectUtil.notEqual(menuItem.getParentId(), 1)) {
            fullId.insert(0, menuItem.getParentId() + "-");
            menuItem = menuItemService.findById(menuItem.getParentId());
        }
        //查找目录Id
        MenuItemStructureMap menuItemStructureMap = menuItemStructureMapService.findMenuItemStructureMapByCondition(menuProfileId, null, menuItem.getMenuItemId());
        if (ObjectUtil.isNull(menuItemStructureMap)) {
            return fullId.toString();
        }
        if (ObjectUtil.equals(menuItemStructureMap.getMenuStructureId(), 0)) {
            fullId.insert(0, "0-");
            return fullId.toString();
        }
        MenuStructure menuStructure = menuStructureMapper.findMenuStructureById(menuItemStructureMap.getMenuStructureId());
        fullId.insert(0, menuStructure.getMenuStructureId() + "-");
        while (ObjectUtil.notEqual(menuStructure.getParentId(), 0)) {
            fullId.insert(0, menuStructure.getParentId() + "-");
            menuStructure =  this.findById(menuStructure.getParentId());
        }
        return fullId.toString();
    }

    public String findFullTitle(Integer menuProfileId, Integer menuItemId){
        MenuItem menuItem = menuItemService.findById(menuItemId);
        StringBuilder fullTitle = new StringBuilder(menuItem.getTitle());
        while (ObjectUtil.notEqual(menuItem.getParentId(), 1)) {
            menuItem = menuItemService.findById(menuItem.getParentId());
            fullTitle.insert(0, menuItem.getTitle() + "-");
        }
        //查找目录名称
        MenuStructure menuStructure = menuStructureMapper.findStructureTitleByMenuIdAndProfileId(menuProfileId, menuItem.getMenuItemId());
        if (ObjectUtil.isNull(menuStructure)) {
            return fullTitle.toString();
        }
        fullTitle.insert(0, menuStructure.getTitle() + "-");
        while (ObjectUtil.notEqual(menuStructure.getParentId(), 0)) {
            menuStructure =  this.findById(menuStructure.getParentId());
            fullTitle.insert(0, menuStructure.getTitle() + "-");
        }
        return fullTitle.toString();
    }

    @Override
    public int deleteById(Integer menuStructureId) {
        return menuStructureMapper.deleteById(menuStructureId);
    }

    @Override
    public int updateMenuStructure(MenuStructureVO menuStructureVO) {
        //更新目录
        if (ObjectUtil.isNull(menuStructureVO.getMenuItemStructureMapId())) {
            MenuStructure menuStructure = BeanUtil.toBean(menuStructureVO, MenuStructure.class);
            return menuStructureMapper.updateById(menuStructure);
        }
        //更新菜单项
        MenuItemStructureMap menuItemStructureMap = BeanUtil.toBean(menuStructureVO, MenuItemStructureMap.class);
        return menuItemStructureMapService.createMenuItemStructureMap(menuItemStructureMap);
    }

    @Override
    public MenuStructure findById(Integer menuStructureId) {
        return menuStructureMapper.findMenuStructureById(menuStructureId);
    }

    @Override
    public List<MenuStructure> findByIds(List<Integer> menuStructureIds) {
        return menuStructureMapper.selectBatchIds(menuStructureIds);
    }

    @Override
    public List<MenuStructure> findTopMenuStructuresByMenuProfileId(Integer menuProfileId) {
        return menuStructureMapper.findTopMenuStructuresByMenuProfileId(menuProfileId);
    }

    @Override
    public List<MenuStructureDTO> getParentMenuStructureByProfileId(Integer menuProfileId) {
        List<MenuStructure> menuStructureList = menuStructureMapper.findByMenuProfileIdAndParentId(menuProfileId, 0);
        List<MenuStructureDTO> menuStructureDTOList = copyMenuStructureTree(menuStructureList);
        menuStructureDTOList.addAll(getFirstLevelMenuItem(menuProfileId));
        return menuStructureDTOList.stream()
                                   .sorted(Comparator.comparing(MenuStructureDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                                   .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenuStructure(MenuStructureVO menuStructureVO) {
        if (ObjectUtil.isNull(menuStructureVO)) {
            return;
        }
        //目录映射关系不为空
        if (ObjectUtil.isNotNull(menuStructureVO.getMenuItemStructureMapId())) {
            MenuItemStructureMap menuItemStructureMap = menuItemStructureMapService.findById(menuStructureVO.getMenuItemStructureMapId());
            //删除权限相关数据
            deletePermission(menuItemStructureMap);
            menuItemStructureMapService.deleteById(menuItemStructureMap.getMenuItemStructureMapId());
            return;
        }
        //目录id不为空
        if (ObjectUtil.isNotNull(menuStructureVO.getMenuStructureId())) {
            this.deleteById(menuStructureVO.getMenuStructureId());
        }
    }

    @Override
    public int deleteByMenuProfileId(Integer menuProfileId) {
        return menuStructureMapper.delete(Wrappers.lambdaQuery(MenuStructure.class)
                                           .eq(MenuStructure::getMenuProfileId, menuProfileId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateMenuStructureSort(List<MenuStructureVO> menuStructureVOList) {
        for (MenuStructureVO menuStructureVO : menuStructureVOList) {
            if (menuStructureVO.getParentId() != null && menuStructureVO.getParentId() != 0) {
                menuItemMapper.update(null, Wrappers.lambdaUpdate(MenuItem.class)
                        .set(MenuItem::getSortIndex, menuStructureVO.getSortIndex())
                        .eq(MenuItem::getMenuItemId, menuStructureVO.getMenuItemId()));
            } else {
                menuStructureMapper.update(null, Wrappers.lambdaUpdate(MenuStructure.class)
                        .set(MenuStructure::getSortIndex, menuStructureVO.getSortIndex())
                        .eq(MenuStructure::getMenuStructureId, menuStructureVO.getMenuStructureId()));
            }
        }
        return menuStructureVOList.size();
    }


    private void deletePermission(MenuItemStructureMap menuItemStructureMap) {
        String fullId = this.findFullId(menuItemStructureMap.getMenuProfileId(), menuItemStructureMap.getMenuItemId());
        permissionService.deleteMenuPermissionByFullId(menuItemStructureMap.getMenuProfileId(), fullId);
        //如果有子节点需查找其子节点一并添加权限
        List<MenuItem> childMenuItem = menuItemService.findByParentId(menuItemStructureMap.getMenuItemId());
        for (MenuItem menuItem : childMenuItem) {
            MenuItemStructureMap childrenMenuItemStructureMap = BeanUtil.toBean(menuItemStructureMap, MenuItemStructureMap.class);
            childrenMenuItemStructureMap.setMenuItemId(menuItem.getMenuItemId());
            this.deletePermission(childrenMenuItemStructureMap);
        }
    }

    private List<MenuStructureDTO> getFirstLevelMenuItem(Integer menuProfileId) {
        List<MenuItemStructureMap> firstLevelMenuItemMaps = menuItemStructureMapService.findByMenuStructureIdAndMenuProfileId(0,menuProfileId);
        return getMenuStructureChildrenNode(firstLevelMenuItemMaps);
    }
    private List<MenuStructureDTO> copyMenuStructureTree(List<MenuStructure> menuStructureList) {
        List<MenuStructureDTO> menuStructureDTOList = new ArrayList<>();
            for (MenuStructure menuStructure : menuStructureList) {
                MenuStructureDTO menuStructureDTO = new MenuStructureDTO();
                BeanUtils.copyProperties(menuStructure, menuStructureDTO);
                menuStructureDTO.setUuid(UUID.randomUUID().toString());
                List<MenuItemStructureMap> sortedMenuItemStructureMaps = menuStructure.getMenuItemStructureMaps().stream().sorted(Comparator.comparing(MenuItemStructureMap::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                                                                                      .toList();
                List<MenuStructureDTO> childrens = getMenuStructureChildrenNode(sortedMenuItemStructureMaps);
                if (CollUtil.isNotEmpty(menuStructure.getChildren())) {
                    List<MenuStructure> sortedChildren = menuStructure.getChildren().stream()
                                                                      .sorted(Comparator.comparing(MenuStructure::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                                                                      .toList();
                    List<MenuStructureDTO> subChildrens = copyMenuStructureTree(sortedChildren);
                    if (!subChildrens.isEmpty()) {
                        childrens.addAll(subChildrens);
                    }
                }
                List<MenuStructureDTO> sortedChildrens = childrens.stream()
                                                                  .sorted(Comparator.comparing(MenuStructureDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                                                                  .toList();
                menuStructureDTO.setChildren(sortedChildrens);
                menuStructureDTOList.add(menuStructureDTO);

            }
        return menuStructureDTOList;
    }

    private List<MenuStructureDTO> getMenuStructureChildrenNode(List<MenuItemStructureMap> menuItemStructureMaps) {
        List<MenuStructureDTO> menuStructureDTOList = new ArrayList<>();
        if (menuItemStructureMaps.isEmpty()) {
            return menuStructureDTOList;
        }
        for (MenuItemStructureMap menuItemStructureMap : menuItemStructureMaps) {
            MenuItem menuItem = menuItemService.findById(menuItemStructureMap.getMenuItemId());
            MenuStructureDTO menuStructureDTO = new MenuStructureDTO();
            if (menuItem != null) {
                BeanUtils.copyProperties(menuItemStructureMap, menuStructureDTO);
                BeanUtils.copyProperties(menuItem, menuStructureDTO);
                menuStructureDTO.setUuid(UUID.randomUUID().toString());
                menuStructureDTO.setChildren(generateMenuStructureDTOFromMenuItem(menuItem.getChildren()));
                menuStructureDTOList.add(menuStructureDTO);
            }
        }
        return menuStructureDTOList;
    }
    private List<MenuStructureDTO> generateMenuStructureDTOFromMenuItem(List<MenuItem> menuItemList){
        if(menuItemList.isEmpty()){
            return Collections.emptyList();
        }
        List<MenuStructureDTO> menuStructureDTOList = new ArrayList<>();
        for(MenuItem menuItem: menuItemList){
            MenuStructureDTO menuStructureDTO = new MenuStructureDTO();
            BeanUtils.copyProperties(menuItem, menuStructureDTO);
            menuStructureDTO.setUuid(UUID.randomUUID().toString());
            menuStructureDTO.setReadOnly(true);
            List<MenuStructureDTO> childrens = generateMenuStructureDTOFromMenuItem(menuItem.getChildren());
            if(!childrens.isEmpty()) {
                List<MenuStructureDTO> sortedChildrens = childrens.stream()
                                                                  .sorted(Comparator.comparing(MenuStructureDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                                                                  .toList();
                menuStructureDTO.setChildren(sortedChildrens);
            }
            menuStructureDTOList.add(menuStructureDTO);
        }
        return menuStructureDTOList;
    }
}
