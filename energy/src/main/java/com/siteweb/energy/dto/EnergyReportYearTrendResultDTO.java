package com.siteweb.energy.dto;
import com.siteweb.common.util.NumberUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class EnergyReportYearTrendResultDTO extends EnergyReportTrendResultDTO{
    private Double M1;
    private Double M2;
    private Double M3;
    private Double M4;
    private Double M5;
    private Double M6;
    private Double M7;
    private Double M8;
    private Double M9;
    private Double M10;
    private Double M11;
    private Double M12;
    private Double Total;

    public EnergyReportYearTrendResultDTO(EnergyReportTrendResultDTO dto){
        this.setComplexIndexId(dto.getComplexIndexId());
        this.setObjectName(dto.getObjectName());
        this.setSearchTypeName(dto.getSearchTypeName());
        this.setComplexIndexDefinitionId(dto.getComplexIndexDefinitionId());
        this.setObjectTypeId(dto.getObjectTypeId());
        this.setObjectId(dto.getObjectId());
        this.setUnit(dto.getUnit());
        this.setClassificationName(dto.getClassificationName());
        this.setClassificationId(dto.getClassificationId());
        this.setComplexIndexName(dto.getComplexIndexName());
        this.setParentObjectId(dto.getParentObjectId());
        this.setParentObjectName(dto.getParentObjectName());
    }

    //赋值总量
    public void SetTotalConsume(){
        Double tmpTotal =
                (M1 == null ? 0d : M1) +
                        (M2 == null ? 0d : M2) +
                        (M3 == null ? 0d : M3) +
                        (M4 == null ? 0d : M4) +
                        (M5 == null ? 0d : M5) +
                        (M6 == null ? 0d : M6) +
                        (M7 == null ? 0d : M7) +
                        (M8 == null ? 0d : M8) +
                        (M9 == null ? 0d : M9) +
                        (M10 == null ? 0d : M10) +
                        (M11 == null ? 0d : M11) +
                        (M12 == null ? 0d : M12);
        this.setTotal(NumberUtil.doubleAccuracy(tmpTotal,2));
    }

    //赋值平均值
    public void SetAvgConsume(){
        SetTotalConsume();
        Double tempTotal = this.getTotal();
        if (tempTotal == null ||  tempTotal == 0) {
            this.setTotal(0d);
            return;
        }

        Integer tempValidCount = 0;
        if (M1 != null) tempValidCount++;
        if (M2 != null) tempValidCount++;
        if (M3 != null) tempValidCount++;
        if (M4 != null) tempValidCount++;
        if (M5 != null) tempValidCount++;
        if (M6 != null) tempValidCount++;
        if (M7 != null) tempValidCount++;
        if (M8 != null) tempValidCount++;
        if (M9 != null) tempValidCount++;
        if (M10 != null) tempValidCount++;
        if (M11 != null) tempValidCount++;
        if (M12 != null) tempValidCount++;

        this.setTotal(NumberUtil.doubleAccuracy(tempTotal/tempValidCount,2));
    }
}
