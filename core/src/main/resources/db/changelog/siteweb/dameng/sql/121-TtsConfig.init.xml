<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-121" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(1, 'alarmtts.enable', 'false', '是否启用网页版TTS语音告警通知');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(2, 'notificationhost.alarm.severity', '','触发告警通知的告警等级（多个告警等级之间以半角逗号隔开）');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(3, 'notificationhost.alarm.status', '', '触发告警通知的告警状态（1为告警开始，2为告警结束）');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(4, 'tts.circularbroadcast.enable', 'false', 'TTS是否循环播报活动告警');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(5, 'tts.confirmnobroadcast.enable', 'false', 'TTS是否已确认告警不播报');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(6, 'tts.firstpushalarm.enable', 'true', 'TTS不播报登录时的活动告警');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(7, 'tts.message.contentTemplate', '', 'TTS语音通知告警消息内容模板');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(8, 'tts.message.endAlarmContentTemplate', '', 'TTS语音通知告警结束消息内容模板');
            INSERT INTO "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") VALUES(9, 'tts.speakRepeatTimes', '1', 'TTS语音单条告警消息播报次数');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(10,'tts.filter.position','','触发tts语音的层级id（多个层级之间以半角逗号隔开）');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(11,'tts.filter.baseType','','触发tts语音的告警基类id（多个告警基类之间以半角逗号隔开）');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(12,'tts.filter.baseEquipmentId','','触发tts语音的设备基类id（多个设备基类之间以半角逗号隔开）');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(13,'tts.filter.event','','触发tts语音的告警id（多个告警之间以半角逗号隔开）');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(14,'tts.filter.equipment','','触发tts语音的设备id（多个设备之间以半角逗号隔开）');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(15,'tts.endnobroadcast.enable','','TTS是否已结束告警不播报');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(16,'tts.projectstatus.enable','','工程状态');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(17,'tts.speakRate','1','TTS语音通知播报语速');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(18,'tts.speakVolume','1','TTS语音通知播报音量');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(19,'tts.filter.keyWord','','TTS语音通知播报关键字过滤');
            insert into "ttsconfig" ("TtsConfigId","TtsConfigKey","TtsConfigValue","Description") values(20,'tts.sortby.eventlevel','false','TTS是否按照告警等级优先播报');

        </sql>
    </changeSet>
</databaseChangeLog>