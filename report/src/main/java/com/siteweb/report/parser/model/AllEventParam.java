package com.siteweb.report.parser.model;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.dto.EquipmentEventIdDTO;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Slf4j
public class AllEventParam {
    /**
     * 设备id
     */
    private List<Integer> equipmentIds;
    /**
     * 设备基类ID
     */
    private Integer baseEquipmentId;
    /**
     * 设备类型ids
     */
    private List<Integer> equipmentCategories;
    /**
     * 告警等级id
     */
    private List<Integer> eventLevels;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 确认人id
     */
    private List<Integer> confirmIds;
    /**
     * 事件过滤符合条件【设备id.事件id】多个用|分隔
     */
    private List<EquipmentEventIdDTO> equipmentEventIdDTOS;
    public AllEventParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "baseEquipmentIds":
                        baseEquipmentId = ReportParamParserUtils.strToInteger(reportParameterPreset.getValue());
                        break;
                    case "equipmentCategories":
                        equipmentCategories = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_CATEGORY_ID);
                        break;
                    case "equipmentIds":
                        equipmentIds = StringUtils.splitToIntegerList(ReportParamParserUtils.jsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID));
                        break;
                    case "eventLevels":
                        eventLevels= StringUtils.splitToIntegerList(reportParameterPreset.getValue());
                        break;
                    case "eventName":
                        eventName = reportParameterPreset.getValue();
                        break;
                    case "keyword":
                        keyword = reportParameterPreset.getValue();
                        break;
                    case "confirmIds":
                        confirmIds = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    case "eventIds":
                        equipmentEventIdDTOS = JSONUtil.toList(reportParameterPreset.getValue(),EquipmentEventIdDTO.class);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 ActiveEventFilterVOBuild 异常：", e);
            throw e;
        }
    }
}
