/*报表模板分类*/
INSERT INTO `reportschemacategory` VALUES (1, '告警', '告警', 'alarm', 1);
INSERT INTO `reportschemacategory` VALUES (2, '历史数据', '历史数据', 'history', 2);
INSERT INTO `reportschemacategory` VALUES (3, '指标', '指标', 'complexindex', 3);
INSERT INTO `reportschemacategory` VALUES (4, '日志', '日志', 'log', 4);
INSERT INTO `reportschemacategory` VALUES (5, '其他', '其他', 'others', 5);
INSERT INTO `reportschemacategory` VALUES (6, '电池', '电池', 'battery', 6);
INSERT INTO `reportschemacategory` VALUES (7, '审计', '审计', 'audit', 7);
INSERT INTO `reportschemacategory` VALUES (8, '空调群控', '基站空调群控报表', 'airconditioner', 8);

/*报表模板*/
INSERT INTO `reportschema` VALUES (1, '当前告警报表', '展示所需查询当前告警报表数据结果', '1.0.0', 1, 'admin', NOW(), 1, 1,null);
INSERT INTO `reportschema` VALUES (2, '历史告警报表', '展示所需查询历史告警报表数据结果', '1.0.0', 1, 'admin', NOW(), 2, 1,null);
INSERT INTO `reportschema` VALUES (3, '历史数据报表', '展示所需查询历史数据报表数据结果', '1.0.0', 2, 'admin', NOW(), 3, 1,null);
INSERT INTO `reportschema` VALUES (7, '告警操作日志报表(设备)', '展示所需查询告警操作日志报表数据结果', '1.0.0', 4, 'admin', NOW(), 7, 1,null);
INSERT INTO `reportschema` VALUES (8, '告警操作日志报表(告警事件)', '展示所需查询告警操作日志报表数据结果', '1.0.0', 4, 'admin', NOW(), 7, 1,null);
INSERT INTO `reportschema` VALUES (9, '历史放电报表', '展示所需查询设备的历史放电结构', '1.0.0', 6, 'admin', NOW(), 8, 1,null);
INSERT INTO `reportschema` VALUES (10, '历史指标报表', '展示所需查询历史指标报表数据结果', '1.0.0', 3, 'admin', NOW(), 9, 1,null);
INSERT INTO `reportschema` VALUES (13, '控制记录报表', '展示所需查询的控制记录', '1.0.0', 5, 'admin', NOW(), 11, 1,null);
-- INSERT INTO `reportschema` VALUES (14, '即时报表', '展示所需查询的实时数据', '1.0.0', 5, 'admin', NOW(), 12, 1,null);
INSERT INTO `reportschema` VALUES (15, '告警数量统计报表', '展示所需查询的告警数量统计', '1.0.0', 1, 'admin', NOW(), 12, 1,null);
INSERT INTO `reportschema` VALUES (16, '用电统计报表', '展示所需查询的用电情况统计', '1.0.0', 2, 'admin', NOW(), 21, 1,null);
INSERT INTO `reportschema` VALUES (18, '通用定制报表', '展示所需查询的每日用电量情况统计', '1.0.0', 2, 'admin', NOW(), 22, 1,null);
INSERT INTO `reportschema` VALUES (23, '告警屏蔽报表', '展示所需查询告警屏蔽报表数据结果', '1.0.0', 4, 'admin', NOW(), 15, 1,null);
INSERT INTO `reportschema` VALUES (24, '用户操作日志报表', '用户的操作日志包含登录及配置更改等操作', '1.0.0', 4, 'admin', NOW(), 4, 1,null);
INSERT INTO `reportschema` VALUES (25, '门禁刷卡记录日志', '展示所需查询门禁刷卡记录报表数据结果', '1.0.0', 4, 'admin', NOW(), 16, 1,null);
INSERT INTO `reportschema` VALUES (26, '告警通知发送记录报表', '查询告警通知发送记录', '1.0.0', 1, 'admin', NOW(), 19, 1,null);
INSERT INTO `reportschema` VALUES (27, '历史预警数据查询报表', '展示所需查询历史预警数据结果', '1.0.0', 2, 'admin', NOW(), 20, 1,null);
INSERT INTO `reportschema` VALUES (28, '历史数据报表(5分钟存储)', '展示所需查询历史数据报表数据结果', '1.0.0', 2, 'admin', NOW(), 3, 1,null);
INSERT INTO `reportschema` VALUES (29, '机架变更记录报表', '展示所需查询机架变更记录结果', '1.0.0', 2, 'admin', NOW(), 23, 1,null);
INSERT INTO `reportschema` VALUES (30, '审计报表', '展示所需查询审计记录', '1.0.0', 7, 'admin', NOW(), 24, 1,null);
INSERT INTO `reportschema` VALUES (31, '安全日志报表', '展示所需查询安全日志记录', '1.0.0', 7, 'admin', NOW(), 25, 1,null);
INSERT INTO `reportschema` VALUES (32, '设备历史运行状态报表', '展示所需查询空调群控设备下属设备历史运行状态', '1.0.0', 8, 'admin', NOW(), 26, 1,48);
INSERT INTO `reportschema` VALUES (33, '批量控制分组变更报表', '展示批量控制分组的变更日志记录', '1.0.0', 8, 'admin', NOW(), 27, 1,null);
INSERT INTO `reportschema` VALUES (34, '批量下发控制命令历史记录报表', '展示历史批量下发过的控制命令记录', '1.0.0', 8, 'admin', NOW(), 28, 1,null);
INSERT INTO `reportschema` VALUES (35, '群控设备分组变更报表', '展示群控设备分组及相关联数据变更日志记录', '1.0.0', 8, 'admin', NOW(), 29, 1,null);
-- INSERT INTO `reportschema` VALUES (36, 'BA控制命令报表', '展示BA下发指令并同步到s6的控制记录', '1.0.0', 4, 'admin', NOW(), 30, 1,null);
INSERT INTO `reportschema` VALUES (37, '所有告警报表', '展示所需查询所有告警报表据结果', '1.0.0', 4, 'admin', NOW(), 31, 1,null);
INSERT INTO `reportschema` VALUES (38, '设备报表', '展示所需查询的设备清单', '1.0.0', 5, 'admin', NOW(), 32, 1,null);
INSERT INTO `reportschema` VALUES (39, '历史数据报表(原始数据)', '展示所需查询历史数据报表数据结果', '1.0.0', 2, 'admin', NOW(), 33, 1,null);
INSERT INTO `reportschema` VALUES (40, '告警分类统计报表', '展示所需查询的异常告警和施工告警数量统计', '1.0.0', 1, 'admin', NOW(),34, 1,null);
INSERT INTO reportschema VALUES (41, '设备串口信息报表', '展示所需查询的设备串口信息数据', '1.0.0', 5, 'admin', NOW(), 35, 1,null);

/*报表数据源*/
INSERT INTO `reportdatasource` VALUES (1, '当前告警', 'live-events');
INSERT INTO `reportdatasource` VALUES (2, '历史告警', 'historyevents');
INSERT INTO `reportdatasource` VALUES (3, '历史数据', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (4, '操作日志', 'userlogs');
INSERT INTO `reportdatasource` VALUES (6, '维护绩效', 'maintenanceperformances');
INSERT INTO `reportdatasource` VALUES (7, '历史数据', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (8, '历史放电', 'batterydischargerecord');
INSERT INTO `reportdatasource` VALUES (9, '历史指标', 'historyComplexIndex');
INSERT INTO `reportdatasource` VALUES (10, '历史数据', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (11, '控制记录', 'historycommandviews');
INSERT INTO `reportdatasource` VALUES (12, '告警数量', 'live-events+historyevents');
INSERT INTO `reportdatasource` VALUES (13, '日常巡检', 'currenthealthystate');
INSERT INTO `reportdatasource` VALUES (14, '综合告警', 'totalevents');
INSERT INTO `reportdatasource` VALUES (15, '告警屏蔽', 'alarmmask');
INSERT INTO `reportdatasource` VALUES (16, '门禁刷卡', 'cardswipe');
INSERT INTO `reportdatasource` VALUES (19, '告警通知发送记录', 'alarmNotifyRecord');
INSERT INTO `reportdatasource` VALUES (20, '历史预警数据查询报表', 'prealarmhistory');
INSERT INTO `reportdatasource` VALUES (23, '机架变更记录报表', 'rackchangerecord');
INSERT INTO `reportdatasource` VALUES (24, '审计报表', 'auditReport');
INSERT INTO `reportdatasource` VALUES (25, '安全日志报表', 'securityReport');
INSERT INTO `reportdatasource` VALUES (26, '设备运行状态报表', 'equipStateReport');
INSERT INTO `reportdatasource` VALUES (27, '批量控制分组变更报表', 'batchControlGroupChangeLog');
INSERT INTO `reportdatasource` VALUES (28, '批量下发控制命令历史记录报表', 'batchControlCmdHistory');
INSERT INTO `reportdatasource` VALUES (29, '群控设备分组变更报表', 'autoControlEquipmentChangeLog');
-- INSERT INTO `reportdatasource` VALUES (30, 'BA控制命令报表', 'bacontrolcommand');
INSERT INTO `reportdatasource` VALUES (31, '所有告警报表', 'allAlarm');
INSERT INTO `reportdatasource` VALUES (32, '设备清单', 'equipmentReport');
INSERT INTO `reportdatasource` VALUES (33, '历史数据报表(原始数据)', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (34, '告警分类统计', 'alarmClassificationStatistical');
INSERT INTO `reportdatasource` VALUES (35, '设备串口信息报表', 'equipmentSerialPortInfo');

/*查询参数*/
/*
ParameterControlId:
1:开始时间
2:结束时间
3:通用下拉控件
4:设备
5:设备信号
6:指标
7:支路空开测点
8:即时报表id
9:设备支路
10：上传excel
11：上传json
12：输入框
13: 事件选择器
14: 信号基类选择器
15: 空调群控设备选择器
16: 局站选择器
17: 层级选择器
18: 设备类型选择器
19: 设备大类小类选择器
 */
/*当前告警报表*/
insert into `reportschemaqueryparameter` values (41, 'startDate', '开始时间', 1, '1', NULL, NULL, TRUE,10);
insert into `reportschemaqueryparameter` values (42, 'endDate', '结束时间', '1', '2', NULL, NULL, TRUE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (43, 'baseEquipmentIds', '设备基类', 1, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,30);
INSERT INTO `reportschemaqueryparameter` VALUES (44, 'equipmentIds', '设备', 1, '4', 'type=multiple', NULL, TRUE,40);
INSERT INTO `reportschemaqueryparameter` VALUES (113, 'eventLevels', '告警等级', 1, '3', 'api=coreeventseverities/all;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,50);
INSERT INTO `reportschemaqueryparameter` VALUES (114, 'eventName', '事件名', 1, '12', NULL, NULL, TRUE,60);
INSERT INTO `reportschemaqueryparameter` VALUES (115, 'keyword', '关键字', 1, '12', NULL, NULL, TRUE,70);
INSERT INTO `reportschemaqueryparameter` VALUES (116, 'confirmerIds', '确认人', 1, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,80);
INSERT INTO `reportschemaqueryparameter` VALUES (118, 'eventIds', '事件', 1, '13', 'type=events', NULL, TRUE,90);
INSERT INTO `reportschemaqueryparameter` VALUES (292, 'description', '注释', 1, '12', NULL, NULL, TRUE,100);
INSERT INTO `reportschemaqueryparameter` VALUES (400, 'equipmentCategories', '设备类型', 1, '19', NULL, NULL, TRUE,31);
-- INSERT INTO `reportschemaqueryparameter` VALUES (401, 'eventReasonTypes', '告警分类', 1, 3, 'api=eventreasontypes;type=multiple', '{"value": "id","display":"name"}', TRUE, 101);

/*历史告警报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (1, 'startDate', '开始时间', 2, '1', NULL, NULL, FALSE,10);
INSERT INTO `reportschemaqueryparameter` VALUES (2, 'endDate', '结束时间', 2, '2', NULL, NULL, FALSE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (3, 'baseEquipmentId', '设备基类', 2, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,30);
INSERT INTO `reportschemaqueryparameter` VALUES (4, 'equipmentIds', '设备', 2, '4', 'type=multiple', NULL, TRUE,40);
INSERT INTO `reportschemaqueryparameter` VALUES (109, 'eventLevel', '告警等级', 2, '3', 'api=coreeventseverities/all;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,50);
INSERT INTO `reportschemaqueryparameter` VALUES (110, 'eventName', '事件名', 2, '12', NULL, NULL, TRUE,60);
INSERT INTO `reportschemaqueryparameter` VALUES (111, 'operatorIds', '确认人', 2, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,70);
INSERT INTO `reportschemaqueryparameter` VALUES (112, 'keyword', '关键字', 2, '12', NULL, NULL, TRUE,80);
INSERT INTO `reportschemaqueryparameter` VALUES (117, 'eventIds', '事件', 2, '13', 'type=events', NULL, TRUE,90);
INSERT INTO `reportschemaqueryparameter` VALUES (293, 'description', '注释', 2, '12', NULL, NULL, TRUE,100);
INSERT INTO `reportschemaqueryparameter` VALUES (500, 'equipmentCategories', '设备类型', 2, '19', NULL, NULL, TRUE,31);
-- INSERT INTO `reportschemaqueryparameter` VALUES (501, 'eventReasonTypes', '告警分类', 2, 3, 'api=eventreasontypes;type=multiple', '{"value": "id","display":"name"}', TRUE, 101);


/*历史数据(信号)*/
INSERT INTO `reportschemaqueryparameter` VALUES (7, 'startTime', '开始时间', 3, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (8, 'endTime', '结束时间', 3, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (10, 'timeGranularity', '时间粒度', 3, '3', 'json=[{"name": "1分钟","value": "1m-start"},{"name": "5分钟","value": "5m-start"},{"name": "10分钟","value": "10m-start"},{"name": "30分钟","value": "30m-start"},{"name": "1小时","value": "1h-start"},{"name": "3小时","value": "3h-start"},{"name": "6小时","value": "6h-start"},{"name": "12小时","value": "12h-start"},{"name": "1天","value": "1d-start"},{"name": "周","value": "1w-start"},{"name": "月","value": "month-start"},{"name": "年","value": "year-start"}]', '{"value": "value", "display":"name"}', false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (85, 'signalIds', '设备信号', 3, '5', NULL, 'type=signals', true,5);
INSERT INTO `reportschemaqueryparameter` VALUES (150, 'baseTypeIds', '信号基类', 3, '14', NULL, 'type=basetypeids', true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (151, 'signalTypes', '存储类型', 3, '3', 'json=[{"name":"统计数据－最大值","value":"0"},{"name":"统计数据－最小值","value":"1"},{"name":"统计数据－平均值","value":"2"},{"name":"事件","value":"3"},{"name":"变化幅值","value":"4"},{"name":"存储周期","value":"5"},{"name":"抄表数据","value":"6"},{"name":"定时存储","value":"7"}];type=multiple', '{"value": "value", "display":"name"}', true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (152, 'unit', '是否带单位', 3, '3', 'json=[{"name":"否","value":"0"},{"name":"是","value":"1"}]', '{"value": "value", "display":"name"}', true,9);
INSERT INTO `reportschemaqueryparameter` VALUES (153, 'equipmentIds', '设备', 3, '4', 'type=multiple', '{"value": "value", "display":"name"}', true,8);
INSERT INTO `reportschemaqueryparameter` VALUES (154, 'valueRetrievalMethod', '取值方式', 3, '3', 'json=[{"name": "最大值","value": "max"},{"name": "最小值","value": "min"},{"name": "平均值","value": "mean"},{"name": "第一个值","value": "first"},{"name": "最后一个值","value": "last"}]', '{"value": "value", "display":"name"}', false,4);

/*历史数据(信号) 周期存储*/
INSERT INTO `reportschemaqueryparameter` VALUES (125, 'startTime', '开始时间', 28, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (126, 'endTime', '结束时间', 28, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (127, 'timeGranularity', '时间粒度', 28, '3', 'json=[{"name": "原始数据","value": "0"},{"name": "10分钟","value": "10m-start"},{"name": "30分钟","value": "30m-start"},{"name": "1小时","value": "1h-start"},{"name": "3小时","value": "3h-start"},{"name": "6小时","value": "6h-start"},{"name": "12小时","value": "12h-start"},{"name": "1天","value": "1d-start"},{"name": "周","value": "1w-start"},{"name": "月","value": "month-start"}]', '{"value": "value", "display":"name"}', false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (128, 'signalIds', '设备信号', 28, '5', NULL, 'type=signals', false,4);
INSERT INTO `reportschemaqueryparameter` VALUES (129, 'unit', '是否带单位', 28, '3', 'json=[{"name":"否","value":"0"},{"name":"是","value":"1"}]', '{"value": "value", "display":"name"}', true,5);
-- INSERT INTO `reportschemaqueryparameter` VALUES (130, 'valueRetrievalMethod', '取值方式', 28, '3', 'json=[{"name": "最大值","value": "max"},{"name": "最小值","value": "min"},{"name": "平均值","value": "mean"},{"name": "第一个值","value": "first"},{"name": "最后一个值","value": "last"}]', '{"value": "value", "display":"name"}', false,4);

/*告警操作日志报表(设备)*/
INSERT INTO `reportschemaqueryparameter` VALUES (12, 'startDate', '开始时间', 7, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (13, 'endDate', '结束时间', 7, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (14, 'equipmentIds', '设备', 7, '4', 'type=multiple', NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (16, 'operatorIds', '操作员', 7, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (17, 'operation', '操作', 7, '3', 'json=[{"name":"全部", "value":"0"},{"name":"强制结束告警", "value":"强制结束告警"}, {"name":"确认告警", "value":"确认告警"}, {"name":"告警备注", "value":"告警备注"}]', '{"value": "value", "display":"name"}', false,5);

/*告警操作日志报表(告警事件)*/
INSERT INTO `reportschemaqueryparameter` VALUES (18, 'startDate', '开始时间', 8, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (19, 'endDate', '结束时间', 8, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (21, 'eventIds', '事件', 8, '13', 'type=events', null, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (22, 'operatorIds', '操作员', 8, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (23, 'operation', '操作', 8, '3', 'json=[{"name":"全部", "value":"0"},{"name":"强制结束告警", "value":"强制结束告警"}, {"name":"告警确认", "value":"告警确认"}, {"name":"告警备注", "value":"告警备注"}]', '{"value": "value", "display":"name"}', false,5);

/*历史指标报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (26, 'startTime', '开始时间', 10, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (27, 'endTime', '结束时间', 10, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (28, 'complexIndexIds', '指标', 10, '6', NULL, NULL, false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (29, 'timeGranularity', '时间粒度', 10, '3', 'json=[{"name": "原始数据","value": "0"},{"name": "10分钟","value": "10m"},{"name": "30分钟","value": "30m"},{"name": "1小时","value": "1h"},{"name": "3小时","value": "3h"},{"name": "6小时","value": "6h"},{"name": "12小时","value": "12h"},{"name": "1天","value": "1d"},{"name": "周","value": "1w"},{"name": "月","value": "month"}]', '{"value": "value", "display":"name"}', false,4);

/*告警屏蔽报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (78, 'maskStartDate', '屏蔽开始时间', 23, '1', NULL, NULL, true,1);
INSERT INTO `reportschemaqueryparameter` VALUES (79, 'maskEndDate', '屏蔽结束时间', 23, '2', NULL, NULL, true,2);
INSERT INTO `reportschemaqueryparameter` VALUES (80, 'startDate', '开始时间', 23, '1', NULL, NULL, true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (81, 'endDate', '结束时间', 23, '2', NULL, NULL, true,4);
INSERT INTO `reportschemaqueryparameter` VALUES (82, 'operationType', '操作类型', 23, '3', 'api=findalarmmaskoperationtype', '{"value": "operationType", "display":"desc"}', true,5);
INSERT INTO `reportschemaqueryparameter` VALUES (83, 'eventIds', '事件', 23, '13', 'type=events&standardId=', NULL, true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (84, 'operatorIds', '操作员', 23, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', true,7);

/*用户操作日志报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (86, 'startDate', '开始时间', 24, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (87, 'endDate', '结束时间', 24, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (88, 'operatorIds', '操作员', 24, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,3);

/*告警数量统计报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (45, 'startDate', '开始时间', 15, '1', NULL, NULL, FALSE, 2);
INSERT INTO `reportschemaqueryparameter` VALUES (46, 'endDate', '结束时间', 15, '2', NULL, NULL, FALSE, 3);
INSERT INTO `reportschemaqueryparameter` VALUES (280, 'statisticType', '统计类型', 15, '3', 'json=[{"name": "房间级","value": "1"},{"name": "设备级","value": "2"},{"name": "设备类型级","value": "3"}]'', ''{"value": "value", "display":"name"}', '{"value": "value", "display":"name"}', FALSE, 1);
INSERT INTO `reportschemaqueryparameter` VALUES (281, 'resourceStructureIds', '层级', 15, '17', 'type=multiple', NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (282, 'equipmentCategories', '设备类型', 15, '18', 'type=multiple', NULL, true, 5);
INSERT INTO `reportschemaqueryparameter` VALUES (283, 'equipmentIds', '设备', 15, '4', 'type=multiple', NULL, true, 6);
INSERT INTO `reportschemaqueryparameter` VALUES (290, 'eventLevels', '告警等级', 15, '3', 'api=coreeventseverities/all;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,7);

/*用电统计报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (47, 'startTime', '开始时间', 16, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (48, 'endTime', '结束时间', 16, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (49, 'signalIds', '信号', 16, '5', 'type=signals&standardId=', NULL, false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (50, 'timeGranularity', '时间粒度', 16, '3', 'json=[{"name": "日-始","value": "start"},{"name": "日-终","value": "end"},{"name": "日-差值","value": "difference"}]', '{"value": "value", "display":"name"}', false,4);
INSERT INTO reportschemaqueryparameter VALUES (303, 'displayEquPosition', '导出时显示设备位置', 16, '3', 'json=[{"name":"No", "value":"-1"},{"name":"Yes", "value":"1"}]', '{"value": "value", "display":"name"}', false, 5);

/*通用定制报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (55, 'startTime', '开始时间', 18, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (56, 'endTime', '结束时间', 18, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (57, 'uploadExcel', '模板文件', 18, '10', NULL, 'upload-dir/customReport', false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (58, 'uploadJson', '配置文件', 18, '11', NULL, 'upload-dir/customReport', false,4);
INSERT INTO `reportschemaqueryparameter` VALUES (59, 'timeGranularity', '时间粒度', 18, '3', 'json=[{"name": "最小值","value": "min"},{"name": "最大值","value": "max"},{"name": "初始值","value": "start"},{"name": "末尾值","value": "end"},{"name": "平均值","value": "avg"},{"name": "差值","value": "difference"},{"name": "最大差值","value": "maxdifference"},{"name": "月差值","value": "monthdifference"}]', '{"value": "value", "display":"name"}', false,5);

/*控制记录报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (38, 'startTime', '开始时间', 13, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (39, 'endTime', '结束时间', 13, '2', NULL, NULL, false,2);

/*历史放电报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (25, 'equipmentIds', '设备', 9, '4', 'type=multiple', NULL, TRUE,1);
-- 机架变更记录报表
INSERT INTO `reportschemaqueryparameter` VALUES (200, 'startTime', '开始时间', 29, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (201, 'endTime', '结束时间', 29, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (203, 'position', '资源位置', 29, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (204, 'computerRackName', '机架名称', 29, '12', NULL, NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (205, 'iTDeviceName', 'IT设备名称', 29, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (206, 'operateState', '状态', 29, '3', 'json=[{"name": "所有","value": "0"},{"name": "上架","value": "1"},{"name": "下架","value": "2"}]', '{"value": "value","display":"name"}', TRUE,6);

/*历史预警数据查询报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (119, 'startDate', '开始时间', 27, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (120, 'endDate', '结束时间', 27, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (121, 'preAlarmCategory', '预警分类', 27, '3', 'api=prealarmcategorys', '{"value": "categoryId","display":"categoryName"}', TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (122, 'preAlarmSeverity', '预警等级', 27, '3', 'api=prealarmseveritys', '{"value": "preAlarmSeverityId","display":"preAlarmSeverityName"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (123, 'levelOfPath', '资源层级', 27, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (124, 'resourceName', '资源名称', 27, '12', NULL, NULL, TRUE,6);
-- 审计报表
INSERT INTO `reportschemaqueryparameter` VALUES (210, 'startTime', '开始时间', 30, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (211, 'endTime', '结束时间', 30, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (212, 'operator', '操作账户', 30, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (213, 'details', '内容', 30, '12', NULL, NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (214, 'clientIp', '客户端ip', 30, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (215, 'level', '审计级别', 30, '3', 'json=[{"name": "最小级别","value": "1"},{"name": "基本级别","value": "2"},{"name": "详细级别","value": "3"},{"name": "未定义级别","value": "4"}];type=multiple', '{"value": "value","display":"name"}', TRUE,6);
-- 安全日志报表
INSERT INTO `reportschemaqueryparameter` VALUES (216, 'startTime', '开始时间', 31, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (217, 'endTime', '结束时间', 31, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (218, 'operator', '操作账户', 31, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (219, 'type', '类别', 31, '3', 'json=[{"name": "身份用户鉴别","value": "1"},{"name": "攻击检测","value": "2"},{"name": "暴力破解","value": "3"},{"name": "完整性检测","value": "4"}];type=multiple', '{"value": "value","display":"name"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (220, 'details', '描述', 31, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (221, 'clientIp', '客户端ip', 31, '12', NULL, NULL, TRUE,6);
-- 门禁报表
INSERT INTO `reportschemaqueryparameter` VALUES (89, 'doorAreaId', '区域', 25, '3', 'api=doorareas', '{"value": "areaId", "display":"areaName"}', TRUE,1);
INSERT INTO `reportschemaqueryparameter` VALUES (90, 'equipmentIds', '设备', 25, '4', 'type=multiple', NULL, TRUE,2);
INSERT INTO `reportschemaqueryparameter` VALUES (91, 'cardCode', '卡号', 25, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (92, 'cardName', '卡名称', 25, '12', NULL, NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (93, 'operatorIds', '持卡人', 25, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (94, 'cardGroup', '卡分组', 25, '3', 'api=dataitems?entryId=75', '{"value": "itemId", "display":"itemValue"}', TRUE,6);
INSERT INTO `reportschemaqueryparameter` VALUES (95, 'cardStatus', '卡状态', 25, '3', 'api=dataitems?entryId=46', '{"value": "itemId", "display":"itemValue"}', TRUE,7);
INSERT INTO `reportschemaqueryparameter` VALUES (96, 'startDate', '刷卡开始时间', 25, '1', NULL, NULL, TRUE,8);
INSERT INTO `reportschemaqueryparameter` VALUES (97, 'endDate', '刷卡结束时间', 25, '2', NULL, NULL, TRUE,9);
INSERT INTO `reportschemaqueryparameter` VALUES (98, 'inOutSigns', '进出门标志', 25, '3', 'json=[{"name": "进门","value": "进门"},{"name": "出门","value": "出门"}]', '{"value": "value", "display":"name"}', TRUE,10);
INSERT INTO `reportschemaqueryparameter` VALUES (99, 'validName', '刷卡或开门状态', 25, '12', NULL, NULL, TRUE,11);
-- 告警通知发送记录报表
INSERT INTO `reportschemaqueryparameter` VALUES (101, 'alarmStartTimeFrom', '告警时间起', 26, '1', NULL, NULL,  false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (102, 'alarmStartTimeTo', '告警时间止', 26, '2', NULL, NULL,  false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (103, 'sendTimeFrom', '发送时间起', 26, '1', NULL, NULL,  false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (104, 'sendTimeTo', '发送时间止', 26, '2', NULL, NULL,  false,4);
INSERT INTO `reportschemaqueryparameter` VALUES (105, 'sendType', '发送方式', 26, '3', 'json=[{"name":"全部", "value":"0"},{"name":"短信", "value":"短信"}, {"name":"邮件", "value":"邮件"}, {"name":"电话语音(短信)", "value":"电话语音(短信)"}, {"name":"企业微信应用通知", "value":"企业微信应用通知"}]', '{"value": "value", "display":"name"}',  true,5);
INSERT INTO `reportschemaqueryparameter` VALUES (106, 'sendResult', '发送结果', 26, '3',  'json=[{"name":"全部", "value":"0"},{"name":"发送成功", "value":"发送成功"}, {"name":"发送失败", "value":"发送失败"}]', '{"value": "value", "display":"name"}', true,6);
/*设备历史运行状态报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (222,  'startDate', '开始时间', 32, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (223,  'endDate', '结束时间', 32, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (224,  'virtualInfo', '群控设备', 32, '15', 'type=multiple', NULL, false,3);
/*批量控制分组变更报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (225, 'startDate', '开始时间', 33, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (226, 'endDate', '结束时间', 33, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (227, 'changeType', '变更类型', 33, '3', 'json=[{"name":"全部", "value":"-1"},{"name":"新增", "value":"1"},{"name":"修改", "value":"2"},{"name":"删除", "value":"3"}]', '{"value": "value", "display":"name"}', true,3);
/*批量下发控制命令历史记录报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (228, 'startDate', '开始时间', 34, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (229, 'endDate', '结束时间', 34, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (230, 'equipmentIds', '设备', 34, '4', 'type=multiple', NULL, true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (231, 'cmdType', '命令类型', 34, '3', 'json=[{"name":"全部", "value":"-1"},{"name":"远程开机", "value":"1"},{"name":"远程关机", "value":"2"},{"name":"温度设置", "value":"3"},{"name":"工作模式切换", "value":"4"}]', '{"value": "value", "display":"name"}', true,4);
/*群控设备分组变更报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (232, 'startDate', '开始时间', 35, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (233, 'endDate', '结束时间', 35, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (234, 'stationIds', '局站', 35, '16', 'type=multiple', NULL, true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (235, 'changeType', '变更类型', 35, '3', 'json=[{"name":"全部", "value":"-1"},{"name":"新增", "value":"1"},{"name":"修改", "value":"2"},{"name":"删除", "value":"3"}]', '{"value": "value", "display":"name"}', true,4);
/*ba控制命令报表*/
-- INSERT INTO `reportschemaqueryparameter` VALUES (236, 'equipmentIds', '设备', 36, '4', 'type=multiple', NULL, TRUE,1);
-- INSERT INTO `reportschemaqueryparameter` VALUES (237, 'controlName', '控制命令名称', 36, '12', NULL, NULL, TRUE,2);
/*所有告警报表*/
insert into `reportschemaqueryparameter` values (238, 'startDate', '开始时间', 37, '1', NULL, NULL, FALSE,10);
insert into `reportschemaqueryparameter` values (239, 'endDate', '结束时间', 37, '2', NULL, NULL, FALSE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (240, 'baseEquipmentIds', '设备基类', 37, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,30);
INSERT INTO `reportschemaqueryparameter` VALUES (241, 'equipmentIds', '设备', 37, '4', 'type=multiple', NULL, TRUE,40);
INSERT INTO `reportschemaqueryparameter` VALUES (242, 'eventLevels', '告警等级', 37, '3', 'api=coreeventseverities/all;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,50);
INSERT INTO `reportschemaqueryparameter` VALUES (243, 'eventName', '事件名', 37, '12', NULL, NULL, TRUE,60);
INSERT INTO `reportschemaqueryparameter` VALUES (244, 'keyword', '关键字', 37, '12', NULL, NULL, TRUE,70);
INSERT INTO `reportschemaqueryparameter` VALUES (245, 'confirmIds', '确认人', 37, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,80);
INSERT INTO `reportschemaqueryparameter` VALUES (246, 'eventIds', '事件', 37, '13', 'type=events', NULL, TRUE,90);
INSERT INTO `reportschemaqueryparameter` VALUES (600, 'equipmentCategories', '设备类型', 37, '19', '', NULL, TRUE,31);
-- INSERT INTO `reportschemaqueryparameter` VALUES (601, 'eventReasonTypes', '告警分类', 37, 3, 'api=eventreasontypes;type=multiple', '{"value": "id","display":"name"}', TRUE, 101);
/*设备报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (247, 'resourceStructureIds', '层级', 38, '17', 'type=multiple', NULL, TRUE,10);
INSERT INTO `reportschemaqueryparameter` VALUES (248, 'baseEquipmentIds', '设备基类', 38, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (288, 'equipmentState', '设备状态', 38, '3', 'json=[{"name": "离线","value": "0"},{"name": "在线","value": "1"},{"name": "未注册","value": "2"}]', '{"value": "value","display":"name"}', TRUE, 30);
INSERT INTO `reportschemaqueryparameter` VALUES (700, 'equipmentCategories', '设备类型', 38, '19', '', '', TRUE, 21);

/*历史数据报表（原始数据）*/
INSERT INTO `reportschemaqueryparameter` VALUES (260, 'startTime', '开始时间', 39, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (261, 'endTime', '结束时间', 39, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (262, 'signalIds', '设备信号', 39, '5', NULL, 'type=signals', true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (263, 'baseTypeIds', '信号基类', 39, '14', NULL, 'type=basetypeids', true,4);
INSERT INTO `reportschemaqueryparameter` VALUES (264, 'signalTypes', '存储类型', 39, '3', 'json=[{"name":"统计数据－最大值","value":"0"},{"name":"统计数据－最小值","value":"1"},{"name":"统计数据－平均值","value":"2"},{"name":"事件","value":"3"},{"name":"变化幅值","value":"4"},{"name":"存储周期","value":"5"},{"name":"抄表数据","value":"6"},{"name":"定时存储","value":"7"}];type=multiple', '{"value": "value", "display":"name"}', true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (265, 'equipmentIds', '设备', 39, '4', 'type=multiple', '{"value": "value", "display":"name"}', true,5);
/*告警分类统计报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (284, 'startDate', '开始时间', 40, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (285, 'endDate', '结束时间', 40, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (286, 'baseEquipmentIds', '设备基类', 40, '3', 'api=equipmentbasetypedtos/used;type=multiple', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (287, 'equipmentIds', '设备', 40, '4', 'type=multiple', '{"value": "value", "display":"name"}', true,4);

/*串口信息报表*/
INSERT INTO reportschemaqueryparameter VALUES (301, 'monitorUnitName', '监控单元名称', 41, '12', NULL, NULL, TRUE,2);
INSERT INTO reportschemaqueryparameter VALUES (302, 'portName', '端口名称', 41, '12', NULL, NULL, TRUE,3);
/* 新增串口信息报表的查询参数 */
INSERT INTO reportschemaqueryparameter VALUES (310, 'resourceStructureIds', '层级名称', 41, '17', NULL, NULL, TRUE,4);
INSERT INTO reportschemaqueryparameter VALUES (311, 'equipmentName', '设备名称', 41, '12', NULL, NULL, TRUE,7);
INSERT INTO reportschemaqueryparameter VALUES (312, 'equipmentCategories', '设备类型', 41, '19', NULL, NULL, TRUE,8);
INSERT INTO reportschemaqueryparameter VALUES (313, 'ipAddress', 'IP地址', 41, '12', NULL, NULL, TRUE,9);
INSERT INTO reportschemaqueryparameter VALUES (314, 'samplerUnitName', '采集单元名称', 41, '12', NULL, NULL, TRUE,10);
INSERT INTO reportschemaqueryparameter VALUES (315, 'equipmentState', '设备状态', 41, '3', 'json=[{"name":"离线", "value":"0"},{"name":"在线", "value":"1"},{"name":"未注册", "value":"2"}]', '{"value": "value","display":"name"}', TRUE,11);
INSERT INTO reportschemaqueryparameter VALUES (316, 'equipmentTemplateName', '设备模板名称', 41, '12', NULL, NULL, TRUE,12);
INSERT INTO reportschemaqueryparameter VALUES (317, 'displayId', '是否显示系统ID', 41, '3', 'json=[{"name":"否","value":"0"},{"name":"是","value":"1"}]', '{"value": "value", "display":"name"}', true,13);


/*输出参数*/
INSERT INTO reportschemaexportparameter VALUES (1,'max','最大值',3,1);
INSERT INTO reportschemaexportparameter VALUES (2,'min','最小值',3,1);
INSERT INTO reportschemaexportparameter VALUES (3,'sum','累加值',3,1);
INSERT INTO reportschemaexportparameter VALUES (4,'avg','平均值',3,1);
INSERT INTO reportschemaexportparameter VALUES (5,'max','最大值',10,1);
INSERT INTO reportschemaexportparameter VALUES (6,'min','最小值',10,1);
INSERT INTO reportschemaexportparameter VALUES (7,'sum','累加值',10,1);
INSERT INTO reportschemaexportparameter VALUES (8,'avg','平均值',10,1);
-- INSERT INTO reportschemaexportparameter VALUES (9,'first','一级告警',15,1);
-- INSERT INTO reportschemaexportparameter VALUES (10,'second','二级告警',15,1);
-- INSERT INTO reportschemaexportparameter VALUES (11,'third','三级告警',15,1);
-- INSERT INTO reportschemaexportparameter VALUES (12,'fourth','四级告警',15,1);
INSERT INTO reportschemaexportparameter VALUES (13,'max','最大值',16,1);
INSERT INTO reportschemaexportparameter VALUES (14,'min','最小值',16,1);
INSERT INTO reportschemaexportparameter VALUES (15,'sum','累加值',16,1);
INSERT INTO reportschemaexportparameter VALUES (16,'avg','平均值',16,1);
INSERT INTO reportschemaexportparameter VALUES (17,'max','最大值',20,1);
INSERT INTO reportschemaexportparameter VALUES (18,'min','最小值',20,1);
INSERT INTO reportschemaexportparameter VALUES (19,'sum','累加值',20,1);
INSERT INTO reportschemaexportparameter VALUES (20,'avg','平均值',20,1);
INSERT INTO reportschemaexportparameter VALUES (21,'max','最大值',28,1);
INSERT INTO reportschemaexportparameter VALUES (22,'min','最小值',28,1);
INSERT INTO reportschemaexportparameter VALUES (23,'sum','累加值',28,1);
INSERT INTO reportschemaexportparameter VALUES (24,'avg','平均值',28,1);
INSERT INTO reportschemaexportparameter VALUES (25,'first','首值',10,1);
INSERT INTO reportschemaexportparameter VALUES (26,'last','尾值',10,1);
/*定时任务邮件发送配置*/
INSERT INTO `reporttimingtasktimetype` VALUES (1,'存储时间','0'),(2,'一小时前','-1h'),(3,'一天前','-1d'),(4,'一周前','-1w'),(5,'一月前','-1m');

-- 默认初始化一个历史告警报表给现场定制页面查询
INSERT INTO report (ReportId, ReportName, ReportDescription, ReportSchemaId, ReportSchemaCategoryId, UpdateUserId, UpdateTime, ReportDataSourceId, MaxQueryInterval, CreateUserId, overt) VALUES(1, '历史告警', NULL, 2, 2, -9999, '2023-06-08 10:50:11', 2, NULL, -9999, 0);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 1, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 2, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 3, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 4, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 109, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 110, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 111, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 112, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 117, '[]', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 293, '', 1);
-- INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 401, '', 1);