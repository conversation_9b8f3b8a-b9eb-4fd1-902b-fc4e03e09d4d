package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.dto.MenuTreeDTO;
import com.siteweb.admin.entity.*;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.service.*;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.service.SystemConfigService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MenuTreeServiceImpl implements MenuTreeService {

    @Autowired
    AccountMapper accountMapper;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    MenuProfileService menuProfileService;

    @Autowired
    MenuStructureService menuStructureService;

    @Autowired
    MenuItemStructureMapService menuItemStructureMapService;

    @Autowired
    MenuItemService menuItemService;

    @Autowired
    UserRoleMapper userRoleMapper;

    @Autowired
    PermissionService permissionService;
    @Autowired
    UserGraphicPageMapService userGraphicPageMapService;

    @Autowired
    RoleGraphicPageMapService roleGraphicPageMapService;
    @Autowired
    RolePermissionMapService rolePermissionMapService;
    @Autowired
    MenuPermissionGroupMapService menuPermissionGroupMapService;

    @Override
    public MenuTreeDTO getMenuTreeByUserId(Integer userId) {
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return null;
        }
        MenuTreeDTO menuTrees = getMenuTrees(userId, enablePermission());
        //替换用户自定义首页
        if (menuTrees != null) {
            this.replaceHomeIndex(userId, menuTrees);
        }
        return menuTrees;
    }

    /**
     * 是否启用权限校验
     * @return boolean true 启用 false 不启用
     */
    private boolean enablePermission() {
        return systemConfigService.findBooleanValue(SystemConfigEnum.MENU_PERMISSION_ENABLE);
    }

    private void replaceHomeIndex(Integer userId, @NotNull MenuTreeDTO menuTrees) {
        if (ObjectUtil.isNull(menuTrees.getChildren()) || menuTrees.getChildren().isEmpty()) {
            return;
        }
        MenuTreeDTO menuTreeDTO = menuTrees.getChildren().get(0);
        if (ObjectUtil.isNull(menuTreeDTO) || CharSequenceUtil.isBlank(menuTreeDTO.getPath()) || !menuTreeDTO.getPath().contains("hmi?pageId=")) {
            return;
        }
        //用户首页组态
        String replaceGraphicPageId = this.findUserHomeIndex(userId);
        if (CharSequenceUtil.isBlank(replaceGraphicPageId)) {
            //角色首页组态
            replaceGraphicPageId = this.findRoleHomeIndex(userId);
        }
        //替换首页组态id
        if (CharSequenceUtil.isNotBlank(replaceGraphicPageId)) {
            String finalReplaceGraphicPageId = replaceGraphicPageId;
            String replace = CharSequenceUtil.replace(menuTreeDTO.getPath(), "[0-9]{1,}", regex -> finalReplaceGraphicPageId);
            menuTreeDTO.setPath(replace);
        }
    }

    /**
     * 查找用户角色首页组态
     * @param userId 用户id
     * @return {@link String}
     */
    private String findRoleHomeIndex(Integer userId) {
        List<RoleGraphicPageMap> roleGraphicPageMapList = roleGraphicPageMapService.findByUserId(userId);
        if (CollUtil.isNotEmpty(roleGraphicPageMapList)) {
            return roleGraphicPageMapList.get(0).getGraphicPageId().toString();
        }
        return null;
    }

    /**
     * 查找用户首页组态
     * @param userId 用户id
     * @return {@link String}
     */
    private String findUserHomeIndex(Integer userId) {
        UserGraphicPageMap userGraphicPageMap = userGraphicPageMapService.findByUserId(userId);
        if (ObjectUtil.isNotNull(userGraphicPageMap)) {
            return userGraphicPageMap.getGraphicPageId().toString();
        }
        return null;
    }

    private MenuTreeDTO getMenuTrees(Integer userId, boolean checkPermission) {
        MenuTreeDTO menuTreeDTO = new MenuTreeDTO();
        MenuProfile menuProfile = menuProfileService.getCurrentMenuProfile();
        if (menuProfile == null) {
            return null;
        }
        menuTreeDTO.setPath(menuItemService.findTopMenuItemPath());
        List<MenuStructure> menuStructureList = menuStructureService.findTopMenuStructuresByMenuProfileId(menuProfile.getMenuProfileId());
        Set<String> menuPermissions = new HashSet<>();
        List<UserRole> userRoleList = userRoleMapper.findRolesByUserId(userId);
        if (checkPermission) {
            menuPermissions = getMenuPermissionsByUserRoles(userRoleList);
        }
        List<MenuTreeDTO> menuTreeDTOList = generateMenuTreeFromMenuStructure(menuStructureList, menuPermissions, checkPermission);
        menuTreeDTOList.addAll(getFirstLevelMenuItem(menuProfile.getMenuProfileId(), menuPermissions, checkPermission));
        List<MenuTreeDTO> sortedMenuStructureDTOList = menuTreeDTOList.stream()
                .sorted(Comparator.comparing(MenuTreeDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                .toList();
        menuTreeDTO.setChildren(sortedMenuStructureDTOList);
        return menuTreeDTO;
    }

    private Set<String> getMenuPermissionsByUserRoles(List<UserRole> userRoles) {
        if (CollUtil.isEmpty(userRoles)) {
            return Collections.emptySet();
        }
        List<Integer> roleIds = userRoles.stream()
                                         .map(UserRole::getRoleId)
                                         .toList();
        List<RolePermissionMap> permissionList = rolePermissionMapService.findByRoleIdsAndCategoryId(roleIds, PermissionCategoryEnum.MENU.getPermissionCategoryId());
        List<Integer> permissionGroupIds = permissionList.stream()
                                                         .map(RolePermissionMap::getPermissionId)
                                                         .toList();
        //是否拥有所有菜单权限
        if (permissionGroupIds.stream().anyMatch(permissionId -> Objects.equals(permissionId, -1))) {
            return permissionService.findPermissionsByCategory(PermissionCategoryEnum.MENU.getPermissionCategoryId())
                                    .stream()
                                    .map(Permission::getDescription)
                                    .collect(Collectors.toSet());
        }
        //拥有部分菜单权限组
        List<Integer> permissionIds = menuPermissionGroupMapService.findPermissionIdsByGroupIds(permissionGroupIds);
        return permissionService.getPermissionByIds(permissionIds)
                                .stream()
                                .map(Permission::getDescription)
                                .collect(Collectors.toSet());
    }

    private List<MenuTreeDTO> generateMenuTreeFromMenuStructure(List<MenuStructure> menuStructureList, Set<String> menuPermissions, boolean checkPermission) {
        List<MenuTreeDTO> menuTreeDTOList = new ArrayList<>();
        for (MenuStructure menuStructure : menuStructureList) {
            if (checkPermission && !checkMenuStructurePermissions(menuStructure, menuPermissions)) {
                continue;
            }
            MenuTreeDTO menuTreeDTO = new MenuTreeDTO();
            menuTreeDTO.setSortIndex(menuStructure.getSortIndex());
            menuTreeDTO.setDescription(menuStructure.getDescription());
            menuTreeDTO.getData().getMenu().setId(menuStructure.getMenuStructureId());
            menuTreeDTO.getData().getMenu().setTitle(menuStructure.getTitle());
            menuTreeDTO.getData().getMenu().setAlias(menuStructure.getAlias());
            menuTreeDTO.getData().getMenu().setHidden(menuStructure.getHidden());
            menuTreeDTO.getData().getMenu().setIcon(menuStructure.getIcon());
            menuTreeDTO.getData().getMenu().setOrder(menuStructure.getSortIndex());
            menuTreeDTO.getData().getMenu().setOrder(menuStructure.getSortIndex());
            menuTreeDTO.getData().getMenu().setExpanded(menuStructure.getExpanded());
            menuTreeDTO.getData().getMenu().setSelected(menuStructure.getSelected());
            if (!menuStructure.getMenuItemStructureMaps().isEmpty()) {
                List<MenuItemStructureMap> sortedMaps = menuStructure.getMenuItemStructureMaps().stream()
                        .sorted(Comparator.comparing(MenuItemStructureMap::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                        .toList();
                menuStructure.setMenuItemStructureMaps(sortedMaps);
            }
            List<MenuTreeDTO> childrens = getMenuStructureChildrenNode(menuStructure.getMenuItemStructureMaps(), menuPermissions, checkPermission);
            if (!menuStructure.getChildren().isEmpty()) {
                List<MenuStructure> sortedChildren = menuStructure.getChildren().stream()
                        .sorted(Comparator.comparing(MenuStructure::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                        .toList();
                List<MenuTreeDTO> subChildrens = generateMenuTreeFromMenuStructure(sortedChildren, menuPermissions, checkPermission);
                if (!subChildrens.isEmpty()) {
                    childrens.addAll(subChildrens);
                }
            }
            if (!childrens.isEmpty()) {
                List<MenuTreeDTO> sortedChildrens = childrens.stream()
                        .sorted(Comparator.comparing(MenuTreeDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                        .toList();
                menuTreeDTO.setChildren(sortedChildrens);
            }
            menuTreeDTOList.add(menuTreeDTO);
        }
        return menuTreeDTOList;
    }

    private List<MenuTreeDTO> generateMenuTreeFromMenuItem(List<MenuItem> menuItemList, String parentMenuId, Set<String> menuPermissions, boolean checkPermission) {
        if (menuItemList.isEmpty()) {
            return Collections.emptyList();
        }
        List<MenuTreeDTO> menuTreeDTOList = new ArrayList<>();
        for (MenuItem menuItem : menuItemList) {
            String menuParentId = String.format("%s-%d", parentMenuId, menuItem.getMenuItemId());
            //判断菜单权限
            if (checkPermission && !checkMenuItemPermissions(menuItem, menuParentId, menuPermissions)) {
                continue;
            }
            MenuTreeDTO menuTreeDTO = new MenuTreeDTO();
            menuTreeDTO.setPath(menuItem.getPath());
            menuTreeDTO.setLayoutPosition(menuItem.getLayoutPosition());
            menuTreeDTO.setExternalWeb(menuItem.getIsExternalWeb());
            menuTreeDTO.setEmbed(menuItem.getIsEmbed());
            menuTreeDTO.setMenuHasNavigation(menuItem.getMenuHasNavigation());
            menuTreeDTO.setDescription(menuItem.getDescription());
            menuTreeDTO.getData().getMenu().setId(menuItem.getMenuItemId());
            menuTreeDTO.getData().getMenu().setTitle(menuItem.getTitle());
            menuTreeDTO.getData().getMenu().setAlias(menuItem.getAlias());
            menuTreeDTO.getData().getMenu().setIcon(menuItem.getIcon());
            menuTreeDTO.getData().getMenu().setExpanded(menuItem.getExpanded());
            menuTreeDTO.getData().getMenu().setSelected(menuItem.getSelected());
            menuTreeDTO.getData().getMenu().setPathMatch(menuItem.getPathMatch());
            List<MenuTreeDTO> childrens = generateMenuTreeFromMenuItem(menuItem.getChildren(), String.format("%s-%d", parentMenuId, menuItem.getMenuItemId()), menuPermissions, checkPermission);
            if (!childrens.isEmpty()) {
                List<MenuTreeDTO> sortedChildrens = childrens.stream()
                        .sorted(Comparator.comparing(MenuTreeDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                        .toList();
                menuTreeDTO.setChildren(sortedChildrens);
            }
            menuTreeDTOList.add(menuTreeDTO);
        }
        return menuTreeDTOList;
    }

    private List<MenuTreeDTO> getFirstLevelMenuItem(Integer menuProfileId, Set<String> menuPermissions, boolean checkPermission) {
        List<MenuItemStructureMap> firstLevelMenuItemMaps = menuItemStructureMapService.findByMenuStructureIdAndMenuProfileId(0, menuProfileId);
        return getMenuStructureChildrenNode(firstLevelMenuItemMaps, menuPermissions, checkPermission);
    }

    private List<MenuTreeDTO> getMenuStructureChildrenNode(List<MenuItemStructureMap> menuItemStructureMaps, Set<String> menuPermissions, boolean checkPermission) {
        List<MenuTreeDTO> menuTreeDTOList = new ArrayList<>();
        if (menuItemStructureMaps.isEmpty()) {
            return menuTreeDTOList;
        }
        for (MenuItemStructureMap menuItemStructureMap : menuItemStructureMaps) {
            String menuParentId = String.format("%d-%d", menuItemStructureMap.getMenuStructureId(), menuItemStructureMap.getMenuItemId());
            MenuItem menuItem = menuItemService.findById(menuItemStructureMap.getMenuItemId());
            //判断菜单权限
            if (menuItem == null || (checkPermission && !checkMenuItemPermissions(menuItem, menuParentId, menuPermissions))) {
                continue;
            }
            MenuTreeDTO menuTreeDTO = new MenuTreeDTO();
            menuTreeDTO.setPath(menuItem.getPath());
            menuTreeDTO.setSortIndex(menuItemStructureMap.getSortIndex());
            menuTreeDTO.setLayoutPosition(menuItem.getLayoutPosition());
            menuTreeDTO.setExternalWeb(menuItem.getIsExternalWeb());
            menuTreeDTO.setEmbed(menuItem.getIsEmbed());
            menuTreeDTO.setMenuHasNavigation(menuItem.getMenuHasNavigation());
            menuTreeDTO.setDescription(menuItem.getDescription());
            menuTreeDTO.getData().getMenu().setId(menuItem.getMenuItemId());
            menuTreeDTO.getData().getMenu().setTitle(menuItem.getTitle());
            menuTreeDTO.getData().getMenu().setAlias(menuItem.getAlias());
            menuTreeDTO.getData().getMenu().setIcon(menuItem.getIcon());
            menuTreeDTO.getData().getMenu().setExpanded(menuItem.getExpanded());
            menuTreeDTO.getData().getMenu().setSelected(menuItem.getSelected());
            menuTreeDTO.getData().getMenu().setPathMatch(menuItem.getPathMatch());
            menuTreeDTO.getData().getMenu().setOrder(menuItem.getSortIndex());
            List<MenuTreeDTO> childrens = generateMenuTreeFromMenuItem(menuItem.getChildren(), menuParentId, menuPermissions, checkPermission);
            if (!childrens.isEmpty()) {
                List<MenuTreeDTO> sortedChildrens = childrens.stream()
                        .sorted(Comparator.comparing(MenuTreeDTO::getSortIndex, Comparator.nullsFirst(Integer::compareTo)))
                        .toList();
                menuTreeDTO.setChildren(sortedChildrens);
            }
            menuTreeDTOList.add(menuTreeDTO);
        }
        return menuTreeDTOList;
    }

    private String getFullMenuStructureId(MenuStructure menuStructure, String currentMenuStructureId) {
        if (menuStructure.getParentId().equals(0)) {
            String result = menuStructure.getMenuStructureId().toString();
            if (currentMenuStructureId == null) {
                return result;
            } else {
                return String.format("%s-%s", result, currentMenuStructureId);
            }
        }
        MenuStructure parentMenuStructure = menuStructureService.findById(menuStructure.getParentId());
        if (currentMenuStructureId == null) {
            return getFullMenuStructureId(parentMenuStructure, menuStructure.getMenuStructureId().toString());
        } else {
            return getFullMenuStructureId(parentMenuStructure, String.format("%d-%s", menuStructure.getMenuStructureId(), currentMenuStructureId));
        }
    }

    private boolean checkMenuStructurePermissions(MenuStructure menuStructure, Set<String> permissions) {
        String fullMenuStructureId = getFullMenuStructureId(menuStructure, null);
        for (String strPermission : permissions) {
            if (strPermission.indexOf(fullMenuStructureId + "-") == 0) {
                return true;
            }
        }
        return false;
    }

    private boolean checkMenuItemPermissions(MenuItem menuItem, String menuParentId, Set<String> permissions) {
        if (menuItem.getChildren().isEmpty()) {
            return permissions.contains(menuParentId);
        }
        for (String strPermission : permissions) {
            if (strPermission.indexOf(menuParentId + "-") == 0) {
                return true;
            }
        }
        return false;
    }
}
