package com.siteweb.asset.service;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.asset.dto.AssetLedgerDTO;
import com.siteweb.monitoring.dto.*;
import com.siteweb.utility.dto.IdValueDTO;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 */
public interface EquipmentLedgerService {
    List<IdValueDTO<String,Integer>>  modelStatistics();
    List<Tree<Integer>> styleTree();
    EquipmentStatusStatisticsDTO statusStatistics();
    PageImpl<EquipmentStateSignalPointDTO> getStatusListByResourceStructure(Pageable pageable, Integer resourceStructureId, Integer alarmState, Integer conState, Integer equipmentCategory, String keywords);
    ExcelWriter exportEquStatus(Integer resourceStructureId, Integer alarmState, Integer conState,Integer equipmentCategory,String keywords);
    List<EquipmentSignalPointStatisticsDTO> getSignalpointStatistics();
    List<AssetLedgerDTO> getAllAssetLedger(String resourceIds, String keywords, String styleName);
    PageImpl<AssetLedgerDTO> getAllAssetLedgerPaged(Pageable pageable, String resourceIds, String keywords, String styleName);
    Integer getAssetDeviceSum();
    List<TemplateSignalPointDTO> getSingleFullPoint(Integer templateId);
    Map<Integer, List<TemplateSignalPointDTO>> getFullPoint(Collection<Integer> templateIds);
    ExcelWriter exportSignalPoint(List<Integer> templateIds);
}
