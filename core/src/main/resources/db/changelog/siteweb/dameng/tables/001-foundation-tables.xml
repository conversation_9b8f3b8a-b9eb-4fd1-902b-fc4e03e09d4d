<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-001" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_account"
            (
            "UserId" INT NOT NULL PRIMARY KEY,
            "UserName" VARCHAR(128) NOT NULL,
            "LogonId" VARCHAR(20) NOT NULL,
            "Password" VARCHAR(128),
            "Enable" BIT DEFAULT 1 NOT NULL,
            "MaxError" INT,
            "Locked" BIT DEFAULT 0 NOT NULL,
            "ValidTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            "IsRemote" BIT DEFAULT 0 NOT NULL,
            "CenterId" INT,
            "PasswordValidTime" TIMESTAMP(0),
            "Avatar" VARCHAR(256),
            "ThemeName" VARCHAR(128),
            "NeedResetPwd" TINYINT DEFAULT 0);

            CREATE TABLE "tbl_activecontrol"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "ControlId" INT NOT NULL,
            "ControlName" VARCHAR(128) NOT NULL,
            "SerialNo" INT AUTO_INCREMENT NOT NULL,
            "ControlSeverity" INT NOT NULL,
            "CmdToken" TEXT NOT NULL,
            "ControlPhase" INT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(255),
            "ControlResultType" INT,
            "ControlResult" VARCHAR(255),
            "ControlExecuterId" INT,
            "ControlExecuterIdName" VARCHAR(255),
            "ControlType" INT,
            "ActionId" INT,
            "Description" VARCHAR(255),
            "Retry" INT,
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "ParameterValues" TEXT NOT NULL,
            "BaseCondId" DECIMAL(12,0),
            NOT CLUSTER PRIMARY KEY("ControlId", "EquipmentId", "SerialNo", "StartTime", "StationId"));

            CREATE OR REPLACE  INDEX "IDX_ActiceControl_2" ON "tbl_activecontrol"("StationId" ASC,"EquipmentId" ASC,"ControlId" ASC,"StartTime" ASC);
            CREATE OR REPLACE  INDEX "TBL_ActiveControl_IDX1" ON "tbl_activecontrol"("SerialNo" ASC);

            CREATE TABLE "tbl_activeevent"
            (
            "SequenceId" VARCHAR(128) NOT NULL,
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "EventId" INT NOT NULL,
            "EventName" VARCHAR(128) NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventSeverityId" INT NOT NULL,
            "EventSeverity" VARCHAR(128) NOT NULL,
            "EventLevel" INT,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "CancelTime" TIMESTAMP(0),
            "CancelUserId" INT,
            "CancelUserName" VARCHAR(128),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(128),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "ReversalNum" INT,
            "Meanings" VARCHAR(255),
            "EventFilePath" VARCHAR(255),
            "Description" VARCHAR(255),
            "SourceHostId" INT,
            "InstructionId" VARCHAR(255),
            "InstructionStatus" INT,
            "StandardAlarmNameId" INT,
            "StandardAlarmName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EquipmentCategory" INT NOT NULL,
            "EquipmentCategoryName" VARCHAR(128),
            "MaintainState" INT DEFAULT 0 NOT NULL,
            "SignalId" INT,
            "RelateSequenceId" VARCHAR(128),
            "EventCategoryId" INT NOT NULL,
            "EventStateId" INT NOT NULL,
            "CenterId" INT,
            "CenterName" VARCHAR(128),
            "StructureName" VARCHAR(128),
            "MonitorUnitName" VARCHAR(128),
            "StructureId" INT,
            "StationCategoryId" INT,
            "EquipmentVendor" VARCHAR(128),
            "ConvergenceEventId" BIGINT DEFAULT 0,
            "resourcestructureId" INT DEFAULT 0,
            "BaseEquipmentId" INT DEFAULT 0,
            NOT CLUSTER PRIMARY KEY("SequenceId"));

            CREATE OR REPLACE  INDEX "IDX_ActiveEvent_Complex_1" ON "tbl_activeevent"("StartTime" DESC,"StationId" ASC,"EquipmentId" ASC,"EventId" ASC,"EventConditionId" ASC);
            -- 告警自动确认需要使用
            create OR REPLACE INDEX "IDX_EventSeverityId_EndTime" on "TBL_ActiveEvent"("EventSeverityId","EndTime");
            -- 告警收敛使用
            CREATE OR REPLACE INDEX "IDX_EquipmentId_ConvergenceEventId_StartTime" ON "TBL_ActiveEvent" ("EquipmentId", "ConvergenceEventId", "StartTime");
            CREATE TABLE "tbl_activesignal"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(128),
            "SignalCategory" INT,
            "DataType" INT NOT NULL,
            "FloatValue" DOUBLE,
            "StringValue" VARCHAR(128),
            "DateTimeValue" TIMESTAMP(0),
            "SampleTime" TIMESTAMP(0) NOT NULL,
            "ShowPrecision" VARCHAR(20),
            "Unit" VARCHAR(64),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EventSeverity" INT,
            "EventSeverityName" VARCHAR(128),
            "Meanings" VARCHAR(255),
            "Flag" INT,
            "LastUpdate" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "LastUpdate", "SignalId", "StationId"));

            CREATE TABLE "tbl_alarmchange"
            (
            "SequenceId" VARCHAR(128) NOT NULL,
            "SerialNo" BIGINT IDENTITY(1480009, 1) NOT NULL,
            "OperationType" INT NOT NULL,
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "EventId" INT NOT NULL,
            "EventName" VARCHAR(128) NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventSeverityId" INT NOT NULL,
            "EventSeverity" VARCHAR(128),
            "EventLevel" INT,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "CancelTime" TIMESTAMP(0),
            "CancelUserId" INT,
            "CancelUserName" VARCHAR(128),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(128),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "ReversalNum" INT,
            "Meanings" VARCHAR(255),
            "EventFilePath" VARCHAR(255),
            "Description" VARCHAR(255),
            "SourceHostId" INT,
            "InstructionId" VARCHAR(255),
            "InstructionStatus" INT,
            "StandardAlarmNameId" INT,
            "StandardAlarmName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EquipmentCategory" INT NOT NULL,
            "EquipmentCategoryName" VARCHAR(128),
            "MaintainState" INT DEFAULT 0 NOT NULL,
            "SignalId" INT,
            "RelateSequenceId" VARCHAR(128),
            "EventCategoryId" INT NOT NULL,
            "EventStateId" INT NOT NULL,
            "CenterId" INT,
            "CenterName" VARCHAR(128),
            "StructureName" VARCHAR(128),
            "MonitorUnitName" VARCHAR(128),
            "StructureId" INT,
            "StationCategoryId" INT,
            "EquipmentVendor" VARCHAR(128),
            "ConvergenceEventId" BIGINT DEFAULT 0,
            "resourcestructureId" INT DEFAULT 0,
            "BaseEquipmentId" INT DEFAULT 0,
            "InsertTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
            NOT CLUSTER PRIMARY KEY("SerialNo"));

            CREATE OR REPLACE  INDEX "TBL_AlarmChange_ID1" ON "tbl_alarmchange"("SequenceId" ASC,"OperationType" ASC);

            CREATE TABLE "tbl_area"
            (
            "AreaId" INT NOT NULL,
            "AreaName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("AreaId"));

            CREATE TABLE "tbl_areamap"
            (
            "StationId" INT NOT NULL,
            "AreaId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("AreaId", "StationId"));

            CREATE TABLE "tbl_backupandcleartime"
            (
            "BackupAndClearTimeId" int PRIMARY KEY AUTO_INCREMENT,
            "DataType" INT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0));

            CREATE TABLE "tbl_configchangedefine"
            (
            "ConfigId" INT NOT NULL PRIMARY KEY,
            "EntityName" VARCHAR(255) NOT NULL,
            "TableName" VARCHAR(255) NOT NULL,
            "IdDefine" VARCHAR(255));

            CREATE TABLE "tbl_configchangemacrolog"
            (
            "ObjectId" VARCHAR(255) NOT NULL,
            "ConfigId" INT NOT NULL,
            "EditType" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("ObjectId","ConfigId","EditType"));

            CREATE OR REPLACE  INDEX "IDX_configchangemacrolog_1" ON "tbl_configchangemacrolog"("UpdateTime" DESC,"ConfigId" ASC);

            CREATE TABLE "tbl_configchangemap"
            (
            "MicroConfigId" INT NOT NULL,
            "MicroEditType" INT NOT NULL,
            "MacroConfigId" INT NOT NULL,
            "MacroEditType" INT NOT NULL,
            "IdConvertRule" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MicroConfigId", "MicroEditType"));

            CREATE TABLE "tbl_configchangemicrolog"
            (
            "ObjectId" VARCHAR(255) NOT NULL,
            "ConfigId" INT NOT NULL,
            "EditType" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("ObjectId", "ConfigId","EditType"));

            CREATE TABLE "tbl_configchecktask"
            (
            "ObjectType" VARCHAR(64) NOT NULL,
            "ObjectId" VARCHAR(128) NOT NULL,
            "Priority" INT NOT NULL,
            "CheckTime" TIMESTAMP(0) NOT NULL,
            "BugType" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("BugType", "ObjectId"));

            CREATE TABLE "tbl_control"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "ControlName" VARCHAR(128) NOT NULL,
            "ControlCategory" INT NOT NULL,
            "CmdToken" TEXT NOT NULL,
            "BaseTypeId" DECIMAL(12,0),
            "ControlSeverity" INT NOT NULL,
            "SignalId" INT,
            "TimeOut" DOUBLE,
            "Retry" INT,
            "Description" VARCHAR(255),
            "Enable" TINYINT NOT NULL,
            "Visible" TINYINT NOT NULL,
            "DisplayIndex" INT NOT NULL,
            "CommandType" INT NOT NULL,
            "ControlType" SMALLINT,
            "DataType" SMALLINT,
            "MaxValue" DOUBLE NOT NULL,
            "MinValue" DOUBLE NOT NULL,
            "DefaultValue" DOUBLE,
            "ModuleNo" INT DEFAULT 0 NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_TBLControl_1" ON "tbl_control"("EquipmentTemplateId" ASC);
            CREATE OR REPLACE  INDEX "IDX_TBLControl_2" ON "tbl_control"("EquipmentTemplateId" ASC,"ControlId" ASC);

            CREATE TABLE "tbl_controllogaction"
            (
            "LogActionId" INT NOT NULL,
            "ActionId" INT NOT NULL,
            "ActionName" VARCHAR(50),
            "EquipmentId" INT,
            "ControlId" INT,
            "ActionValue" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LogActionId", "ActionId"));

            CREATE TABLE "tbl_controlmeanings"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "ParameterValue" SMALLINT NOT NULL,
            "Meanings" VARCHAR(255),
            "BaseCondId" DECIMAL(12,0),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_TBLControlMeanings_1" ON "tbl_controlmeanings"("EquipmentTemplateId" ASC,"ControlId" ASC);

            CREATE TABLE "tbl_dataentry"
            (
            "EntryId" INT NOT NULL,
            "EntryCategory" INT,
            "EntryName" VARCHAR(128),
            "EntryTitle" VARCHAR(128),
            "EntryAlias" VARCHAR(255),
            "Enable" TINYINT DEFAULT 1 NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "tbl_dataitem"
            (
            "EntryItemId" INT NOT NULL,
            "ParentEntryId" INT DEFAULT 0 NOT NULL,
            "ParentItemId" INT DEFAULT 0 NOT NULL,
            "EntryId" INT NOT NULL,
            "ItemId" INT NOT NULL,
            "ItemValue" VARCHAR(128) NOT NULL,
            "ItemAlias" VARCHAR(255),
            "Enable" TINYINT DEFAULT 1 NOT NULL,
            "IsSystem" TINYINT DEFAULT 1 NOT NULL,
            "IsDefault" TINYINT DEFAULT 0 NOT NULL,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            "ExtendField5" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EntryItemId"));

            CREATE OR REPLACE  INDEX "IDX_DataItem_EntryId" ON "tbl_dataitem"("EntryId" ASC);

            CREATE TABLE "tbl_datastruct"
            (
            "EntryId" INT NOT NULL,
            "StructType" INT,
            "StructName" VARCHAR(128),
            "StructDescription" VARCHAR(256),
            "AttributeId" INT,
            "AttributeName" VARCHAR(128),
            "AttributeType" VARCHAR(128),
            "TypeDefine" VARCHAR(256),
            "Description" VARCHAR(256),
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "tbl_dbversionrecord"
            (
            "SerialNo" INT AUTO_INCREMENT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "Version" VARCHAR(30) NOT NULL,
            "Module" VARCHAR(255),
            "LastModifyTime" TIMESTAMP(0) NOT NULL,
            "Feature" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialNo"));

            CREATE TABLE "tbl_department"
            (
            "DepartmentId" INT NOT NULL,
            "DepartmentName" VARCHAR(128) NOT NULL,
            "DepartmentLevel" VARCHAR(20),
            "DepartmentFunction" VARCHAR(40),
            "ParentDeprtId" INT,
            "Description" VARCHAR(255),
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
            NOT CLUSTER PRIMARY KEY("DepartmentId"));

            CREATE TABLE "tbl_dynamicconfig"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT NOT NULL,
            "ConfigTime" TIMESTAMP(0) NOT NULL,
            "StationId" INT NOT NULL,
            "HostId" INT NOT NULL,
            "SyncFlag" INT DEFAULT (-2) NOT NULL,
            "SyncXml" TEXT NOT NULL,
            "SyncTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_DynamicConfig_1" ON "tbl_dynamicconfig"("ConfigTime" ASC,"HostId" ASC);

            CREATE TABLE "tbl_employee"
            (
            "EmployeeId" INT NOT NULL PRIMARY KEY,
            "DepartmentId" INT,
            "EmployeeName" VARCHAR(128) NOT NULL,
            "EmployeeType" INT,
            "EmployeeTitle" INT,
            "JobNumber" VARCHAR(20) NOT NULL,
            "Gender" INT,
            "Mobile" VARCHAR(50),
            "Phone" VARCHAR(50),
            "Email" VARCHAR(128),
            "Address" VARCHAR(255),
            "PostAddress" VARCHAR(255),
            "Enable" BIT DEFAULT 1 NOT NULL,
            "Description" VARCHAR(255),
            "IsAddTempUser" BIT DEFAULT 0 NOT NULL,
            "UserValidTime" INT DEFAULT 172800);

            CREATE TABLE "tbl_enumdata"
            (
            "EntryId" INT NOT NULL,
            "AttributeType" INT,
            "AttributeName" VARCHAR(128),
            "AttributeDescription" VARCHAR(256),
            "EnumId" INT,
            "EnumType" VARCHAR(128),
            "EnumValue" INT,
            "EnumDefine" VARCHAR(256),
            "Description" VARCHAR(256),
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "tbl_equipment"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "EquipmentNo" VARCHAR(128) NOT NULL,
            "EquipmentModule" VARCHAR(128),
            "EquipmentStyle" VARCHAR(128),
            "AssetState" INT,
            "Price" DOUBLE,
            "UsedLimit" DOUBLE,
            "UsedDate" TIMESTAMP(0),
            "BuyDate" TIMESTAMP(0),
            "Vendor" VARCHAR(255),
            "Unit" VARCHAR(255),
            "EquipmentCategory" INT NOT NULL,
            "EquipmentType" INT NOT NULL,
            "EquipmentClass" INT,
            "EquipmentState" INT NOT NULL,
            "EventExpression" VARCHAR(255),
            "StartDelay" DOUBLE,
            "EndDelay" DOUBLE,
            "Property" VARCHAR(255),
            "Description" VARCHAR(255),
            "EquipmentTemplateId" INT,
            "HouseId" INT,
            "MonitorUnitId" INT NOT NULL,
            "WorkStationId" INT,
            "SamplerUnitId" INT NOT NULL,
            "DisplayIndex" INT NOT NULL,
            "ConnectState" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "ParentEquipmentId" VARCHAR(255),
            "RatedCapacity" VARCHAR(255),
            "InstalledModule" TEXT NOT NULL,
            "ProjectName" VARCHAR(255),
            "ContractNo" VARCHAR(255),
            "InstallTime" TIMESTAMP(0),
            "EquipmentSN" VARCHAR(255),
            "SO" VARCHAR(255),
            "ResourceStructureId" INT DEFAULT 0,
            "ExtValue" VARCHAR(8188),
            "Photo" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId"),
            CHECK("ExtValue" IS JSON ));

            CREATE OR REPLACE  INDEX "IDC_EquipmentId_MonitorUnit_ID" ON "tbl_equipment"("MonitorUnitId" ASC);
            CREATE OR REPLACE  INDEX "IDX_Equipment_StationId" ON "tbl_equipment"("StationId" ASC,"HouseId" ASC);
            CREATE OR REPLACE  INDEX "IDX_EquipmentId_1" ON "tbl_equipment"("MonitorUnitId" ASC,"SamplerUnitId" ASC);
            CREATE OR REPLACE  INDEX "IDX_Equipment_ResourceStructureId" ON "tbl_equipment"("ResourceStructureId" ASC);
            CREATE OR REPLACE  INDEX "IDX_EquipmentTemplateId" ON "tbl_equipment"("EquipmentTemplateId" ASC);

            CREATE TABLE "tbl_equipmentidmap"
            (
            "EquipmentIdMapId" int PRIMARY KEY AUTO_INCREMENT,
            "OldStationId" INT,
            "OldEquipmentId" INT,
            "KoloStationId" INT,
            "KoloEquipmentId" INT,
            "OPostCode" INT);

            CREATE TABLE "tbl_equipmentkeyvalue"
            (
            "EquipmentType" INT NOT NULL,
            "EquipmentCategory" INT NOT NULL,
            "MinValue" INT,
            "CurrentValue" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentType", "EquipmentCategory"));

            CREATE TABLE "tbl_equipmentmaintain"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentState" INT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            "Description" VARCHAR(255),
            "ExtendFiled1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentmask"
            (
            "EquipmentId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "TimeGroupId" INT,
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentprojectinfo"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "ProjectName" VARCHAR(255),
            "ContractNo" VARCHAR(255),
            "InstallTime" TIMESTAMP(0),
            "EquipmentSN" VARCHAR(255),
            "SO" VARCHAR(255),
            CONSTRAINT "TBL_EquipmentProjectInfo_IDX1" PRIMARY KEY("StationId", "EquipmentId"));

            CREATE TABLE "tbl_equipmenttemplate"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "EquipmentTemplateName" VARCHAR(128) NOT NULL,
            "ParentTemplateId" INT NOT NULL,
            "Memo" VARCHAR(255) NOT NULL,
            "ProtocolCode" VARCHAR(32) NOT NULL,
            "EquipmentCategory" INT NOT NULL,
            "EquipmentType" INT NOT NULL,
            "Property" VARCHAR(255),
            "Description" VARCHAR(255),
            "EquipmentStyle" VARCHAR(128),
            "Unit" VARCHAR(255),
            "Vendor" VARCHAR(255),
            "Photo" VARCHAR(255),
            "EquipmentBaseType" INT,
            "StationCategory" INT,
            "ExtendField1" VARCHAR(255),
            "SecondaryCategory" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId"));

            CREATE TABLE "tbl_equipmenttemplateidmap"
            (
            "EquipmentTemplateIdMapId" int PRIMARY KEY AUTO_INCREMENT,
            "OldEquipmentTemplateId" INT,
            "KoloEquipmentTemplateId" INT);

            CREATE TABLE "tbl_equiptemplatebaseconfirm"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "ConfirmTime" TIMESTAMP(0) NOT NULL,
            "ConfirmUser" INT NOT NULL,
            "Reason" TEXT,
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId"));

            CREATE TABLE "tbl_event"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventName" VARCHAR(128) NOT NULL,
            "StartType" INT NOT NULL,
            "EndType" INT NOT NULL,
            "StartExpression" TEXT,
            "SuppressExpression" TEXT,
            "EventCategory" INT NOT NULL,
            "SignalId" INT,
            "Enable" TINYINT NOT NULL,
            "Visible" TINYINT NOT NULL,
            "Description" VARCHAR(255),
            "DisplayIndex" INT,
            "ModuleNo" INT DEFAULT 0 NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_TBLEvent_1" ON "tbl_event"("EquipmentTemplateId" ASC);
            CREATE OR REPLACE  INDEX "IDX_TBLEvent_2" ON "tbl_event"("EquipmentTemplateId" ASC,"EventId" ASC);
            CREATE OR REPLACE  INDEX "IDX_Event_Signal_1" ON "tbl_event"("EquipmentTemplateId" ASC,"SignalId" ASC);

            CREATE TABLE "tbl_eventcondition"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "StartOperation" VARCHAR(4) NOT NULL,
            "StartCompareValue" DOUBLE NOT NULL,
            "StartDelay" INT NOT NULL,
            "EndOperation" VARCHAR(4),
            "EndCompareValue" DOUBLE,
            "EndDelay" INT,
            "Frequency" INT,
            "FrequencyThreshold" INT,
            "Meanings" VARCHAR(255),
            "EquipmentState" INT,
            "BaseTypeId" DECIMAL(12,0),
            "EventSeverity" INT NOT NULL,
            "StandardName" INT,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_TBLEventCondition_1" ON "tbl_eventcondition"("EquipmentTemplateId" ASC,"EventId" ASC);

            CREATE TABLE "tbl_eventex"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "turnover" INT,
            "ExtendField1" VARCHAR(20),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_TBLEventex_1" ON "tbl_eventex"("EquipmentTemplateId" ASC);
            CREATE OR REPLACE  INDEX "IDX_TBLEventex_2" ON "tbl_eventex"("EquipmentTemplateId" ASC,"EventId" ASC);

            CREATE TABLE "tbl_eventlogaction"
            (
            "LogActionId" INT NOT NULL PRIMARY KEY,
            "ActionName" VARCHAR(255) NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "TriggerType" INT NOT NULL,
            "StartExpression" VARCHAR(255),
            "SuppressExpression" VARCHAR(255),
            "InformMsg" VARCHAR(255),
            "Description" VARCHAR(255));

            CREATE TABLE "tbl_eventmask"
            (
            "EquipmentId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "TimeGroupId" INT,
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "EventId", "StationId"));

            CREATE TABLE "tbl_eventmaskhistory"
            (
            "SequenceId" VARCHAR(128) NOT NULL PRIMARY KEY,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "Meanings" VARCHAR(255),
            "BaseTypeId" DECIMAL(12,0),
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0));

            CREATE TABLE "tbl_historycontrol"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "ControlId" INT NOT NULL,
            "ControlName" VARCHAR(128) NOT NULL,
            "SerialNo" INT NOT NULL,
            "ControlSeverity" INT NOT NULL,
            "CmdToken" TEXT NOT NULL,
            "ControlPhase" INT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(255),
            "ControlResultType" INT,
            "ControlResult" VARCHAR(255),
            "ControlExecuterId" INT,
            "ControlExecuterIdName" VARCHAR(255),
            "ControlType" INT,
            "ActionId" INT,
            "Description" VARCHAR(255),
            "Retry" INT,
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "ParameterValues" TEXT NOT NULL,
            "BaseCondId" DECIMAL(12,0),
            CONSTRAINT "TBL_HistoryControl_IDX1" PRIMARY KEY("StartTime", "StationId", "EquipmentId", "ControlId", "SerialNo"));

            CREATE TABLE "tbl_historycontrolmid"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "ControlId" INT NOT NULL,
            "ControlName" VARCHAR(128) NOT NULL,
            "SerialNo" INT NOT NULL,
            "ControlSeverity" INT NOT NULL,
            "CmdToken" TEXT NOT NULL,
            "ControlPhase" INT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(255),
            "ControlResultType" INT,
            "ControlResult" VARCHAR(255),
            "ControlExecuterId" INT,
            "ControlExecuterIdName" VARCHAR(255),
            "ControlType" INT,
            "ActionId" INT,
            "Description" VARCHAR(255),
            "Retry" INT,
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "ParameterValues" TEXT NOT NULL,
            "BaseCondId" DECIMAL(12,0),
            CONSTRAINT "TBL_HistoryControlMid_IDX1" PRIMARY KEY("StartTime","StationId","EquipmentId","ControlId","SerialNo"));

            CREATE TABLE "tbl_historyevent"
            (
            "SequenceId" VARCHAR(128) NOT NULL,
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "EventId" INT NOT NULL,
            "EventName" VARCHAR(128) NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventSeverityId" INT NOT NULL,
            "EventSeverity" VARCHAR(128) NOT NULL,
            "EventLevel" INT,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL,
            "CancelTime" TIMESTAMP(0),
            "CancelUserId" INT,
            "CancelUserName" VARCHAR(128),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(128),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "ReversalNum" INT,
            "Meanings" VARCHAR(255),
            "EventFilePath" VARCHAR(255),
            "Description" VARCHAR(255),
            "SourceHostId" INT,
            "InstructionId" VARCHAR(255),
            "InstructionStatus" INT,
            "StandardAlarmNameId" INT,
            "StandardAlarmName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EquipmentCategory" INT,
            "EquipmentCategoryName" VARCHAR(128),
            "MaintainState" INT DEFAULT 0 NOT NULL,
            "SignalId" INT,
            "RelateSequenceId" VARCHAR(128),
            "EventCategoryId" INT,
            "EventStateId" INT,
            "CenterId" INT,
            "CenterName" VARCHAR(128),
            "StructureName" VARCHAR(128),
            "MonitorUnitName" VARCHAR(128),
            "StructureId" INT,
            "StationCategoryId" INT,
            "EquipmentVendor" VARCHAR(128),
            "ConvergenceEventId" BIGINT DEFAULT 0,
            "resourcestructureId" INT DEFAULT 0,
            "BaseEquipmentId" INT DEFAULT 0,
            CONSTRAINT "TBL_HistoryEvent_IDX1" PRIMARY KEY("StartTime", "StationId", "EquipmentId", "EventId", "EventConditionId"));

            CREATE OR REPLACE  INDEX "TBL_HistoryEvent_IDX2" ON "tbl_historyevent"("StructureId" ASC,"StationCategoryId" ASC,"EquipmentCategory" ASC,"StationId" ASC,"EventStateId" ASC,"EventCategoryId" ASC,"EventConditionId" ASC,"StartTime" ASC);
            CREATE OR REPLACE  INDEX "TBL_HistoryEvent_IDX3" ON "tbl_historyevent"("StartTime" ASC,"BaseTypeId" ASC,"StationId" ASC,"EquipmentId" ASC,"EventSeverityId" ASC,"SignalId" ASC);

            CREATE TABLE "tbl_historyeventmask"
            (
            "SequenceId" VARCHAR(128) NOT NULL PRIMARY KEY,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "Meanings" VARCHAR(255),
            "BaseTypeId" DECIMAL(12,0),
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL);

            CREATE TABLE "tbl_historyeventmaskmid"
            (
            "SequenceId" VARCHAR(128) NOT NULL PRIMARY KEY,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "Meanings" VARCHAR(255),
            "BaseTypeId" DECIMAL(12,0),
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL);

            CREATE TABLE "tbl_historyeventmid"
            (
            "SequenceId" VARCHAR(128) NOT NULL,
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "EventId" INT NOT NULL,
            "EventName" VARCHAR(128) NOT NULL,
            "EventConditionId" INT NOT NULL,
            "EventSeverityId" INT NOT NULL,
            "EventSeverity" VARCHAR(128) NOT NULL,
            "EventLevel" INT,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL,
            "CancelTime" TIMESTAMP(0),
            "CancelUserId" INT,
            "CancelUserName" VARCHAR(128),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(128),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "ReversalNum" INT,
            "Meanings" VARCHAR(255),
            "EventFilePath" VARCHAR(255),
            "Description" VARCHAR(255),
            "SourceHostId" INT,
            "InstructionId" VARCHAR(255),
            "InstructionStatus" INT,
            "StandardAlarmNameId" INT,
            "StandardAlarmName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EquipmentCategory" INT,
            "EquipmentCategoryName" VARCHAR(128),
            "MaintainState" INT DEFAULT 0 NOT NULL,
            "SignalId" INT,
            "RelateSequenceId" VARCHAR(128),
            "EventCategoryId" INT,
            "EventStateId" INT,
            "CenterId" INT,
            "CenterName" VARCHAR(128),
            "StructureName" VARCHAR(128),
            "MonitorUnitName" VARCHAR(128),
            "StructureId" INT,
            "StationCategoryId" INT,
            "EquipmentVendor" VARCHAR(128),
            "ConvergenceEventId" BIGINT DEFAULT 0,
            "ResourcestructureId" INT DEFAULT 0,
            "BaseEquipmentId" INT DEFAULT 0,
            CONSTRAINT "TBL_HistoryEventMid_IDX1" PRIMARY KEY("StartTime","StationId","EquipmentId","EventId","EventConditionId"));

            CREATE TABLE "tbl_historypassword"
            (
            "UserId" INT NOT NULL,
            "Password" VARCHAR(128),
            "RecordTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("RecordTime", "UserId"));

            CREATE TABLE "tbl_historyselection"
            (
            "HistorySelectionId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT NOT NULL,
            "SelectionType" VARCHAR(128) NOT NULL,
            "SelectionName" VARCHAR(255) NOT NULL,
            "SelectionContent" CLOB NOT NULL,
            "Description" VARCHAR(255),
            "CreateTime" TIMESTAMP(0),
            "QueryInformation" CLOB,
            NOT CLUSTER PRIMARY KEY("HistorySelectionId"));

            CREATE TABLE "tbl_hourlysignal"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "RecordTime" TIMESTAMP(0) NOT NULL,
            "DataType" INT,
            "FloatValue" DOUBLE,
            "StringValue" VARCHAR(255),
            "ReportTime" TIMESTAMP(0) NOT NULL,
            "SignalPropertyId" INT DEFAULT 0 NOT NULL,
            CONSTRAINT "TBL_HourlySignal_IDX1" PRIMARY KEY("RecordTime", "StationId", "EquipmentId", "SignalId"));

            CREATE TABLE "tbl_house"
            (
            "HouseId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "HouseName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
            NOT CLUSTER PRIMARY KEY("HouseId", "StationId"));

            CREATE TABLE "tbl_logicclassentry"
            (
            "EntryId" INT NOT NULL,
            "EntryCategory" INT,
            "LogicClassId" INT,
            "LogicClass" VARCHAR(128),
            "StandardType" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EntryId", "StandardType"));

            CREATE TABLE "tbl_loginformlist"
            (
            "LoginFormListId" int PRIMARY KEY AUTO_INCREMENT,
            "LogActionId" INT NOT NULL,
            "InformerId" INT NOT NULL,
            "UserId" INT,
            "InfoType" INT,
            "Description" VARCHAR(255));

            CREATE TABLE "tbl_logininfo"
            (
            "UserId" INT NOT NULL,
            "LoginType" INT NOT NULL,
            "LoginTime" TIMESTAMP(0) NOT NULL,
            "IPAddress" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LoginTime", "LoginType", "UserId"));

            CREATE TABLE "tbl_logininfomid"
            (
            "UserId" INT NOT NULL,
            "LoginType" INT NOT NULL,
            "LoginTime" TIMESTAMP(0) NOT NULL,
            "IPAddress" VARCHAR(255),
            CONSTRAINT "TBL_LoginInfoMid_IDX1" PRIMARY KEY("UserId", "LoginType", "LoginTime"));

            CREATE TABLE "tbl_middletbl"
            (
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "SerialId" INT NOT NULL,
            "BusinessTypeName" VARCHAR(255) NOT NULL,
            "ExpressionName" VARCHAR(255) NOT NULL,
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(128) NOT NULL,
            "SignalCategory" INT NOT NULL,
            "DataType" INT NOT NULL,
            "FloatValue" DOUBLE,
            "StringValue" VARCHAR(128),
            "DateTimeValue" TIMESTAMP(0),
            "SampleTime" TIMESTAMP(0) NOT NULL,
            "ShowPrecision" VARCHAR(20),
            "Unit" VARCHAR(64),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EventSeverity" INT,
            "EventSeverityName" VARCHAR(128),
            "Meanings" VARCHAR(255),
            "ThresholdType" INT,
            "BusinessState" INT,
            "BaseCondId" DECIMAL(12,0),
            "BaseMeaning" VARCHAR(255),
            CONSTRAINT "TBL_MiddleTbl_IDX1" PRIMARY KEY("ExpressionId", "SampleTime", "StationId", "EquipmentId", "SignalId"));

            CREATE OR REPLACE  INDEX "TBL_MiddleTbl_IDX2" ON "tbl_middletbl"("BusinessTypeId" ASC,"ExpressionId" ASC,"StationId" ASC,"EquipmentId" ASC,"SignalId" ASC,"SampleTime" ASC,"FloatValue" ASC);

            CREATE TABLE "tbl_midxxxxmid"
            (
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "SerialId" INT NOT NULL,
            "BusinessTypeName" VARCHAR(255) NOT NULL,
            "ExpressionName" VARCHAR(255) NOT NULL,
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(128) NOT NULL,
            "SignalCategory" INT NOT NULL,
            "DataType" INT NOT NULL,
            "FloatValue" DOUBLE,
            "StringValue" VARCHAR(128),
            "DateTimeValue" TIMESTAMP(0),
            "SampleTime" TIMESTAMP(0) NOT NULL,
            "ShowPrecision" VARCHAR(20),
            "Unit" VARCHAR(64),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            "EventSeverity" INT,
            "EventSeverityName" VARCHAR(128),
            "Meanings" VARCHAR(255),
            "ThresholdType" INT,
            "BusinessState" INT,
            "BaseCondId" DECIMAL(12,0),
            "BaseMeaning" VARCHAR(255),
            "ComeFromTableName" VARCHAR(64) NOT NULL,
            CONSTRAINT "TBL_MidXxxxMid_IDX1" PRIMARY KEY("ExpressionId", "SampleTime", "StationId", "EquipmentId", "SignalId"));

            CREATE TABLE "tbl_monitorunitprojectinfo"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "ProjectName" VARCHAR(255),
            "ContractNo" VARCHAR(255),
            "InstallTime" TIMESTAMP(0),
            CONSTRAINT "TBL_MUProjectInfo_IDX1" PRIMARY KEY("StationId", "MonitorUnitId"));

            CREATE TABLE "tbl_mufullcfgstate"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "ConfigFileCode" VARCHAR(32) NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "State" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("MonitorUnitId", "StationId"));

            CREATE TABLE "tbl_musyncrecord"
            (
            "RecordId" INT AUTO_INCREMENT NOT NULL,
            "StationId" INT NOT NULL,
            "TaskId" INT,
            "MonitorUnitId" INT NOT NULL,
            "SyncResult" INT NOT NULL,
            "SyncTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("RecordId", "StationId"));

            CREATE TABLE "tbl_musynctask"
            (
            "TaskId" INT AUTO_INCREMENT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "SyncState" INT NOT NULL,
            "SyncRule" INT NOT NULL,
            "PlanTime" TIMESTAMP(0),
            "BeginTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UpdateTime" TIMESTAMP(0),
            "MaxRetryCount" INT NOT NULL,
            "RetryCount" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("TaskId"));

            CREATE TABLE "tbl_newinstation"
            (
            "IpAddress" VARCHAR(128) NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "Id" INT AUTO_INCREMENT NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_newsystemconfigs"
            (
            "SystemConfigId" INT NOT NULL,
            "SystemConfigKey" VARCHAR(255),
            "SystemConfigValue" VARCHAR(255),
            "Description" VARCHAR(255),
            CONSTRAINT "PK_TBL_NewSystemConfigs_ID" PRIMARY KEY("SystemConfigId"));

            CREATE TABLE "tbl_notificationsn"
            (
            "NotificationSnId" int PRIMARY KEY AUTO_INCREMENT,
            "SerialNo" DECIMAL(14,0));

            CREATE TABLE "tbl_notifycommand"
            (
            "SequenceId" DECIMAL(22,17) NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "CommandId" INT NOT NULL,
            "CommandSeverity" INT,
            "CmdToken" TEXT,
            "CommandPhase" INT,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "CommandResultType" INT,
            "CommandResult" VARCHAR(255),
            "CommandExecuterId" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SequenceId"));

            CREATE TABLE "tbl_onlineuser"
            (
            "OnlineUserId" int PRIMARY KEY AUTO_INCREMENT,
            "BusinessModuleType" INT,
            "LogonId" VARCHAR(255),
            "UserId" INT NOT NULL,
            "UserName" VARCHAR(255),
            "LoginTime" TIMESTAMP(0),
            "LastUpdateTime" TIMESTAMP(0),
            "loginIP" VARCHAR(255),
            "Description" VARCHAR(255),
            "Status" INT,
            "Token" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255));

            CREATE TABLE "tbl_operation"
            (
            "OperationId" INT NOT NULL,
            "OperationCategory" INT,
            "OperationName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            "MenusItemId" INT,
            NOT CLUSTER PRIMARY KEY("OperationId"));

            CREATE TABLE "tbl_operationdetail"
            (
            "OperationDetailId" int PRIMARY KEY AUTO_INCREMENT,
            "UserId" INT NOT NULL,
            "ObjectId" VARCHAR(128),
            "ObjectType" INT NOT NULL,
            "PropertyName" VARCHAR(128),
            "OperationTime" TIMESTAMP(0) NOT NULL,
            "OperationType" VARCHAR(64) NOT NULL,
            "OldValue" VARCHAR(4000),
            "NewValue" VARCHAR(4000));

            CREATE TABLE "tbl_operationdetailmid"
            (
            "OperationDetailMidId" int PRIMARY KEY AUTO_INCREMENT,
            "UserId" INT NOT NULL,
            "ObjectId" VARCHAR(128),
            "ObjectType" INT NOT NULL,
            "PropertyName" VARCHAR(128),
            "OperationTime" TIMESTAMP(0) NOT NULL,
            "OperationType" VARCHAR(64) NOT NULL,
            "OldValue" VARCHAR(255),
            "NewValue" VARCHAR(255));

            CREATE TABLE "tbl_operationgroup"
            (
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("GroupId"));

            CREATE TABLE "tbl_operationgroupmap"
            (
            "OperationId" INT NOT NULL,
            "GroupId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("GroupId", "OperationId"));

            CREATE TABLE "tbl_operationrecord"
            (
            "UserId" INT NOT NULL,
            "StationId" INT,
            "StationName" VARCHAR(255),
            "Operation" INT NOT NULL,
            "OperationTime" TIMESTAMP(0) NOT NULL,
            "OperationType" INT,
            "OperationContent" VARCHAR(3000) NOT NULL,
            CONSTRAINT "IDX_operationrecord" PRIMARY KEY("OperationTime", "UserId", "Operation"));


            CREATE TABLE "tbl_operationrecordmid"
            (
            "UserId" INT NOT NULL,
            "StationId" INT,
            "StationName" VARCHAR(255),
            "Operation" INT NOT NULL,
            "OperationTime" TIMESTAMP(0) NOT NULL,
            "OperationType" INT,
            "OperationContent" VARCHAR(255) NOT NULL,
            CONSTRAINT "IDX_operationrecordmid" PRIMARY KEY("OperationTime", "UserId", "Operation"));

            CREATE TABLE "tbl_originbussinesscategorymap"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "OriginCategory" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId"));

            CREATE TABLE "tbl_primaryalarm"
            (
            "FilterId" INT NOT NULL,
            "StationCategory" INT NOT NULL,
            "PrimaryStationId" INT NOT NULL,
            "PrimaryEquipmentId" INT NOT NULL,
            "PrimaryBaseTypeId" INT NOT NULL,
            CONSTRAINT "TBL_PrimaryAlarm_ID1" PRIMARY KEY("FilterId", "StationCategory", "PrimaryStationId", "PrimaryEquipmentId", "PrimaryBaseTypeId"));

            CREATE TABLE "tbl_primarykeyidentity"
            (
            "TableId" INT NOT NULL,
            "TableName" VARCHAR(30),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("TableId"));

            CREATE TABLE "tbl_primarykeyvalue"
            (
            "TableId" INT NOT NULL,
            "PostalCode" INT NOT NULL,
            "MinValue" INT,
            "CurrentValue" INT,
            NOT CLUSTER PRIMARY KEY("PostalCode", "TableId"));

            CREATE TABLE "tbl_primarysecondmaprule"
            (
            "FilterId" INT NOT NULL,
            "RuleName" VARCHAR(255) NOT NULL,
            "ProcessDelay" INT NOT NULL,
            "Description" VARCHAR(255),
            CONSTRAINT "TBL_PrimarySecondMapRule_ID1" PRIMARY KEY("FilterId"));

            CREATE TABLE "tbl_projectstatehouse"
            (
            "HouseId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "HouseName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            "LastUpdateDate" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("HouseId", "StationId"));

            CREATE TABLE "tbl_projectstateoperation"
            (
            "OperationId" INT AUTO_INCREMENT NOT NULL,
            "OperationType" INT NOT NULL,
            "Operation" VARCHAR(100),
            "StationId" INT,
            "HouseId" INT,
            "EquipmentId" INT,
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            "OperationDate" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("OperationId"));

            CREATE TABLE "tbl_projectstatestation"
            (
            "StationId" INT NOT NULL,
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            "LastUpdateDate" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("StationId"));

            CREATE TABLE "tbl_replicatelogs"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "LogStr" TEXT NOT NULL,
            "InsertTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE TABLE "tbl_rmuworkstationservice"
            (
            "WorkStationId" INT NOT NULL,
            "WorkStationName" VARCHAR(255),
            "SCLoginIP" VARCHAR(64),
            "SCServicePort" VARCHAR(64),
            "SCLoginPort" VARCHAR(64),
            "SUURLSuffix" VARCHAR(64),
            "HeartPeriod" VARCHAR(64),
            "UserName" VARCHAR(64),
            "PassWord" VARCHAR(64),
            "FtpUser" VARCHAR(64),
            "FtpPass" VARCHAR(64),
            "SUServiceIP" VARCHAR(64),
            "SUServicePort" VARCHAR(64),
            "SUVer" VARCHAR(64),
            "AskConfigInterval" VARCHAR(64),
            "LastAskFactoryTime" TIMESTAMP(0),
            "ExtendField1" VARCHAR(128),
            "ExtendField2" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("WorkStationId"));

            CREATE OR REPLACE  INDEX "TBL_RMUWorkStationService_IDX1" ON "tbl_rmuworkstationservice"("WorkStationId" ASC);

            CREATE TABLE "tbl_sampleridmap"
            (
            "SamplerIdMapId" int PRIMARY KEY AUTO_INCREMENT,
            "OldSamplerId" INT,
            "KoloSamplerId" INT);

            CREATE TABLE "tbl_secondaryalarm"
            (
            "FilterId" INT NOT NULL,
            "StationCategory" INT NOT NULL,
            "SecondaryStationId" INT NOT NULL,
            "SecondaryEquipmentId" INT NOT NULL,
            "SecondaryBaseTypeId" INT NOT NULL,
            CONSTRAINT "TBL_SecondaryAlarm_ID1" PRIMARY KEY("FilterId", "StationCategory", "SecondaryStationId", "SecondaryEquipmentId", "SecondaryBaseTypeId"));

            CREATE TABLE "tbl_serialno"
            (
            "TableName" VARCHAR(125) NOT NULL PRIMARY KEY,
            "SerialNo" DECIMAL(12,0) NOT NULL);

            CREATE TABLE "tbl_signal"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "Enable" TINYINT NOT NULL,
            "Visible" TINYINT NOT NULL,
            "Description" VARCHAR(255),
            "SignalName" VARCHAR(128) NOT NULL,
            "SignalCategory" INT NOT NULL,
            "SignalType" INT NOT NULL,
            "ChannelNo" INT NOT NULL,
            "ChannelType" INT NOT NULL,
            "Expression" TEXT,
            "DataType" INT,
            "ShowPrecision" VARCHAR(20),
            "Unit" VARCHAR(64),
            "StoreInterval" DOUBLE,
            "AbsValueThreshold" DOUBLE,
            "PercentThreshold" DOUBLE,
            "StaticsPeriod" INT,
            "BaseTypeId" DECIMAL(12,0),
            "ChargeStoreInterVal" DOUBLE,
            "ChargeAbsValue" DOUBLE,
            "DisplayIndex" INT NOT NULL,
            "MDBSignalId" INT,
            "ModuleNo" INT DEFAULT 0 NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_TBLSignal_1" ON "tbl_signal"("EquipmentTemplateId" ASC);
            CREATE OR REPLACE  INDEX "IDX_TBLSignal_2" ON "tbl_signal"("EquipmentTemplateId" ASC,"SignalId" ASC);
            CREATE OR REPLACE  INDEX "IDX_TBLSignal_3" ON "tbl_signal"("EquipmentTemplateId" ASC,"BaseTypeId" ASC);
            CREATE OR REPLACE  INDEX "IDX_BaseTypeId" ON "tbl_signal"("BaseTypeId" ASC);

            CREATE TABLE "tbl_signalmeanings"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "StateValue" SMALLINT NOT NULL,
            "Meanings" VARCHAR(255),
            "BaseCondId" DECIMAL(12,0),
            NOT CLUSTER PRIMARY KEY("Id"),
            CONSTRAINT "CLUSTERED" UNIQUE("EquipmentTemplateId", "SignalId", "StateValue"));

            CREATE OR REPLACE  INDEX "IDX_TBLSignalMeanings_1" ON "tbl_signalmeanings"("EquipmentTemplateId" ASC,"SignalId" ASC);

            CREATE TABLE "tbl_signalproperty"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalPropertyId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"),
            CONSTRAINT "index_27" UNIQUE("EquipmentTemplateId", "SignalId", "SignalPropertyId"));

            CREATE OR REPLACE  INDEX "IDX_TBLSignalProperty_1" ON "tbl_signalproperty"("EquipmentTemplateId" ASC,"SignalId" ASC);

            CREATE TABLE "tbl_signalstatistics"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(128) NOT NULL,
            "StatisticsTime" TIMESTAMP(0) NOT NULL,
            "MinValue" DOUBLE,
            "MinTime" TIMESTAMP(0),
            "MaxValue" DOUBLE,
            "MaxTime" TIMESTAMP(0),
            "AvgValue" DOUBLE,
            "AvgTime" TIMESTAMP(0),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            CONSTRAINT "TBL_SignalStatistics_IDX1" PRIMARY KEY("StatisticsTime", "StationId", "EquipmentId", "SignalId"));

            CREATE TABLE "tbl_signalstatisticsmid"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(128) NOT NULL,
            "StatisticsTime" TIMESTAMP(0) NOT NULL,
            "MinValue" DOUBLE,
            "MinTime" TIMESTAMP(0),
            "MaxValue" DOUBLE,
            "MaxTime" TIMESTAMP(0),
            "AvgValue" DOUBLE,
            "AvgTime" TIMESTAMP(0),
            "BaseTypeId" DECIMAL(12,0),
            "BaseTypeName" VARCHAR(128),
            CONSTRAINT "TBL_SignalStatisticsMid_IDX1" PRIMARY KEY("StatisticsTime","StationId","EquipmentId","SignalId"));

            CREATE TABLE "tbl_spaceusedresult"
            (
            "SpaceUsedResultId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "tablename" TEXT,
            "rows" DECIMAL(17,0),
            "reserved" DECIMAL(17,0),
            "data" DECIMAL(17,0),
            "indexp" DECIMAL(17,0),
            "unused" DECIMAL(17,0));

            CREATE TABLE "tbl_specialtygroup"
            (
            "SpecialtyGroupId" INT NOT NULL,
            "SpecialtyGroupName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SpecialtyGroupId"));

            CREATE TABLE "tbl_specialtygroupmap"
            (
            "SpecialtyGroupId" INT NOT NULL,
            "EntryItemId" INT NOT NULL,
            "Operation" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EntryItemId", "SpecialtyGroupId"));

            CREATE TABLE "tbl_station"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "Latitude" DECIMAL(22,17),
            "Longitude" DECIMAL(22,17),
            "SetupTime" TIMESTAMP(0),
            "CompanyId" INT,
            "ConnectState" INT DEFAULT 2 NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "StationCategory" INT NOT NULL,
            "StationGrade" INT NOT NULL,
            "StationState" INT NOT NULL,
            "ContactId" INT,
            "SupportTime" INT,
            "OnWayTime" DOUBLE,
            "SurplusTime" DOUBLE,
            "FloorNo" VARCHAR(50),
            "PropList" VARCHAR(255),
            "Acreage" DOUBLE,
            "BuildingType" INT,
            "ContainNode" TINYINT DEFAULT 0 NOT NULL,
            "Description" VARCHAR(255),
            "BordNumber" INT,
            "CenterId" INT NOT NULL,
            "Enable" TINYINT DEFAULT 1 NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "ProjectName" VARCHAR(255),
            "ContractNo" VARCHAR(255),
            "InstallTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("StationId"));

            CREATE TABLE "tbl_stationbasemap"
            (
            "StationBaseType" INT NOT NULL,
            "StationCategory" INT NOT NULL,
            "StandardType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("StandardType", "StationBaseType", "StationCategory"));

            CREATE TABLE "tbl_stationbasetype"
            (
            "Id" INT NOT NULL,
            "StandardId" INT NOT NULL,
            "Type" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("Id", "StandardId"));

            CREATE TABLE "tbl_stationidmap"
            (
            "StationIdMap" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "OldStationId" INT,
            "KoloStationId" INT,
            "OPostCode" INT);

            CREATE TABLE "tbl_stationmask"
            (
            "StationId" INT NOT NULL,
            "TimeGroupId" INT,
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            NOT CLUSTER PRIMARY KEY("StationId"));

            CREATE TABLE "tbl_stationprojectinfo"
            (
            "StationId" INT NOT NULL,
            "ProjectName" VARCHAR(255),
            "ContractNo" VARCHAR(255),
            "InstallTime" TIMESTAMP(0),
            CONSTRAINT "TBL_StationProjectInfo_IDX1" PRIMARY KEY("StationId"));

            CREATE TABLE "tbl_stationstructure"
            (
            "StructureId" INT NOT NULL,
            "StructureGroupId" INT NOT NULL,
            "ParentStructureId" INT NOT NULL,
            "StructureName" VARCHAR(128) NOT NULL,
            "IsUngroup" BIT NOT NULL,
            "StructureType" INT NOT NULL,
            "MapZoom" DOUBLE,
            "Longitude" DECIMAL(22,17),
            "Latitude" DECIMAL(22,17),
            "Description" VARCHAR(255),
            "LevelPath" VARCHAR(200) NOT NULL,
            "Enable" BIT NOT NULL,
            NOT CLUSTER PRIMARY KEY("StructureId"));

            CREATE TABLE "tbl_stationstructureidmap"
            (
            "StationStructureIdMap" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "OldStructureId" INT,
            "KoloStructureId" INT,
            "OPostCode" INT);

            CREATE TABLE "tbl_stationstructuremap"
            (
            "StructureId" INT NOT NULL,
            "StationId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("StationId", "StructureId"));

            CREATE TABLE "tbl_stationswatchmap"
            (
            "SwatchStationId" INT NOT NULL,
            "StationId" INT NOT NULL,
            CONSTRAINT "IDX_StationSwatchMap" PRIMARY KEY("SwatchStationId", "StationId"));

            CREATE TABLE "tbl_stationvendormap"
            (
            "StationId" INT NOT NULL,
            "VendorName" VARCHAR(50) NOT NULL,
            NOT CLUSTER PRIMARY KEY("StationId", "VendorName"));

            CREATE TABLE "tbl_stationvip"
            (
            "StationID" INT NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL,
            "ExtendField" VARCHAR(255),
            CONSTRAINT "IDX_STATION_VIP" PRIMARY KEY("StationID"));

            CREATE TABLE "tbl_swatchstation"
            (
            "SwatchStationId" INT IDENTITY(2, 1) NOT NULL PRIMARY KEY,
            "SwatchStationName" VARCHAR(128) NOT NULL,
            "StationId" INT NOT NULL,
            "CreateTime" TIMESTAMP(0) NOT NULL,
            "Description" VARCHAR(255));

            CREATE TABLE "tbl_sysconfig"
            (
            "ConfigKey" VARCHAR(255) NOT NULL PRIMARY KEY,
            "ConfigValue" VARCHAR(1024));

            CREATE TABLE "tbl_timegroup"
            (
            "TimeGroupId" INT NOT NULL,
            "TimeGroupCategory" INT NOT NULL,
            "TimeGroupName" VARCHAR(128) NOT NULL,
            "TimeGroupType" SMALLINT NOT NULL,
            "TimeGroupException" BIT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL,
            "LastUpdateDate" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("TimeGroupId"));

            CREATE TABLE "tbl_timegroupset"
            (
            "TimeGroupSetId" INT NOT NULL,
            "TimeGroupSetName" VARCHAR(128) NOT NULL,
            NOT CLUSTER PRIMARY KEY("TimeGroupSetId"));

            CREATE TABLE "tbl_timegroupspan"
            (
            "TimeSpanId" INT NOT NULL,
            "TimeGroupId" INT NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Week" SMALLINT,
            "TimeSpanChar" VARCHAR(255) NOT NULL,
            "LastUpdateDate" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("TimeSpanId"));

            CREATE TABLE "tbl_userrole"
            (
            "RoleId" INT NOT NULL,
            "RoleName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("RoleId"));

            CREATE TABLE "tbl_userrolemap"
            (
            "UserId" INT NOT NULL,
            "RoleId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("RoleId", "UserId"));

            CREATE TABLE "tbl_userroleright"
            (
            "RoleId" INT NOT NULL,
            "OperationId" INT NOT NULL,
            "OperationType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("OperationId", "OperationType", "RoleId"));

            CREATE TABLE "tbl_workstation"
            (
            "WorkStationId" INT NOT NULL,
            "WorkStationName" VARCHAR(255) NOT NULL,
            "WorkStationType" INT NOT NULL,
            "IPAddress" VARCHAR(64) NOT NULL,
            "ParentId" INT DEFAULT 0 NOT NULL,
            "ConnectState" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "IsUsed" BIT DEFAULT 1 NOT NULL,
            "CPU" DOUBLE,
            "Memory" DOUBLE,
            "ThreadCount" INT,
            "DiskFreeSpace" DOUBLE,
            "DBFreeSpace" DOUBLE,
            "LastCommTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("WorkStationId"));

            CREATE TABLE "tbl_workstationex"
            (
            "WorkstationExId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "WorkStationId" INT,
            "Content" VARCHAR(500));

            CREATE TABLE "tbl_workstationmap"
            (
            "WorkstationMapId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "SiteWebWorkStationId" INT,
            "KoloWorkStationId" INT,
            "SiteWebWorkStationName" VARCHAR(128),
            "KoloWorkStationName" VARCHAR(128));

            CREATE TABLE "tbl_writebackentry" (
            "EntryId" INT NOT NULL,
            "EntryCategory" INT,
            "EntryName" varchar(128),
            "EntryTitle" varchar(128),
            "EntryAlias" varchar(255),
            "Enable" BIT DEFAULT 1 NOT NULL,
            "Description" varchar(255),
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "tsl_acrossmonitorunitsignal"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "Expression" CLOB,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "SignalId", "StationId"));

            CREATE TABLE "tsl_activeevent"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "SequenceId" VARCHAR(128) NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "ResetSequenceId" VARCHAR(128),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "ReversalNum" INT,
            "Meanings" VARCHAR(255),
            "BaseTypeId" DECIMAL(12,0),
            CONSTRAINT "TSL_ActiveEvent_ID1" PRIMARY KEY("SequenceId"));

            CREATE OR REPLACE  INDEX "IDX_TSL_ActiveEvent_3" ON "tsl_activeevent"("StationId" ASC,"EquipmentId" ASC,"EventId" ASC,"EventConditionId" ASC);
            CREATE OR REPLACE  INDEX "TSL_ActiveEvent_ID2" ON "tsl_activeevent"("ResetSequenceId" ASC);

            CREATE TABLE "tsl_channelmap"
            (
            "SamplerUnitId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "OriginalChannelNo" INT NOT NULL,
            "StandardChannelNo" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SamplerUnitId"));

            CREATE TABLE "tsl_dataservercapacity"
            (
            "DataServerId" INT NOT NULL,
            "Capacity" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("DataServerId"));

            CREATE TABLE "tsl_deviceportmap"
            (
            "SerialDeviceId" INT NOT NULL,
            "SerialPortId" INT NOT NULL,
            "PortId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SerialDeviceId", "SerialPortId", "PortId"));

            CREATE TABLE "tsl_ipdevice"
            (
            "DeviceId" INT NOT NULL,
            "DeviceNo" VARCHAR(128) NOT NULL,
            "DeviceName" VARCHAR(128) NOT NULL,
            "ProtocolType" INT NOT NULL,
            "IpAddress" VARCHAR(128) NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceId"));

            CREATE TABLE "tsl_monitorunit"
            (
            "MonitorUnitId" INT NOT NULL PRIMARY KEY,
            "MonitorUnitName" VARCHAR(128) NOT NULL,
            "MonitorUnitCategory" INT NOT NULL,
            "MonitorUnitCode" VARCHAR(128) NOT NULL,
            "WorkStationId" INT,
            "StationId" INT,
            "IpAddress" VARCHAR(128),
            "RunMode" INT,
            "ConfigFileCode" VARCHAR(32),
            "ConfigUpdateTime" TIMESTAMP(0),
            "SampleConfigCode" VARCHAR(32),
            "SoftwareVersion" VARCHAR(64),
            "Description" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "HeartbeatTime" TIMESTAMP(0),
            "ConnectState" INT DEFAULT 2 NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "IsSync" TINYINT DEFAULT 1 NOT NULL,
            "SyncTime" TIMESTAMP(0),
            "IsConfigOK" TINYINT DEFAULT 1 NOT NULL,
            "ConfigFileCode_Old" VARCHAR(32),
            "SampleConfigCode_Old" VARCHAR(32),
            "AppCongfigId" INT,
            "CanDistribute" TINYINT NOT NULL,
            "Enable" TINYINT NOT NULL,
            "ProjectName" VARCHAR(255),
            "ContractNo" VARCHAR(255),
            "InstallTime" TIMESTAMP(0),
            "FSU" BIT DEFAULT 0);

            CREATE TABLE "tsl_monitorunitconfig"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "AppConfigId" INT NOT NULL,
            "SiteWebTimeOut" INT NOT NULL,
            "RetryTimes" INT NOT NULL,
            "HeartBeat" INT NOT NULL,
            "EquipmentTimeOut" INT NOT NULL,
            "PortInterruptCount" INT NOT NULL,
            "PortInitializeInternal" INT NOT NULL,
            "MaxPortInitializeTimes" INT NOT NULL,
            "PortQueryTimeOut" INT NOT NULL,
            "DataSaveTimes" INT NOT NULL,
            "HistorySignalSavedTimes" INT NOT NULL,
            "HistoryBatterySavedTimes" INT NOT NULL,
            "HistoryEventSavedTimes" INT NOT NULL,
            "CardRecordSavedCount" INT NOT NULL,
            "ControlLog" TINYINT NOT NULL,
            "IpAddressDS" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tsl_monitorunitcontrol"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "TargetControl" INT NOT NULL,
            "LogicExpression" TEXT,
            NOT CLUSTER PRIMARY KEY("ControlId", "EquipmentId", "StationId"));

            CREATE TABLE "tsl_monitorunitevent"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "StartExpression" VARCHAR(255),
            "SuppressExpression" TEXT,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "EventId", "StationId"));

            CREATE TABLE "tsl_monitorunitextend"
            (
            "MonitorUnitId" INT NOT NULL,
            "Description" VARCHAR(255),
            "ExtendFiled1" VARCHAR(255),
            "ExtendFiled2" VARCHAR(255),
            "ExtendFiled3" VARCHAR(255),
            "ExtendFiled4" VARCHAR(255),
            "ExtendFiled5" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MonitorUnitId"));

            CREATE TABLE "tsl_monitorunitipmap"
            (
            "MonitorUnitId" INT NOT NULL PRIMARY KEY,
            "NewIpAddress" VARCHAR(128),
            "OldIpAddress" VARCHAR(128),
            "RecordTime" TIMESTAMP(0),
            "IsSync" BIT DEFAULT 0 NOT NULL,
            "IsConflict" BIT DEFAULT 0 NOT NULL,
            "Description" VARCHAR(255));

            CREATE TABLE "tsl_monitorunitsignal"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "ReferenceSamplerUnitId" INT,
            "ReferenceChannelNo" INT,
            "Expression" TEXT,
            "InstanceType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "SignalId", "StationId"));

            CREATE TABLE "tsl_port"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "PortId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "PortNo" INT NOT NULL,
            "PortName" VARCHAR(128) NOT NULL,
            "PortType" INT NOT NULL,
            "Setting" VARCHAR(255) NOT NULL,
            "PhoneNumber" VARCHAR(128),
            "LinkSamplerUnitId" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_Port_MonitorUnitId" ON "tsl_port"("MonitorUnitId" ASC,"PortId" ASC);

            CREATE TABLE "tsl_realtimerouting"
            (
            "DataServerId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DataServerId", "MonitorUnitId"),
            CONSTRAINT "IDX_MonitorUnitId_Routing" UNIQUE("MonitorUnitId"));

            CREATE TABLE "tsl_routedistribution"
            (
            "DataServerId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("DataServerId", "MonitorUnitId"));

            CREATE TABLE "tsl_sampler"
            (
            "SamplerId" INT AUTO_INCREMENT NOT NULL,
            "SamplerName" VARCHAR(128) NOT NULL,
            "SamplerType" SMALLINT NOT NULL,
            "ProtocolCode" VARCHAR(32) NOT NULL,
            "DLLCode" VARCHAR(255) NOT NULL,
            "DLLVersion" VARCHAR(32) NOT NULL,
            "ProtocolFilePath" VARCHAR(255) NOT NULL,
            "DLLFilePath" VARCHAR(255) NOT NULL,
            "DllPath" VARCHAR(255) NOT NULL,
            "Setting" VARCHAR(255),
            "Description" VARCHAR(255),
            "SoCode" VARCHAR(255) NOT NULL,
            "SoPath" VARCHAR(255) NOT NULL,
            "UploadProtocolFile" SMALLINT DEFAULT 0,
            NOT CLUSTER PRIMARY KEY("SamplerId"),
            CONSTRAINT "uniqueProtocolcode" UNIQUE("ProtocolCode"));

            CREATE TABLE "tsl_samplerunit"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "SamplerUnitId" INT NOT NULL,
            "PortId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "SamplerId" INT NOT NULL,
            "ParentSamplerUnitId" INT NOT NULL,
            "SamplerType" INT NOT NULL,
            "SamplerUnitName" VARCHAR(128) NOT NULL,
            "Address" INT NOT NULL,
            "SpUnitInterval" DOUBLE,
            "DllPath" VARCHAR(128),
            "ConnectState" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "PhoneNumber" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_SamplerUnit_MonitorUnitId" ON "tsl_samplerunit"("MonitorUnitId" ASC,"PortId" ASC,"SamplerUnitId" ASC);
            CREATE OR REPLACE  INDEX "IDX_SamplerUnit_1" ON "tsl_samplerunit"("MonitorUnitId" ASC,"SamplerUnitId" ASC);
            CREATE OR REPLACE  INDEX "IDX_SamplerUnit_SamplerUnitId" ON "tsl_samplerunit"("SamplerUnitId" ASC);

            CREATE TABLE "tsl_subscribesignal"
            (
            "StationId" INT NOT NULL,
            "HostId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "SubscribeType" INT NOT NULL,
            "LastSampleDateTime" TIMESTAMP(0),
            "LastUpdateDateTime" TIMESTAMP(0),
            "SubscribeDateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "HostId", "SignalId", "StationId"));

            CREATE OR REPLACE  INDEX "IDX_SubscribeSignal_HostId" ON "tsl_subscribesignal"("HostId" ASC,"LastUpdateDateTime" ASC);
        </sql>
    </changeSet>
</databaseChangeLog>