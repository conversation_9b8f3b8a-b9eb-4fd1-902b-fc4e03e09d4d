CREATE TABLE `accountalias` (
  `AccountAliasId` int NOT NULL AUTO_INCREMENT,
  `UserId` int DEFAULT NULL COMMENT '用户ID',
  `Alias` varchar(128) DEFAULT NULL COMMENT '别名',
  `Checked` tinyint(1) DEFAULT NULL COMMENT '是否默认',
  PRIMARY KEY (`AccountAliasId`),
  UNIQUE KEY `AccountAliasId` (`AccountAliasId`)
);

CREATE TABLE `accountpassworderrrecord` (
  `UserId` int NOT NULL COMMENT '用户ID',
  `PasswordErrCnt` int DEFAULT NULL COMMENT '密码错误累计次数',
  `FreezeTime` datetime DEFAULT NULL COMMENT '账号冻结目标时间',
  PRIMARY KEY (`UserId`),
  UNIQUE KEY `UserId` (`UserId`)
);

CREATE TABLE `accountterminaldevicemap` (
  `UserId` int NOT NULL COMMENT '账号id',
  `TerminalDeviceId` varchar(128) DEFAULT NULL COMMENT '终端设备id',
  `UpdateTime` datetime DEFAULT NULL COMMENT '操作时间',
  `OperatorId` int DEFAULT NULL COMMENT '操作人ID',
  PRIMARY KEY (`UserId`),
  UNIQUE KEY `UserId` (`UserId`)
);

CREATE TABLE `accounttimespan` (
  `AccountTimeSpanId` int NOT NULL AUTO_INCREMENT,
  `UserId` int DEFAULT NULL COMMENT '用户ID',
  `WeekSpanChar` varchar(128) DEFAULT NULL COMMENT '星期集合，逗号分隔',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`AccountTimeSpanId`),
  UNIQUE KEY `AccountTimeSpanId` (`AccountTimeSpanId`)
);

CREATE TABLE `auditreport` (
  `AuditReportId` int NOT NULL AUTO_INCREMENT,
  `OperationAccount` varchar(128) DEFAULT NULL COMMENT '操作账户',
  `Level` int DEFAULT NULL COMMENT '级别',
  `Type` varchar(20) DEFAULT NULL COMMENT '事件类型',
  `ClientIP` varchar(50) DEFAULT NULL COMMENT '客户端ip',
  `Details` text COMMENT '详情',
  `Result` varchar(20) DEFAULT NULL COMMENT '事件结果',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`AuditReportId`),
  UNIQUE KEY `AuditReportId` (`AuditReportId`)
);

CREATE TABLE `departmentcodemap` (
  `DepartmentId` int NOT NULL COMMENT '主键id',
  `ParentDepartmentId` int DEFAULT NULL COMMENT '父部门id',
  `Code` varchar(256) DEFAULT NULL COMMENT '第三方部门唯一标识',
  `ParentCode` varchar(256) DEFAULT NULL COMMENT '第三方父部门唯一标识',
  PRIMARY KEY (`DepartmentId`),
  UNIQUE KEY `DepartmentId` (`DepartmentId`)
);

CREATE TABLE `diskfile` (
  `FileId` bigint NOT NULL AUTO_INCREMENT,
  `FilePath` varchar(128) NOT NULL,
  `FileName` varchar(128) NOT NULL,
  `Status` int DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`FileId`),
  UNIQUE KEY `FileId` (`FileId`)
);

CREATE TABLE `gocronexpression` (
  `ExpressionId` int NOT NULL AUTO_INCREMENT,
  `Expression` varchar(128) NOT NULL,
  `SimpleExpression` varchar(128) NOT NULL,
  `ExpressionDescription` varchar(128) DEFAULT NULL,
  `ExpressionDescriptionEn` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ExpressionId`),
  UNIQUE KEY `ExpressionId` (`ExpressionId`)
);

CREATE TABLE `historypassword` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `LogonId` varchar(128) DEFAULT NULL COMMENT '登录名',
  `Password` varchar(128) DEFAULT NULL COMMENT '密码',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更改时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `ipfilterpolicy` (
  `IpFilterPolicyId` int NOT NULL AUTO_INCREMENT,
  `IpAddrSet` varchar(255) DEFAULT NULL COMMENT 'Ip地址设置（具体IP:******* IP段：*******-*********）',
  `WeekSpanChar` varchar(128) DEFAULT NULL COMMENT '星期集合，逗号分隔',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`IpFilterPolicyId`),
  UNIQUE KEY `IpFilterPolicyId` (`IpFilterPolicyId`)
);

CREATE TABLE `license` (
  `LicenseId` int NOT NULL AUTO_INCREMENT,
  `Product` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `UniqueInfo` varchar(1000) DEFAULT NULL COMMENT '系统唯一uuid',
  `LicenseType` int DEFAULT NULL COMMENT '授权码类型 1试用授权码 2正式授权码',
  `ActiveTime` datetime DEFAULT NULL COMMENT '激活时间',
  `LimitTime` datetime DEFAULT NULL COMMENT '到期时间',
  PRIMARY KEY (`LicenseId`),
  UNIQUE KEY `LicenseId` (`LicenseId`)
);

CREATE TABLE `licensefeature` (
  `FeatureId` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) DEFAULT NULL COMMENT '模块名称',
  `IsActive` tinyint DEFAULT NULL COMMENT '是否激活 0否 1是',
  `Data` varchar(255) DEFAULT NULL COMMENT '附加扩展信息',
  PRIMARY KEY (`FeatureId`),
  UNIQUE KEY `FeatureId` (`FeatureId`)
);

CREATE TABLE `loginlog` (
  `LoginLogId` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL,
  `OperatingTime` datetime NOT NULL,
  `OperatingType` varchar(64) NOT NULL,
  `ClientType` varchar(32) NOT NULL,
  `ClientIp` varchar(50) NOT NULL,
  PRIMARY KEY (`LoginLogId`),
  UNIQUE KEY `LoginLogId` (`LoginLogId`)
);

CREATE TABLE `menuitem` (
  `MenuItemId` int NOT NULL AUTO_INCREMENT,
  `ParentId` int DEFAULT NULL COMMENT '父菜单ID',
  `Path` varchar(255) DEFAULT NULL COMMENT '菜单路径',
  `Title` varchar(255) DEFAULT NULL COMMENT '菜单名',
  `Icon` varchar(255) DEFAULT NULL COMMENT '菜单图标',
  `FeatureId` int DEFAULT NULL COMMENT '所属模块id(对应LicenseFeature表)',
  `Selected` tinyint(1) DEFAULT NULL COMMENT '是否默认选中',
  `Expanded` tinyint(1) DEFAULT NULL COMMENT '是否可展开',
  `PathMatch` varchar(255) DEFAULT NULL COMMENT '路径匹配策略',
  `LayoutPosition` int DEFAULT NULL COMMENT '菜单排序',
  `IsSystemConfig` tinyint(1) DEFAULT NULL COMMENT '是否系统内置',
  `IsExternalWeb` tinyint(1) DEFAULT NULL COMMENT '是否外部URL链接',
  `MenuHasNavigation` tinyint(1) DEFAULT NULL COMMENT '是否可跳转',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  `Alias` varchar(255) DEFAULT NULL COMMENT '别名',
  `IsEmbed` tinyint(1) DEFAULT NULL COMMENT '是否嵌入',
  `SortIndex` int DEFAULT NULL COMMENT '排序索引',
  PRIMARY KEY (`MenuItemId`),
  UNIQUE KEY `MenuItemId` (`MenuItemId`)
);

CREATE TABLE `menuitemstructuremap` (
  `MenuItemStructureMapId` int NOT NULL AUTO_INCREMENT,
  `MenuProfileId` int DEFAULT NULL COMMENT '菜单方案ID',
  `MenuStructureId` int DEFAULT NULL COMMENT '菜单目录ID',
  `MenuItemId` int DEFAULT NULL COMMENT '菜单ID',
  `SortIndex` int DEFAULT NULL COMMENT '排序索引',
  PRIMARY KEY (`MenuItemStructureMapId`),
  UNIQUE KEY `MenuItemStructureMapId` (`MenuItemStructureMapId`)
);

CREATE TABLE `menupermissiongroup` (
  `MenuPermissionGroupId` int NOT NULL AUTO_INCREMENT,
  `MenuPermissionGroupName` varchar(128) DEFAULT NULL COMMENT '菜单权限组名称',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`MenuPermissionGroupId`)
);

CREATE TABLE `menupermissiongroupmap` (
  `MenuPermissionGroupMapId` int NOT NULL AUTO_INCREMENT,
  `MenuPermissionGroupId` int DEFAULT NULL COMMENT '菜单权限组Id',
  `PermissionId` int DEFAULT NULL COMMENT '权限Id',
  PRIMARY KEY (`MenuPermissionGroupMapId`)
);

CREATE TABLE `menuprofile` (
  `MenuProfileId` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) DEFAULT NULL COMMENT '方案名称',
  `Checked` tinyint(1) DEFAULT NULL COMMENT '是否被选中',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`MenuProfileId`),
  UNIQUE KEY `MenuProfileId` (`MenuProfileId`)
);

CREATE TABLE `menustructure` (
  `MenuStructureId` int NOT NULL AUTO_INCREMENT,
  `MenuProfileId` int DEFAULT NULL COMMENT '菜单方案ID',
  `ParentId` int DEFAULT NULL COMMENT '父菜单目录ID',
  `Title` varchar(255) DEFAULT NULL COMMENT '菜单目录名称',
  `Icon` varchar(255) DEFAULT NULL COMMENT '菜单目录图标',
  `Selected` tinyint(1) DEFAULT NULL COMMENT '是否默认选中',
  `Expanded` tinyint(1) DEFAULT NULL COMMENT '是否可展开',
  `Hidden` tinyint(1) DEFAULT NULL COMMENT '是否隐藏显示',
  `SortIndex` int DEFAULT NULL COMMENT '排序索引',
  `IsSystem` tinyint(1) DEFAULT NULL COMMENT '是否系统内置',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  `Alias` varchar(255) DEFAULT NULL COMMENT '别名',
  PRIMARY KEY (`MenuStructureId`),
  UNIQUE KEY `MenuStructureId` (`MenuStructureId`)
);

CREATE TABLE `mobileclientmap` (
  `LoginUserId` int NOT NULL,
  `Cid` varchar(128) DEFAULT NULL COMMENT '客户端id',
  `MobileInfo` varchar(256) DEFAULT NULL COMMENT '型号',
  PRIMARY KEY (`LoginUserId`),
  UNIQUE KEY `LoginUserId` (`LoginUserId`)
);

CREATE TABLE `mobileconditionalpushconfig` (
  `LoginUserId` int NOT NULL,
  `Enable` tinyint(1) DEFAULT NULL COMMENT '是否启用',
  `Conditional` text COMMENT '推送条件',
  PRIMARY KEY (`LoginUserId`),
  UNIQUE KEY `LoginUserId` (`LoginUserId`)
);

CREATE TABLE `notification` (
  `NotificationId` int NOT NULL AUTO_INCREMENT,
  `Pushed` tinyint(1) DEFAULT NULL COMMENT '推送是否成功',
  `Category` varchar(255) DEFAULT NULL COMMENT '通知分类',
  `Title` varchar(255) DEFAULT NULL COMMENT '通知标题',
  `Content` varchar(255) DEFAULT NULL COMMENT '通知内容',
  `Sender` varchar(255) DEFAULT NULL COMMENT '发送者',
  `Color` varchar(255) DEFAULT NULL COMMENT '颜色',
  `Icon` varchar(255) DEFAULT NULL COMMENT '图标',
  `WebLink` varchar(255) DEFAULT NULL COMMENT 'web跳转链接',
  `AppLink` varchar(255) DEFAULT NULL COMMENT 'app跳转链接',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  `ExternalId` varchar(50) DEFAULT NULL COMMENT '外部消息厂商请求标识',
  `ExtParam` text COMMENT '扩展参数',
  PRIMARY KEY (`NotificationId`),
  UNIQUE KEY `NotificationId` (`NotificationId`)
);

CREATE TABLE `notificationlog` (
  `NotificationLogId` decimal(19,0) NOT NULL,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `StartTime` datetime NOT NULL,
  `EventStatus` int NOT NULL,
  `NotifyResult` int NOT NULL,
  `NotifyReciever` int DEFAULT NULL,
  `NotifyAddress` varchar(255) DEFAULT NULL,
  `NotifyCategory` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SMSSentTime` datetime DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`NotificationLogId`),
  KEY `NotificationLog_IDX` (`StartTime`)
);

CREATE TABLE `notificationlogmid` (
  `NotificationLogId` decimal(19,0) NOT NULL PRIMARY KEY,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `StartTime` datetime NOT NULL,
  `EventStatus` int NOT NULL,
  `NotifyResult` int NOT NULL,
  `NotifyReciever` int DEFAULT NULL,
  `NotifyAddress` varchar(255) DEFAULT NULL,
  `NotifyCategory` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `SMSSentTime` datetime DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL
);

CREATE TABLE `notificationreceiver` (
  `NotificationReceiverId` int NOT NULL AUTO_INCREMENT,
  `NotificationId` int DEFAULT NULL COMMENT '消息通知id',
  `LoginUserId` int NOT NULL,
  `Readed` tinyint(1) DEFAULT NULL COMMENT '是否已读',
  `ReadedTime` datetime DEFAULT NULL COMMENT '已读时间',
  `Deleted` tinyint(1) DEFAULT NULL COMMENT '是否删除',
  `DeletedTime` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`NotificationReceiverId`),
  UNIQUE KEY `NotificationReceiverId` (`NotificationReceiverId`)
);

CREATE TABLE `notifymode` (
  `NotifyModeId` int NOT NULL,
  `NotifyModeName` varchar(128) NOT NULL,
  `NotifyModeFormat` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdateDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`NotifyModeId`)
);

CREATE TABLE `notifyreceiver` (
  `NotifyReceiverId` int NOT NULL,
  `NotifyReceiverCategory` int NOT NULL,
  `NotifyReceiverName` varchar(128) NOT NULL,
  `NotifyAddress` varchar(255) NOT NULL,
  `NotifyContent` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdateDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`NotifyReceiverId`,`NotifyReceiverCategory`),
  KEY `NotifyReceiverCategory` (`NotifyReceiverCategory`)
);

CREATE TABLE `notifyreceivermap` (
  `EventFilterId` int NOT NULL,
  `EventFilterConditionId` int NOT NULL,
  `NotifyReceiverId` int NOT NULL,
  `NotifyReceiverCategory` int NOT NULL,
  `NotifyServerId` int NOT NULL,
  `NotifyServerCategory` int NOT NULL,
  PRIMARY KEY (`EventFilterId`,`EventFilterConditionId`,`NotifyReceiverId`,`NotifyReceiverCategory`,`NotifyServerId`,`NotifyServerCategory`)
);

CREATE TABLE `notifyserver` (
  `NotifyServerId` int NOT NULL,
  `NotifyServerCategory` int NOT NULL,
  `NotifyServerName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`NotifyServerId`,`NotifyServerCategory`),
  KEY `NotifyServerCategory` (`NotifyServerCategory`)
);

CREATE TABLE `permission` (
  `PermissionId` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(128) DEFAULT NULL COMMENT '权限名称',
  `Category` int DEFAULT NULL COMMENT '权限分类',
  `Caption` varchar(255) DEFAULT NULL COMMENT '标题',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`PermissionId`),
  UNIQUE KEY `PermissionId` (`PermissionId`)
);

CREATE TABLE `permissioncategory` (
  `PermissionCategoryId` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(128) DEFAULT NULL COMMENT '分类名称',
  `Caption` varchar(255) DEFAULT NULL COMMENT '标题',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`PermissionCategoryId`),
  UNIQUE KEY `PermissionCategoryId` (`PermissionCategoryId`)
);

CREATE TABLE `region` (
  `RegionId` int NOT NULL AUTO_INCREMENT,
  `RegionName` varchar(128) DEFAULT NULL COMMENT '区域名称',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`RegionId`)
);

CREATE TABLE `regionmap` (
  `RegionMapId` int NOT NULL AUTO_INCREMENT,
  `ResourceStructureId` int NOT NULL COMMENT '资源组ID',
  `EquipmentId` int NOT NULL COMMENT '设备ID',
  `RegionId` int NOT NULL COMMENT '区域ID',
  PRIMARY KEY (`RegionMapId`,`ResourceStructureId`,`EquipmentId`,`RegionId`),
  UNIQUE KEY `RegionMapId` (`RegionMapId`)
);

CREATE TABLE `resourcestructure` (
  `ResourceStructureId` int NOT NULL AUTO_INCREMENT,
  `SceneId` int DEFAULT NULL COMMENT '场景ID',
  `StructureTypeId` int DEFAULT NULL COMMENT '资源组类型',
  `ResourceStructureName` varchar(128) DEFAULT NULL COMMENT '分组名',
  `ParentResourceStructureId` int DEFAULT NULL COMMENT '父分组Id',
  `Photo` varchar(256) DEFAULT NULL COMMENT '图片',
  `Position` varchar(256) DEFAULT NULL COMMENT '位置信息',
  `LevelOfPath` varchar(128) DEFAULT NULL COMMENT '连接路径',
  `Display` tinyint(1) DEFAULT NULL COMMENT '是否显示',
  `SortValue` int DEFAULT NULL COMMENT '排序Index',
  `ExtendedField` json DEFAULT NULL COMMENT '扩展信息',
  `OriginId` int DEFAULT NULL COMMENT '源对象ID',
  `OriginParentId` int DEFAULT NULL COMMENT '源父对象ID',
  PRIMARY KEY (`ResourceStructureId`),
  UNIQUE KEY `ResourceStructureId` (`ResourceStructureId`),
  KEY `IDX_ResourceStructure_LevelOfPath` (`LevelOfPath`),
  KEY `IDX_ResourceStructure_Type` (`StructureTypeId`),
  KEY `IDX_ResourceStructure_1` (`StructureTypeId`,`OriginParentId`,`OriginId`)
);

CREATE TABLE `resourcestructuremask` (
  `ResourceStructureId` int NOT NULL COMMENT '资源组ID',
  `TimeGroupId` int DEFAULT NULL COMMENT '时间组ID',
  `Reason` varchar(255) DEFAULT NULL COMMENT '屏蔽原因',
  `StartTime` datetime DEFAULT NULL COMMENT '屏蔽开始时间',
  `EndTime` datetime DEFAULT NULL COMMENT '屏蔽结束时间',
  `UserId` int DEFAULT NULL COMMENT '屏蔽人',
  PRIMARY KEY (`ResourceStructureId`),
  UNIQUE KEY `ResourceStructureId` (`ResourceStructureId`)
);

CREATE TABLE `resourcestructuretype` (
  `ResourceStructureTypeId` int NOT NULL COMMENT '分类Id',
  `SceneId` int DEFAULT NULL COMMENT '场景ID',
  `ResourceStructureTypeName` varchar(128) DEFAULT NULL COMMENT '分类名',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`ResourceStructureTypeId`),
  UNIQUE KEY `ResourceStructureTypeId` (`ResourceStructureTypeId`)
);

CREATE TABLE `rolegraphicpagemap` (
  `RoleId` int NOT NULL COMMENT '角色id',
  `GraphicPageId` int DEFAULT NULL COMMENT '组态id',
  `Config` text COMMENT '配置',
  PRIMARY KEY (`RoleId`),
  UNIQUE KEY `RoleId` (`RoleId`)
);

CREATE TABLE `rolepermissionmap` (
  `RolePermissionMapId` int NOT NULL AUTO_INCREMENT,
  `RoleId` int DEFAULT NULL COMMENT '角色ID',
  `PermissionCategoryId` int DEFAULT NULL COMMENT '权限分类ID',
  `PermissionId` int DEFAULT NULL COMMENT '权限ID',
  PRIMARY KEY (`RolePermissionMapId`),
  UNIQUE KEY `RolePermissionMapId` (`RolePermissionMapId`),
  KEY `IDX_PermissionCategoryId` (`PermissionCategoryId`),
  KEY `IDX_ROLE_PermissionId` (`RoleId`,`PermissionId`)
);

CREATE TABLE `roomcategory` (
  `RoomCategoryId` int NOT NULL,
  `RoomCategoryName` varchar(128) NOT NULL,
  `Color` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`RoomCategoryId`),
  UNIQUE KEY `RoomCategoryId` (`RoomCategoryId`)
);

CREATE TABLE `scene` (
  `SceneId` int NOT NULL AUTO_INCREMENT,
  `SceneName` varchar(128) DEFAULT NULL COMMENT '场景名称',
  `Checked` tinyint(1) DEFAULT NULL COMMENT '是否已勾选',
  `Remark` varchar(128) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`SceneId`),
  UNIQUE KEY `SceneId` (`SceneId`)
);

CREATE TABLE `scenecompmap` (
  `SceneCompMapId` int NOT NULL AUTO_INCREMENT,
  `type` varchar(128) DEFAULT NULL,
  `SceneId` int DEFAULT NULL,
  `PageCategory` int DEFAULT NULL,
  `CompType` int DEFAULT NULL,
  PRIMARY KEY (`SceneCompMapId`),
  UNIQUE KEY `SceneCompMapId` (`SceneCompMapId`)
);

CREATE TABLE `scenemenuprofilemap` (
  `SceneMenuProfileMapId` int NOT NULL AUTO_INCREMENT,
  `SceneId` int DEFAULT NULL COMMENT '场景Id',
  `MenuProfileId` int DEFAULT NULL COMMENT '菜单方案',
  PRIMARY KEY (`SceneMenuProfileMapId`),
  UNIQUE KEY `SceneMenuProfileMapId` (`SceneMenuProfileMapId`)
);

CREATE TABLE `scenepermissioncategorymap` (
  `ScenePermissionCategoryMapId` int NOT NULL COMMENT '场景权限分类唯一ID',
  `PermissionCategoryId` int DEFAULT NULL COMMENT '权限分类ID',
  `SceneId` int DEFAULT NULL COMMENT '场景ID',
  PRIMARY KEY (`ScenePermissionCategoryMapId`),
  UNIQUE KEY `ScenePermissionCategoryMapId` (`ScenePermissionCategoryMapId`)
);

CREATE TABLE `scenepermissionmap` (
  `ScenePermissionMapId` int NOT NULL AUTO_INCREMENT,
  `PermissionId` int DEFAULT NULL COMMENT '权限点ID',
  `SceneId` int DEFAULT NULL COMMENT '场景ID',
  PRIMARY KEY (`ScenePermissionMapId`),
  UNIQUE KEY `ScenePermissionMapId` (`ScenePermissionMapId`)
);

CREATE TABLE `scenestructure` (
  `SceneStructureId` int NOT NULL AUTO_INCREMENT,
  `SceneId` int NOT NULL,
  `ObjectTypeId` int NOT NULL,
  `DisplayIndex` int DEFAULT NULL,
  PRIMARY KEY (`SceneStructureId`),
  UNIQUE KEY `SceneStructureId` (`SceneStructureId`)
);

CREATE TABLE `securityreport` (
  `SecurityReportId` int NOT NULL AUTO_INCREMENT,
  `OperationAccount` varchar(128) DEFAULT NULL COMMENT '操作账户',
  `Type` int DEFAULT NULL COMMENT '类别',
  `ClientIP` varchar(50) DEFAULT NULL COMMENT '客户端ip',
  `Details` text COMMENT '详情',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`SecurityReportId`),
  UNIQUE KEY `SecurityReportId` (`SecurityReportId`)
);

CREATE TABLE `softwareversion` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ModuleName` varchar(128) NOT NULL,
  `Version` varchar(256) NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `Feature` varchar(256) NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `systemconfig` (
  `SystemConfigId` int NOT NULL AUTO_INCREMENT,
  `SystemConfigKey` varchar(255) DEFAULT NULL COMMENT '键名',
  `SystemConfigValue` varchar(255) DEFAULT NULL COMMENT '对应键值',
  `SystemConfigType` int DEFAULT NULL COMMENT '键值对业务类型',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`SystemConfigId`),
  UNIQUE KEY `SystemConfigId` (`SystemConfigId`),
  UNIQUE KEY `SystemConfigKey` (`SystemConfigKey`)
);

CREATE TABLE `ttsconfig` (
  `TtsConfigId` int NOT NULL AUTO_INCREMENT,
  `TtsConfigKey` varchar(255) DEFAULT NULL COMMENT '键名',
  `TtsConfigValue` varchar(10000) DEFAULT NULL COMMENT '对应键值',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`TtsConfigId`),
  UNIQUE KEY `TtsConfigId` (`TtsConfigId`),
  UNIQUE KEY `TtsConfigKey` (`TtsConfigKey`)
);

CREATE TABLE `userconfig` (
  `UserConfigId` int NOT NULL AUTO_INCREMENT,
  `UserId` int DEFAULT NULL COMMENT '用户ID',
  `ConfigType` int DEFAULT NULL COMMENT '配置类型 1TTS',
  `ConfigKey` varchar(255) DEFAULT NULL COMMENT '用户信息配置键',
  `ConfigValue` varchar(255) DEFAULT NULL COMMENT '用户信息配置值',
  PRIMARY KEY (`UserConfigId`),
  UNIQUE KEY `UserConfigId` (`UserConfigId`),
  KEY `IDX_UserId_UserType` (`UserId`,`ConfigType`)
);

CREATE TABLE `webclientlog` (
  `WebClientLogId` int NOT NULL AUTO_INCREMENT,
  `UserName` varchar(255) DEFAULT NULL COMMENT '用户名称',
  `ClientIp` varchar(255) DEFAULT NULL COMMENT '客户端ip',
  `BusinessModule` varchar(50) DEFAULT NULL COMMENT '模块类型',
  `Content` varchar(1000) DEFAULT NULL COMMENT '内容',
  `Remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`WebClientLogId`),
  UNIQUE KEY `WebClientLogId` (`WebClientLogId`),
  KEY `IDX_CreateTime` (`CreateTime`),
  KEY `IDX_UserName_CreateTime` (`UserName`,`CreateTime`)
);

