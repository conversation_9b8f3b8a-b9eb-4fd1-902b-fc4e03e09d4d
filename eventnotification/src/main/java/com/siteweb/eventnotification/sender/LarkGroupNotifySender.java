package com.siteweb.eventnotification.sender;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.dto.lark.LarkMessageRequestDTO;
import com.siteweb.eventnotification.dto.wecom.WeComMessageResponseDTO;
import com.siteweb.utility.constans.SystemConfigEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description WeComGroupNotifySender
 * @createTime 2023-06-12 16:25:28
 */
@Service
public class LarkGroupNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(LarkGroupNotifySender.class);

    @Autowired
    @Qualifier("proxyRestTemplateSSL")
    RestTemplate proxyRestTemplateSSL;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("飞书通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.LARK_APPLY_API_URL, elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("飞书通知URL未配置");
            return -2;
        }
        LarkMessageRequestDTO larkMessageRequestDTO = new LarkMessageRequestDTO(alarmNotifyExecutorDTO.getAlarmNotifyContent());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<LarkMessageRequestDTO> httpEntity = new HttpEntity<>(larkMessageRequestDTO, httpHeaders);
        String sendType = messageSourceUtil.getMessage("send.type.weComApply");
        String sendResult = "";
        WeComMessageResponseDTO weComweMessageResponseDTO = null;
        try {
            log.info("请求飞书通知消息,url:{},参数:{}", notifyGatewayUrl, larkMessageRequestDTO);
            weComweMessageResponseDTO = proxyRestTemplateSSL.postForObject(notifyGatewayUrl, httpEntity, WeComMessageResponseDTO.class);
            sendResult = messageSourceUtil.getMessage("send.result.success");
        } catch (RestClientException e) {
            sendResult = messageSourceUtil.getMessage("send.result.fail");
            log.error("飞书通知数据发送失败：{} {},{}", weComweMessageResponseDTO, alarmNotifyExecutorDTO.getSequenceId(), ExceptionUtil.stacktraceToString(e));
        }
        log.info("飞书通知响应:{}",weComweMessageResponseDTO);
        super.alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, "", sendResult);
        if (weComweMessageResponseDTO != null && Objects.equals(weComweMessageResponseDTO.getError_code(), 0)) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
