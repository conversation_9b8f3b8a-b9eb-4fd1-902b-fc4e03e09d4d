<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.AlarmChangeMapper">
    <sql id="Base_Column_List">
        SequenceId, SerialNo, OperationType, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName, EventConditionId,
        EventSeverityId, EventSeverity, StartTime, EndTime, CancelTime, CancelUserId, CancelUserName, ConfirmTime,
        ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath, Description, SourceHostId,
        InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId, BaseTypeName,
        EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId,
        EventStateId, CenterId, CenterName, StructureName, MonitorUnitName, StructureId, StationCategoryId, EquipmentVendor, InsertTime
    </sql>
    <insert id="batchInsert">
        INSERT INTO tbl_alarmchange (
        sequenceId, operationType, stationId, stationName, equipmentId, equipmentName,
        eventId, eventName, eventConditionId, eventSeverityId, eventLevel, eventSeverity,
        startTime, endTime, cancelTime, cancelUserId, cancelUserName, confirmTime, confirmerId,
        confirmerName, eventValue, endValue, reversalNum, meanings, eventFilePath, description,
        sourceHostId, instructionId, instructionStatus, standardAlarmNameId, standardAlarmName,
        baseTypeId, baseTypeName, equipmentCategory, equipmentCategoryName, maintainState,
        signalId, relateSequenceId, eventCategoryId, eventStateId, centerId, centerName,
        structureName, monitorUnitName, structureId, stationCategoryId, equipmentVendor,
        insertTime, resourceStructureId, baseEquipmentId)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.sequenceId}, #{item.operationType}, #{item.stationId},
            #{item.stationName}, #{item.equipmentId}, #{item.equipmentName}, #{item.eventId},
            #{item.eventName}, #{item.eventConditionId}, #{item.eventSeverityId}, #{item.eventLevel},
            #{item.eventSeverity}, #{item.startTime}, #{item.endTime}, #{item.cancelTime},
            #{item.cancelUserId}, #{item.cancelUserName}, #{item.confirmTime}, #{item.confirmerId},
            #{item.confirmerName}, #{item.eventValue}, #{item.endValue}, #{item.reversalNum},
            #{item.meanings}, #{item.eventFilePath}, #{item.description}, #{item.sourceHostId},
            #{item.instructionId}, #{item.instructionStatus}, #{item.standardAlarmNameId},
            #{item.standardAlarmName}, #{item.baseTypeId}, #{item.baseTypeName},
            #{item.equipmentCategory}, #{item.equipmentCategoryName}, #{item.maintainState},
            #{item.signalId}, #{item.relateSequenceId}, #{item.eventCategoryId},
            #{item.eventStateId}, #{item.centerId}, #{item.centerName}, #{item.structureName},
            #{item.monitorUnitName}, #{item.structureId}, #{item.stationCategoryId},
            #{item.equipmentVendor}, #{item.insertTime}, #{item.resourceStructureId},
            #{item.baseEquipmentId}
            )
        </foreach>
    </insert>
    <select id="getMaxSerialNo" resultType="java.lang.Long">
        select COALESCE(max(SerialNo),0) FROM tbl_alarmchange
    </select>
    <select id="readAlarmChange" resultType="com.siteweb.monitoring.entity.AlarmChange">
        SELECT
        a.SequenceId, a.SerialNo, a.OperationType, a.StationId, a.StationName, a.EquipmentId, a.EquipmentName, a.EventId, a.EventName, a.EventConditionId,
        a.EventLevel, a.EventSeverity, a.StartTime, a.EndTime, a.CancelTime, a.CancelUserId, a.CancelUserName, a.ConfirmTime,
        a.ConfirmerId, a.ConfirmerName, a.EventValue, a.ReversalNum, a.Meanings, a.EventFilePath, a.Description, a.SourceHostId,
        a.InstructionId, a.InstructionStatus, a.StandardAlarmNameId, a.StandardAlarmName, a.BaseTypeId, a.BaseTypeName,
        a.EquipmentCategory, a.EquipmentCategoryName, a.MaintainState, a.SignalId, a.RelateSequenceId, a.EventCategoryId,
        a.EventStateId, a.CenterId, a.CenterName, a.StructureName, a.MonitorUnitName, a.StructureId, a.StationCategoryId, a.EquipmentVendor,
        a.InsertTime, b.ResourceStructureId, a.BaseEquipmentId, c.BaseEquipmentName, a.EndValue,a.EventSeverityId
        FROM tbl_alarmchange a LEFT JOIN tbl_equipment b ON a.StationId=b.StationId AND a.EquipmentId=b.EquipmentId
        LEFT JOIN TBL_EquipmentBaseType c on a.BaseEquipmentId = c.BaseEquipmentId
        WHERE a.SerialNo &gt; #{startSerialNo}
        ORDER BY a.SerialNo ASC Limit 4000
    </select>
    <select id="getFilterEventLevel" resultType="java.lang.Integer">
        SELECT
        CAST(td.ExtendField4 AS int)
        FROM
        tbl_dataitem td
        WHERE
        td.entryId = 23
        AND td.enable = 1
    </select>
</mapper>