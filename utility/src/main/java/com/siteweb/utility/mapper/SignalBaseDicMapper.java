package com.siteweb.utility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.utility.dto.SignalBaseDicCategoryDTO;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.SignalBaseDic;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface SignalBaseDicMapper extends BaseMapper<SignalBaseDic> {
    List<SignalBaseDic> findBranchSignalByBaseEquipmentId(Integer baseEquipmentId);
    List<SignalBaseDic> findSignalBaseDicByEquipmentId(Integer equipmentId);

    List<SignalBaseDicCategoryDTO> findSignalBaseCategory(List<Long> baseTypeIds);

    List<SignalBaseDicDTO> findByBaseEquipmentIdAndEquipmentIds(@Param("baseEquipmentId") Integer baseEquipmentId, @Param("equipmentIds") Collection<Integer> equipmentIds);

    List<IdValueDTO<Long, String>> findStandardIdNameMap(@Param("standardVer") Integer standardVer, @Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("baseTypeIds") List<Long> baseTypeIds);
}

