package com.siteweb.hmi.service.impl;

import com.siteweb.common.net.FtpClient;
import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.PortService;
import com.siteweb.monitoring.service.SamplerUnitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/5/15
 */
@Service
@Slf4j
public class ComtradeFtpServiceImpl implements ComtradeFtpService {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    private PortService portService;

    @Autowired
    private ComtradeFileCleanupService comtradeFileCleanupService;

    /**
     * 根据设备ID获取对应的IP地址
     * <p>
     * 该方法通过设备ID查找关联的采样单元和端口信息，从端口的setting字段中提取IP地址。
     * 查找过程：设备 -> 采样单元 -> 端口 -> 端口配置中的IP地址
     *f
     * @param equipmentId 设备ID
     * @return 设备对应的IP地址，如果任何步骤失败则返回空字符串
     */
    @Override
    public String getIpAddressByEquipmentId(Integer equipmentId) {
        // 获取设备对象
        Equipment equipment = equipmentService.findById(equipmentId);
        if (equipment == null) {
            return "";
        }

        // 获取采样单元对象
        Integer samplerUnitId = equipment.getSamplerUnitId();
        if (samplerUnitId == null) {
            return "";
        }

        SamplerUnit samplerUnit = samplerUnitService.findBySamplerUnitId(samplerUnitId);
        if (samplerUnit == null) {
            return "";
        }

        // 获取端口对象
        Integer portId = samplerUnit.getPortId();
        if (portId == null) {
            return "";
        }

        Port port = portService.findByPortIdAndMonitorUnitId(portId, samplerUnit.getMonitorUnitId());
        if (port == null) {
            return "";
        }

        // setting字段中获取IP地址
        String setting = port.getSetting();
        if (setting == null) {
            return "";
        }
        String ip = setting.replaceAll("(.*?)(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})(.*)", "$2");
        return ip;
    }

    /**
     * 从指定IP地址的FTP服务器下载COMTRADE文件
     * <p>
     * 该方法会连接到指定IP的FTP服务器，下载/comtrade目录下的所有文件到本地指定目录。
     * 本地存储路径格式为：upload-dir/comtrade/{equipmentId}/
     *
     * @param ipAddress   FTP服务器的IP地址
     * @param equipmentId 设备ID，用于创建对应的本地存储目录
     * @return 成功下载的文件数量，如果发生错误则返回0
     */
    @Override
    public int downloadComtradeFiles(String ipAddress, Integer equipmentId) {
        if (ipAddress == null || ipAddress.isEmpty() || equipmentId == null) {
            return 0;
        }

        FtpClient ftpClient = new FtpClient(ipAddress, 21);
        String localPath = "upload-dir/comtrade/" + equipmentId;

        try {
            ftpClient.login();
            ftpClient.changeDirectory("/comtrade");

            File localDir = new File(localPath);
            if (!localDir.exists()) {
                localDir.mkdirs();
            }

            ftpClient.downloadDirectory("/comtrade", localPath, List.of());
            ftpClient.logout();

            // 返回下载的文件数量
            File[] files = localDir.listFiles();
            return files != null ? files.length : 0;
        } catch (IOException e) {
            log.error("下载COMTRADE文件失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 从指定IP地址的FTP服务器下载最新的COMTRADE文件
     * <p>
     * 该方法将只下载最近生成的COMTRADE文件，避免重复下载历史文件。
     * 具体实现待完成。
     *
     * @param ipAddress   FTP服务器的IP地址
     * @param equipmentId 设备ID
     * @return 成功下载的最新文件数量，当前返回0
     */
    @Override
    public int downloadLatestComtradeFiles(String ipAddress, Integer equipmentId) {
        if (ipAddress == null || ipAddress.isEmpty() || equipmentId == null) {
            return 0;
        }

        FtpClient ftpClient = new FtpClient(ipAddress, 21);
        String localPath = "upload-dir/comtrade/" + equipmentId;
        File localDir = new File(localPath);

        if (!localDir.exists()) {
            localDir.mkdirs();
        }

        try {
            // 获取本地文件列表
            Set<String> localFiles = new HashSet<>();
            if (localDir.exists()) {
                File[] files = localDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        localFiles.add(file.getName());
                    }
                }
            } else {
                localDir.mkdirs();
            }

            // 连接FTP服务器并获取文件列表
            ftpClient.login();
            ftpClient.changeDirectory("/comtrade");
            FTPFile[] ftpFiles = ftpClient.getApacheFtpClient().listFiles();

            // 筛选需要下载的文件
            List<String> filesToDownload = new ArrayList<>();
            for (FTPFile ftpFile : ftpFiles) {
                String fileName = ftpFile.getName();
                if (!localFiles.contains(fileName)) {
                    filesToDownload.add(fileName);

                    // 确保成对下载.cfg和.dat文件
                    if (fileName.endsWith(".cfg")) {
                        String datFile = fileName.replace(".cfg", ".dat");
                        if (!localFiles.contains(datFile)) {
                            filesToDownload.add(datFile);
                        }
                    } else if (fileName.endsWith(".dat")) {
                        String cfgFile = fileName.replace(".dat", ".cfg");
                        if (!localFiles.contains(cfgFile)) {
                            filesToDownload.add(cfgFile);
                        }
                    }
                }
            }

            // 下载文件
            int downloadedCount = 0;
            for (String fileName : filesToDownload) {
                try (OutputStream outputStream = new FileOutputStream(new File(localDir, fileName))) {
                    if (ftpClient.getApacheFtpClient().retrieveFile(fileName, outputStream)) {
                        downloadedCount++;
                        log.info("下载最新COMTRADE文件成功: {}", fileName);
                    }
                }
            }

            ftpClient.logout();
            
            // 下载完成后执行文件清理
            if (downloadedCount > 0) {
                try {
                    int cleanedCount = comtradeFileCleanupService.cleanupComtradeFiles(equipmentId);
                    if (cleanedCount > 0) {
                        log.info("设备ID: {} 下载完成后清理了 {} 个旧文件对", equipmentId, cleanedCount);
                    }
                } catch (Exception e) {
                    log.error("设备ID: {} 下载后执行文件清理时出错", equipmentId, e);
                }
            }
            
            return downloadedCount;

        } catch (IOException e) {
            log.error("下载最新COMTRADE文件失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取指定设备的COMTRADE文件列表
     * <p>
     * 该方法将返回指定设备ID对应目录下的所有COMTRADE文件名列表。
     * 具体实现待完成。
     *
     * @param equipmentId 设备ID
     * @return COMTRADE文件名列表，当前返回空列表
     */
    @Override
    public List<String> getComtradeFileList(Integer equipmentId) {
        return List.of();
    }
}
