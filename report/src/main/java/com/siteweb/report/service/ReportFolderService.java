package com.siteweb.report.service;


import com.siteweb.report.dto.ReportFolderTreeDTO;
import com.siteweb.report.entity.ReportFolder;
import com.siteweb.report.entity.ReportFolderMap;

import java.util.List;

public interface ReportFolderService {

    /**
     * 获取报表文件夹树
     */
    List<ReportFolderTreeDTO> findReportFolderTree();

    /**
     * 新增报表文件夹
     */
    void saveReportFolder(ReportFolder reportFolder);

    /**
     * 修改报表文件夹
     */
    void updateReportFolder(ReportFolder reportFolder);

    /**
     * 获取报表文件夹详情
     *
     * @param folderId 报表文件夹ID
     */
    ReportFolder findReportFolder(Integer folderId);

    /**
     * 删除报表文件夹
     */
    void deleteReportFolder(Integer folderId);

    /**
     * 新增修改报表和文件夹的挂载关系
     */
    void saveOrUpdateReportFolderMap(ReportFolderMap reportFolderMap);

    /**
     * 删除报表和文件夹的关系
     */
    void deleteReportFolderMapByReportId(Integer reportId);
}
