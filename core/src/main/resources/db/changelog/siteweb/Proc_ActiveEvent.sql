DROP FUNCTION IF EXISTS `getEventNote`;
CREATE FUNCTION `getEventNote`(oldNote TEXT, newNote TEXT) RETURNS text CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    DECLARE currentNote TEXT;
    -- 新的注释长度大于255，直接返回老注释
    IF CHAR_LENGTH(newNote) > 255 THEN
        RETURN oldNote;
    END IF;
    -- 旧的注释是空，直接返回新注释
    IF oldNote IS NULL OR oldNote = '' THEN
        RETURN newNote;
    END IF;
    -- 新旧注释使用;号拼接
    SET currentNote = CONCAT(oldNote, ';', newNote);
    -- 长度超过了255，将开头以；分割的最老的注释去除
    WHILE CHAR_LENGTH(currentNote) > 255 DO
            SET currentNote = SUBSTRING(currentNote, LOCATE(';', currentNote) + 1);
        END WHILE;

    RETURN currentNote;
END;

DROP PROCEDURE IF EXISTS `S6_ConfirmedEvent`;
CREATE PROCEDURE `S6_ConfirmedEvent`(

    IN  Events     VARCHAR(4000),
    IN  ConfirmerId        int,
    in  Note   VARCHAR(255)
)
BEGIN
    DECLARE   v_EndTime        datetime     ;
    DECLARE   v_SequenceId     VARCHAR(256);
    DECLARE   v_ConfirmTime     datetime   ;
    DECLARE   v_ConfirmerName    VARCHAR(256);
    DECLARE   v_i                 INT ;
    DECLARE   v_RecordCount       INT ;
    DECLARE   v_sql       VARCHAR(4000);
    DECLARE   v_OldConfirmTime dateTime;
    DECLARE   v_Description    VARCHAR(255);

    DROP TEMPORARY TABLE IF EXISTS tt_ConfirmAlarm;
    CREATE TEMPORARY TABLE tt_ConfirmAlarm (
                                               IndexId INT AUTO_INCREMENT UNIQUE,
                                               SequenceId VARCHAR(128) NOT NULL,
                                               StationId INT NOT NULL,
                                               StationName VARCHAR(255)  NULL,
                                               EquipmentId INT  NULL,
                                               EquipmentName VARCHAR(128)  NULL,
                                               EventId INT  NULL,
                                               EventName VARCHAR(128)  NULL,
                                               EventConditionId INT  NULL,
                                               EventSeverityId INT  NULL,
                                               EventSeverity VARCHAR(128) NULL,
                                               EventLevel INT NULL,
                                               StartTime DATETIME  NULL,
                                               EndTime DATETIME NULL,
                                               CancelTime DATETIME NULL,
                                               CancelUserId INT NULL,
                                               CancelUserName VARCHAR(128) NULL,
                                               ConfirmTime DATETIME NULL,
                                               ConfirmerId INT NULL,
                                               ConfirmerName VARCHAR(128) NULL,
                                               EventValue FLOAT NULL,
                                               EndValue DOUBLE NULL,
                                               ReversalNum INT NULL,
                                               Meanings VARCHAR(255) NULL,
                                               EventFilePath VARCHAR(255) NULL,
                                               Description VARCHAR(255) NULL,
                                               SourceHostId INT NULL,
                                               InstructionId VARCHAR(255) NULL,
                                               InstructionStatus INT NULL,
                                               StandardAlarmNameId INT NULL,
                                               StandardAlarmName VARCHAR(128) NULL,
                                               BaseTypeId NUMERIC(10,0) NULL,
                                               BaseTypeName VARCHAR(128) NULL,
                                               EquipmentCategory INT  NULL,
                                               EquipmentCategoryName VARCHAR(128) NULL,
                                               MaintainState INT NOT NULL,
                                               SignalId INT NULL,
                                               RelateSequenceId VARCHAR(128) NULL,
                                               EventCategoryId INT NULL,
                                               EventStateId INT NULL,
                                               CenterId INT NULL,
                                               CenterName VARCHAR(128) NULL,
                                               StructureName VARCHAR(128) NULL,
                                               MonitorUnitName VARCHAR(128) NULL,
                                               StructureId INT NULL,
                                               StationCategoryId INT NULL,
                                               EquipmentVendor  VARCHAR(128) NULL,
                                               resourcestructureId   INT  DEFAULT 0,
                                               BaseEquipmentId       INT DEFAULT 0,
                                               ConvergenceEventId    BIGINT DEFAULT 0
    )  ;

    SET v_sql = CONCAT('INSERT INTO tt_ConfirmAlarm(SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, EndValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId,StationCategoryId,EquipmentVendor,resourcestructureId,BaseEquipmentId,ConvergenceEventId)
	   SELECT SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, EndValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId,StationCategoryId,EquipmentVendor,resourcestructureId,BaseEquipmentId,ConvergenceEventId
	   					     	FROM  TBL_ActiveEvent WHERE SequenceId IN (' , IFNULL(Events,'') , ')');
    SET @sql_Stmt = v_sql;
    PREPARE Execute_Stmt FROM @sql_Stmt;
    EXECUTE Execute_Stmt;
    DEALLOCATE PREPARE Execute_Stmt;

    SET v_ConfirmTime = CURRENT_TIMESTAMP ;
    SELECT UserName INTO v_ConfirmerName FROM TBL_Account WHERE UserId = ConfirmerId ;

    SELECT  MIN(IndexId), MAX(IndexId) INTO v_i,v_RecordCount FROM tt_ConfirmAlarm;
    WHILE v_i <= v_RecordCount DO
            SELECT ConfirmTime,SequenceId,Description INTO v_OldConfirmTime,v_SequenceId,v_Description
            FROM tt_ConfirmAlarm
            WHERE IndexId = v_i;

            SELECT EndTime INTO v_EndTime FROM TBL_ActiveEvent WHERE SequenceId = v_SequenceId;

            IF v_OldConfirmTime IS NULL THEN

                IF v_EndTime IS NULL THEN
                    UPDATE TBL_ActiveEvent T SET T.ConfirmTime = v_ConfirmTime, T.ConfirmerId = ConfirmerId, T.ConfirmerName = v_ConfirmerName,T.Description = getEventNote(T.Description,Note)
                    WHERE SequenceId = v_SequenceId;
                ELSE
                    CALL PNL_Ins_MidHistoryEvent(v_SequenceId, v_EndTime, v_ConfirmTime, ConfirmerId, v_ConfirmerName,@ret);
                END IF;

                INSERT INTO TBL_AlarmChange
                (
                    SequenceId ,
                    OperationType ,
                    StationId ,
                    StationName ,
                    EquipmentId ,
                    EquipmentName ,
                    EventId ,
                    EventName ,
                    EventConditionId ,
                    EventSeverityId ,
                    EventSeverity ,
                    EventLevel,
                    StartTime ,
                    EndTime ,
                    CancelTime ,
                    CancelUserId ,
                    CancelUserName ,
                    ConfirmTime ,
                    ConfirmerId ,
                    ConfirmerName ,
                    EventValue ,
                    EndValue,
                    ReversalNum ,
                    Meanings ,
                    EventFilePath ,
                    Description ,
                    SourceHostId ,
                    InstructionId ,
                    InstructionStatus ,
                    StandardAlarmNameId ,
                    StandardAlarmName ,
                    BaseTypeId ,
                    BaseTypeName ,
                    EquipmentCategory ,
                    EquipmentCategoryName ,
                    MaintainState  ,
                    SignalId ,
                    RelateSequenceId ,
                    EventCategoryId ,
                    EventStateId ,
                    CenterId ,
                    CenterName ,
                    StructureName ,
                    MonitorUnitName,
                    StructureId,
                    StationCategoryId,
                    EquipmentVendor,
                    resourcestructureId,
                    BaseEquipmentId,
                    ConvergenceEventId
                )
                SELECT
                    T.SequenceId,
                    3 AS OperationType,
                    T.StationId,
                    T.StationName,
                    T.EquipmentId,
                    T.EquipmentName,
                    T.EventId,
                    T.EventName,
                    T.EventConditionId,
                    T.EventSeverityId,
                    T.EventSeverity,
                    T.EventLevel,
                    T.StartTime,
                    v_EndTime,
                    T.CancelTime,
                    T.CancelUserId,
                    T.CancelUserName,
                    v_ConfirmTime,
                    ConfirmerId,
                    v_ConfirmerName,
                    T.EventValue,
                    T.EndValue,
                    T.ReversalNum,
                    T.Meanings,
                    T.EventFilePath,
                    getEventNote(v_Description,Note),
                    T.SourceHostId,
                    T.InstructionId,
                    T.InstructionStatus,
                    T.StandardAlarmNameId,
                    T.StandardAlarmName,
                    T.BaseTypeId,
                    T.BaseTypeName,
                    T.EquipmentCategory,
                    T.EquipmentCategoryName,
                    T.MaintainState,
                    T.SignalId,
                    T.RelateSequenceId,
                    T.EventCategoryId,
                    T.EventStateId,
                    T.CenterId,
                    T.CenterName,
                    T.StructureName,
                    T.MonitorUnitName,
                    T.StructureId,
                    T.StationCategoryId,
                    T.EquipmentVendor,
                    T.resourcestructureId,
                    T.BaseEquipmentId,
                    T.ConvergenceEventId
                FROM tt_ConfirmAlarm T
                WHERE T.SequenceId = v_SequenceId;
            END IF;

            SET v_i = v_i + 1;
        END WHILE;
    DROP TEMPORARY TABLE IF EXISTS tt_ConfirmAlarm;
END;

DROP PROCEDURE IF EXISTS `S6_CancelEvent`;
CREATE PROCEDURE `S6_CancelEvent`(IN Events varchar(4000), IN UserId int, IN Note varchar(4000))
BEGIN
    DECLARE     v_SequenceId     VARCHAR(256);
    DECLARE     v_ConfirmTime    datetime;
    DECLARE     v_ConfirmerId    int;
    DECLARE     v_StationId		int;
    DECLARE     v_EquipmentId	int;
    DECLARE     v_EventId	    int;
    DECLARE     v_EventConditionId int;
    DECLARE     v_StartTime		DATETIME;
    DECLARE     v_EndTime		DATETIME;
    DECLARE     v_Overturn		INT;
    DECLARE     v_Meanings		VARCHAR(255);
    DECLARE     v_EventValue	FLOAT;
    DECLARE     v_BaseTypeId	NUMERIC(10,0);
    DECLARE     v_ConfirmerName  VARCHAR(256);
    DECLARE     v_UserName       VARCHAR(256);
    DECLARE     v_i                 INT ;
    DECLARE     v_RecordCount       INT ;
    DECLARE     v_OperationType     INT ;
    DECLARE     v_sql       VARCHAR(4000);
    DECLARE     v_isProcess				INT;

    DECLARE     v_SourceHostId	INT;
    DECLARE     v_EventName		VARCHAR(128);
    DECLARE     v_acEquipmentName VARCHAR(128);
    DECLARE     v_EquipmentCategoryId INT;

    DROP TEMPORARY TABLE IF EXISTS tt_CancelAlarm;
    CREATE TEMPORARY TABLE tt_CancelAlarm (
                                              IndexId INT AUTO_INCREMENT UNIQUE,
                                              SequenceId VARCHAR(128) NOT NULL,
                                              StationId INT NOT NULL,
                                              StationName VARCHAR(255)  NULL,
                                              EquipmentId INT  NULL,
                                              EquipmentName VARCHAR(128)  NULL,
                                              EventId INT  NULL,
                                              EventName VARCHAR(128)  NULL,
                                              EventConditionId INT  NULL,
                                              EventSeverityId INT  NULL,
                                              EventSeverity VARCHAR(128) NULL,
                                              EventLevel INT NULL,
                                              StartTime DATETIME  NULL,
                                              EndTime DATETIME NULL,
                                              CancelTime DATETIME NULL,
                                              CancelUserId INT NULL,
                                              CancelUserName VARCHAR(128) NULL,
                                              ConfirmTime DATETIME NULL,
                                              ConfirmerId INT NULL,
                                              ConfirmerName VARCHAR(128) NULL,
                                              EventValue FLOAT NULL,
                                              ReversalNum INT NULL,
                                              Meanings VARCHAR(255) NULL,
                                              EventFilePath VARCHAR(255) NULL,
                                              Description VARCHAR(255) NULL,
                                              SourceHostId INT NULL,
                                              InstructionId VARCHAR(255) NULL,
                                              InstructionStatus INT NULL,
                                              StandardAlarmNameId INT NULL,
                                              StandardAlarmName VARCHAR(128) NULL,
                                              BaseTypeId NUMERIC(10,0) NULL,
                                              BaseTypeName VARCHAR(128) NULL,
                                              EquipmentCategory INT  NULL,
                                              EquipmentCategoryName VARCHAR(128) NULL,
                                              MaintainState INT NOT NULL,
                                              SignalId INT NULL,
                                              RelateSequenceId VARCHAR(128) NULL,
                                              EventCategoryId INT NULL,
                                              EventStateId INT NULL,
                                              CenterId INT NULL,
                                              CenterName VARCHAR(128) NULL,
                                              StructureName VARCHAR(128) NULL,
                                              MonitorUnitName VARCHAR(128) NULL,
                                              StructureId INT NULL,
                                              StationCategoryId INT NULL,
                                              EquipmentVendor  VARCHAR(128) NULL,
                                              resourcestructureId   INT  DEFAULT 0,
                                              BaseEquipmentId       INT DEFAULT 0,
                                              ConvergenceEventId    BIGINT DEFAULT 0
    );

    SET v_sql = CONCAT('INSERT INTO tt_CancelAlarm(SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId, StationCategoryId, EquipmentVendor, resourcestructureId,BaseEquipmentId,ConvergenceEventId)
	   SELECT SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName,
	   		EventConditionId, EventSeverityId, EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId,
	   			CancelUserName, ConfirmTime, ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath,
	   				Description, SourceHostId, InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId,
	   					BaseTypeName, EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId, EventStateId,
	   					    CenterId, CenterName, StructureName, MonitorUnitName,StructureId, StationCategoryId, EquipmentVendor,resourcestructureId,BaseEquipmentId,ConvergenceEventId
	   					     	FROM  TBL_ActiveEvent WHERE SequenceId IN (' , IFNULL(Events,'') , ') and EndTime is null');

    SET @sql_Stmt = v_sql;
    PREPARE Execute_Stmt FROM @sql_Stmt;
    EXECUTE Execute_Stmt;
    DEALLOCATE PREPARE Execute_Stmt;

    SET v_EndTime = CURRENT_TIMESTAMP;

    SELECT T.UserName into v_UserName FROM TBL_Account T WHERE T.UserId = UserId;

    SELECT MIN(IndexId), MAX(IndexId) INTO v_i , v_RecordCount   FROM tt_CancelAlarm;
    WHILE v_i <= v_RecordCount DO
            SELECT 	T.StationId,
                      T.EquipmentId,
                      T.EventId,
                      T.EventConditionId,
                      T.SequenceId,
                      T.StartTime,
                      T.ReversalNum,
                      T.Meanings,
                      T.EventValue,
                      T.BaseTypeId,
                      T.ConfirmTime ,
                      T.ConfirmerId,
                      T.ConfirmerName,
                      T.SourceHostId,
                      T.EquipmentCategory,
                      T.EventName
            INTO
                v_StationId              ,
                v_EquipmentId            ,
                v_EventId                ,
                v_EventConditionId       ,
                v_SequenceId             ,
                v_StartTime              ,
                v_Overturn               ,
                v_Meanings               ,
                v_EventValue             ,
                v_BaseTypeId             ,
                v_ConfirmTime            ,
                v_ConfirmerId            ,
                v_ConfirmerName          ,
                v_SourceHostId           ,
                v_EquipmentCategoryId    ,
                v_EventName
            FROM tt_CancelAlarm T WHERE T.IndexId = v_i;


            IF (v_EventId = -3 AND v_EndTime IS NOT NULL) THEN
                UPDATE TBL_Equipment T
                SET ConnectState = 1
                WHERE StationId = v_StationId AND EquipmentId = v_EquipmentId;
            END IF;


            IF EXISTS(SELECT 'X' FROM TBL_SysConfig WHERE ConfigKey = 'StandardCategory' AND ConfigValue = '1') THEN
                IF (v_EquipmentCategoryId = 99 AND v_Meanings = '被监控设备通信采集中断') THEN
                    SET v_acEquipmentName = LEFT(v_EventName, char_length(v_EventName) - instr(reverse(v_EventName),'_'));

                    UPDATE TBL_Equipment
                    SET ConnectState = 1
                    WHERE StationId = v_StationId AND EquipmentName = v_acEquipmentName AND MonitorUnitId = v_SourceHostId;
                END	IF;
            END IF;


            SELECT T.IsProcess INTO v_isProcess FROM TBL_SARIsProcess T;


            CALL PNL_SavePreEventResponse(v_StationId,v_EquipmentId,v_EventId,v_EventConditionId,v_SequenceId,v_StartTime,v_EndTime,v_Overturn,v_Meanings,v_EventValue,v_BaseTypeId,@ret);


            IF v_isProcess  > 1	 THEN

                DELETE FROM TBL_SARAlarmQueue WHERE SequenceId = v_SequenceId;

                UPDATE TBL_SARAlarmActiveRecord
                SET EndTime = v_EndTime,Overturn = v_Overturn,Meanings = v_Meanings, EventValue = v_EventValue
                WHERE SequenceId = v_SequenceId;
            END	IF;


            UPDATE TBL_ActiveEvent SET CancelTime = v_EndTime, CancelUserId = UserId, CancelUserName = v_UserName, Description =  getEventNote(Description,Note)
            WHERE SequenceId =  v_SequenceId;

            IF v_ConfirmTime IS NULL THEN
                SET v_ConfirmTime = CURRENT_TIMESTAMP ;
                SET v_ConfirmerName = v_UserName;
                SET v_ConfirmerId = UserId;
            END IF;


            CALL PNL_Ins_MidHistoryEvent(v_SequenceId, v_EndTime, v_ConfirmTime, v_ConfirmerId, v_ConfirmerName,@ret);

            INSERT INTO TBL_AlarmChange
            (  SequenceId ,
               OperationType ,
               StationId ,
               StationName ,
               EquipmentId ,
               EquipmentName ,
               EventId ,
               EventName ,
               EventConditionId ,
               EventSeverityId ,
               EventSeverity ,
               EventLevel,
               StartTime ,
               EndTime ,
               CancelTime ,
               CancelUserId ,
               CancelUserName ,
               ConfirmTime ,
               ConfirmerId ,
               ConfirmerName ,
               EventValue ,
               ReversalNum ,
               Meanings ,
               EventFilePath ,
               Description ,
               SourceHostId ,
               InstructionId ,
               InstructionStatus ,
               StandardAlarmNameId ,
               StandardAlarmName ,
               BaseTypeId ,
               BaseTypeName ,
               EquipmentCategory ,
               EquipmentCategoryName ,
               MaintainState  ,
               SignalId ,
               RelateSequenceId ,
               EventCategoryId ,
               EventStateId ,
               CenterId ,
               CenterName ,
               StructureName ,
               MonitorUnitName,
               StructureId,
               StationCategoryId,
               EquipmentVendor,
               resourcestructureId,
               BaseEquipmentId,
               ConvergenceEventId
            )
            SELECT
                T.SequenceId,
                2 AS OperationType,
                T.StationId,
                T.StationName,
                T.EquipmentId,
                T.EquipmentName,
                T.EventId,
                T.EventName,
                T.EventConditionId,
                T.EventSeverityId,
                T.EventSeverity,
                T.EventLevel,
                T.StartTime,
                v_EndTime,
                v_EndTime,
                UserId,
                v_UserName,
                T.ConfirmTime,
                T.ConfirmerId,
                T.ConfirmerName,
                T.EventValue,
                T.ReversalNum,
                T.Meanings,
                T.EventFilePath,
                getEventNote(T.Description,Note),
                T.SourceHostId,
                T.InstructionId,
                T.InstructionStatus,
                T.StandardAlarmNameId,
                T.StandardAlarmName,
                T.BaseTypeId,
                T.BaseTypeName,
                T.EquipmentCategory,
                T.EquipmentCategoryName,
                T.MaintainState,
                T.SignalId,
                T.RelateSequenceId,
                T.EventCategoryId,
                T.EventStateId,
                T.CenterId,
                T.CenterName,
                T.StructureName,
                T.MonitorUnitName,
                T.StructureId,
                T.StationCategoryId,
                T.EquipmentVendor,
                T.resourcestructureId,
                T.BaseEquipmentId,
                T.ConvergenceEventId
            FROM tt_CancelAlarm T
            WHERE T.SequenceId = v_SequenceId;



            INSERT INTO TBL_AlarmChange
            (
                SequenceId ,
                OperationType ,
                StationId ,
                StationName ,
                EquipmentId ,
                EquipmentName ,
                EventId ,
                EventName ,
                EventConditionId ,
                EventSeverityId ,
                EventSeverity ,
                EventLevel,
                StartTime ,
                EndTime ,
                CancelTime ,
                CancelUserId ,
                CancelUserName ,
                ConfirmTime ,
                ConfirmerId ,
                ConfirmerName ,
                EventValue ,
                ReversalNum ,
                Meanings ,
                EventFilePath ,
                Description ,
                SourceHostId ,
                InstructionId ,
                InstructionStatus ,
                StandardAlarmNameId ,
                StandardAlarmName ,
                BaseTypeId ,
                BaseTypeName ,
                EquipmentCategory ,
                EquipmentCategoryName ,
                MaintainState  ,
                SignalId ,
                RelateSequenceId ,
                EventCategoryId ,
                EventStateId ,
                CenterId ,
                CenterName ,
                StructureName ,
                MonitorUnitName,
                StructureId,
                StationCategoryId,
                EquipmentVendor,
                resourcestructureId,
                BaseEquipmentId,
                ConvergenceEventId
            )
            SELECT
                T.SequenceId,
                3 AS OperationType,
                T.StationId,
                T.StationName,
                T.EquipmentId,
                T.EquipmentName,
                T.EventId,
                T.EventName,
                T.EventConditionId,
                T.EventSeverityId,
                T.EventSeverity,
                T.EventLevel,
                T.StartTime,
                v_EndTime,
                v_EndTime,
                UserId,
                v_UserName,
                v_ConfirmTime,
                v_ConfirmerId,
                v_ConfirmerName,
                T.EventValue,
                T.ReversalNum,
                T.Meanings,
                T.EventFilePath,
                getEventNote(T.Description,Note),
                T.SourceHostId,
                T.InstructionId,
                T.InstructionStatus,
                T.StandardAlarmNameId,
                T.StandardAlarmName,
                T.BaseTypeId,
                T.BaseTypeName,
                T.EquipmentCategory,
                T.EquipmentCategoryName,
                T.MaintainState,
                T.SignalId,
                T.RelateSequenceId,
                T.EventCategoryId,
                T.EventStateId,
                T.CenterId,
                T.CenterName,
                T.StructureName,
                T.MonitorUnitName,
                T.StructureId,
                T.StationCategoryId,
                T.EquipmentVendor,
                T.resourcestructureId,
                T.BaseEquipmentId,
                T.ConvergenceEventId
            FROM tt_CancelAlarm T
            WHERE T.SequenceId = v_SequenceId;

            SET v_i = v_i + 1;
        END WHILE;
    DROP TEMPORARY TABLE IF EXISTS tt_CancelAlarm;
END;