package com.siteweb.as.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.SampleValueFormatUtil;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.entity.SignalMeanings;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.model.EquipmentSignalMap;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.as.dto.ActiveSignalAS;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.service.SignalBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实时信号处理类（配置数据+实时数据）
 */
@Component
public class ActiveSignalASManager {

    @Autowired
    public ConfigSignalManager configSignalManager;

    @Autowired
    public RealTimeSignalManager realTimeSignalManager;
    @Autowired
    SignalBaseDicService signalBaseDicService;
    @Autowired
    public EquipmentManager equipmentManager;
    @Autowired
    SignalSubscribeManager signalSubscribeManager;
    @Autowired
    private ActiveSignalManager activeSignalManager;

    private final String  dataPrefix ="RealTimeSignal:";

    /**
     * 根据设备Id获取信号列表
     * @param equipmentId
     * @return 实时信号列表
     */
    public List<ActiveSignalAS> getActiveSignalsByEquipmentId(Integer equipmentId){
        List<ActiveSignalAS> result = new ArrayList<>();
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return result;
        }
        //获取配置
        List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalByEquipmentId(equipmentId);
        if (CollUtil.isEmpty(configSignalItems)) {
            return result;
        }
        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for(ConfigSignalItem configSignalItem:configSignalItems){
            redisKey.add(dataPrefix+equipmentId+ "." + configSignalItem.getSignalId());
        }
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);

        //转map便于查找
        Map<Integer, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getSignalId, p -> p));

        //拼接实时信号
        List<Long> baseTypeIds = configSignalItems.stream().map(ConfigSignalItem::getBaseTypeId).filter(Objects::nonNull).toList();
        Map<Long, String> baseNameMap = signalBaseDicService.getStandardIdNameMap(equipment.getEquipmentTemplateId(),baseTypeIds);

        for(ConfigSignalItem configSignalItem : configSignalItems){
            RealTimeSignalItem realTimeSignalItem = realTimeSignalItemMap.get(configSignalItem.getSignalId());
            ActiveSignalAS activeSignal =  constructSimpleActiveSignalByConfigAndRealTimeItem2(configSignalItem, realTimeSignalItem, baseNameMap);
            result.add(activeSignal);
        }
        signalSubscribeManager.sendSignalSubscribe(equipmentId);
        return  result;
    }
    /**
     * 根据设备ID，信号ID列表获取实时信号，主要用于数字地图，子系统组态
     * @param signalId 请求ID
     * @return
     */
    public ActiveSignalAS getConfigSignalItemByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {

        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            return null;
        }
        ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalId);
        RealTimeSignalItem realTimeSignalItem = realTimeSignalManager.getRealTimeSignalBySignalId(equipmentId, signalId);

        /* 组成显示的实时信号 */
        if (Objects.isNull(configSignalItem)) {
            return null;
        }
        List<Long> baseTypeIds = configSignalItem.getBaseTypeId() != null ? List.of(configSignalItem.getBaseTypeId()) : null;
        Map<Long, String> baseNameMap = signalBaseDicService.getStandardIdNameMap(equipment.getEquipmentTemplateId(),baseTypeIds);
        ActiveSignalAS activeSignal =  constructSimpleActiveSignalByConfigAndRealTimeItem2(configSignalItem, realTimeSignalItem, baseNameMap);
        signalSubscribeManager.sendSignalSubscribe(equipmentId);
        return activeSignal;
    }

    /**
     * 获取基站下所有重要信号列表
     * @param stationId 基站Id
     * @param signalPropertyId 属性Id
     * @return 实行信号列表
     */
    public List<EquipmentActiveSignal> getStationImportantActiveSignalByProperty(Integer stationId, Integer signalPropertyId){
        List<EquipmentActiveSignal> result = new ArrayList<>();

        List<EquipmentSignalMap> equipmentSignalMaps = new ArrayList<>();

        List<Equipment> equipments = equipmentManager.getEquipmentByStationId(stationId);
        for (Equipment equipment:equipments){

            List<ConfigSignalItem> cs = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndProperty(equipment.getEquipmentTemplateId(), signalPropertyId);
            if(cs.size() > 0) {
                EquipmentSignalMap equipmentSignalMap = new EquipmentSignalMap(equipment.getEquipmentId(), equipment.getEquipmentName(), cs);
                equipmentSignalMaps.add(equipmentSignalMap);
            }
        }
        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for(EquipmentSignalMap equipmentSignalMap:equipmentSignalMaps){
            redisKey.addAll(equipmentSignalMap.getRedisKey());
        }
        if (redisKey.isEmpty()) {
            return result;
        }
        /*从 redis获取实时数据 */
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));

        /* 组成显示的实时信号 */
        for(EquipmentSignalMap equipmentSignalMap : equipmentSignalMaps){
            List<SimpleActiveSignal> activeSignals = new ArrayList<>();
            for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
                RealTimeSignalItem realTimeSignalItem = null;
                if (realTimeSignalItemMap.containsKey(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId())) {
                    realTimeSignalItem = realTimeSignalItemMap.get(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId());
                }
                SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
                activeSignals.add(activeSignal);
            }
            EquipmentActiveSignal equipmentActiveSignal = new EquipmentActiveSignal(equipmentSignalMap.getEquipmentId(), equipmentSignalMap.getEquipmentName(), activeSignals);
            result.add(equipmentActiveSignal);
        }
        subscribeSignal(result);
        return  result;
    }
    private void subscribeSignal(List<EquipmentActiveSignal> result) {
        Map<Integer, List<Integer>> equipmentSignalsMap = result.stream()
                                                                .collect(Collectors.groupingBy(EquipmentActiveSignal::getEquipmentId, Collectors.flatMapping(activeSignalManager -> activeSignalManager.getActiveSignals().stream().map(SimpleActiveSignal::getSignalId), Collectors.toList())));
        signalSubscribeManager.sendSignalSubscribe(equipmentSignalsMap);
    }

    /**
     *  根据配置信息跟实时信息构建实时信号实体
     * @param configSignalItem
     * @param realTimeSignalItem
     * @return 实时信号列表
     */
    private SimpleActiveSignal constructSimpleActiveSignalByConfigAndRealTimeItem(ConfigSignalItem configSignalItem, RealTimeSignalItem realTimeSignalItem){
        SimpleActiveSignal simpleActiveSignal = new SimpleActiveSignal();
        simpleActiveSignal.setSignalId(configSignalItem.getSignalId());
        simpleActiveSignal.setSignalName(configSignalItem.getSignalName());
        simpleActiveSignal.setSignalCategory(configSignalItem.getSignalCategory());
        simpleActiveSignal.setDisplayIndex(configSignalItem.getDisplayIndex());
        simpleActiveSignal.setBaseTypeId(configSignalItem.getBaseTypeId());
        simpleActiveSignal.setUnit(configSignalItem.getUnit());
        if(realTimeSignalItem != null) {
            simpleActiveSignal.setCurrentState(realTimeSignalItem.getEventLevel());
            simpleActiveSignal.setCurrentValue(getCurrentValue(configSignalItem, realTimeSignalItem.getCurrentValue()));
            simpleActiveSignal.setOriginalValue(realTimeSignalItem.getCurrentValue());
            simpleActiveSignal.setSampleTime(realTimeSignalItem.getSampleTime());
        }
        return simpleActiveSignal;
    }
    /**
     *  根据配置信息跟实时信息构建实时信号实体
     * @param configSignalItem
     * @param realTimeSignalItem
     * @return 实时信号列表
     */
    private ActiveSignalAS constructSimpleActiveSignalByConfigAndRealTimeItem2(ConfigSignalItem configSignalItem, RealTimeSignalItem realTimeSignalItem, Map<Long, String> baseNameMap){

        ActiveSignalAS signal = new ActiveSignalAS();
        signal.setSignalId(configSignalItem.getSignalId());
        signal.setSignalName(configSignalItem.getSignalName());
        signal.setSignalCategory(configSignalItem.getSignalCategory());
        signal.setDisplayIndex(configSignalItem.getDisplayIndex());
        if(configSignalItem.getBaseTypeId()!= null) {
            signal.setBaseTypeId(configSignalItem.getBaseTypeId());
            signal.setStandardTypeName(baseNameMap.get(configSignalItem.getBaseTypeId()));
        }
        signal.setUnit(configSignalItem.getUnit());
        signal.setShowPrecision(configSignalItem.getShowPrecision());
        signal.setDisplayIndex(configSignalItem.getDisplayIndex());

        if(realTimeSignalItem != null) {
            signal.setCurrentState(0);
            ActiveSignalAS.currentValues currentValue = new ActiveSignalAS.currentValues();
            signal.setCurrentValue(currentValue);
            signal.setEventLevel(realTimeSignalItem.getEventLevel());
            signal.setEventSeverity(realTimeSignalItem.getEventLevel());
            String originValue = realTimeSignalItem.getCurrentValue();
            String signalMeaning = activeSignalManager.getCurrentValue(configSignalItem, originValue);
            signal.setSignalMeaning(signalMeaning);
            currentValue.setValueType(1);
            currentValue.setStringValue(originValue);

            signal.setSampleTime(realTimeSignalItem.getSampleTime());
        }
        return signal;
    }
    public ActiveSignalAS constructSimpleActiveSignalByConfigAndRealTimeItem3(ConfigSignalItem configSignalItem, HistorySignal realTimeSignalItem){

        ActiveSignalAS signal = new ActiveSignalAS();
        signal.setSignalId(configSignalItem.getSignalId());
        signal.setSignalName(configSignalItem.getSignalName());
        signal.setSignalCategory(configSignalItem.getSignalCategory());
        signal.setDisplayIndex(configSignalItem.getDisplayIndex());
        if(configSignalItem.getBaseTypeId()!= null) {
            signal.setBaseTypeId(configSignalItem.getBaseTypeId());
            SignalBaseDic dic = signalBaseDicService.findById(configSignalItem.getBaseTypeId());
            if(dic != null)
                signal.setStandardTypeName(dic.getBaseTypeName());
        }
        signal.setUnit(configSignalItem.getUnit());
        signal.setShowPrecision(configSignalItem.getShowPrecision());
        signal.setDisplayIndex(configSignalItem.getDisplayIndex());

        if(realTimeSignalItem != null) {
            signal.setCurrentState(0);
            ActiveSignalAS.currentValues currentValue = new ActiveSignalAS.currentValues();
            signal.setCurrentValue(currentValue);
            String originValue = realTimeSignalItem.getPointValue();
            currentValue.setValueType(1);
            currentValue.setStringValue(originValue);

            signal.setSignalMeaning(originValue);
            if(configSignalItem.getSignalCategory()==2){
                Integer intValue = SampleValueFormatUtil.getIntValue(originValue);
                Optional<SignalMeanings> signalMeanings= configSignalItem.getMeaningsList().stream()
                        .filter(meaning -> ObjectUtil.isNotNull(meaning.getStateValue()) && meaning.getStateValue().equals(intValue)).findFirst();
                if (signalMeanings.isPresent()) {
                    signal.setSignalMeaning(signalMeanings.get().getMeanings());
                }
            }
            signal.setSampleTime(realTimeSignalItem.getSampleTime());
        }
        return signal;
    }
    /**
     *  设置信号值
     * @param configSignalItem 配置选项
     * @param originValue 原始值
     * @return 格式化后的值
     */
    public String getCurrentValue(ConfigSignalItem configSignalItem, String originValue){
        if (Objects.isNull(configSignalItem)) {
            return originValue;
        }
        String value = originValue;

        /* 如果是信号量，取含义*/
        if(configSignalItem.getSignalCategory()==2){
            Integer intValue = SampleValueFormatUtil.getIntValue(originValue);
            Optional<SignalMeanings> signalMeanings= configSignalItem.getMeaningsList().stream()
                    .filter(meaning -> ObjectUtil.isNotNull(meaning.getStateValue()) && meaning.getStateValue().equals(intValue)).findFirst();
            if (signalMeanings.isPresent()) {
                //调用get()返回Optional值。
                value = signalMeanings.get().getMeanings();
            }
        }
        else if(configSignalItem.getSignalCategory() ==1){
            if(ObjectUtil.isNull( configSignalItem.getUnit())){
                value = SampleValueFormatUtil.getFormatValue(configSignalItem.getShowPrecision(), value);
            } else {
                value = SampleValueFormatUtil.getFormatValue(configSignalItem.getShowPrecision(), value) + configSignalItem.getUnit();
            }
        }
        return  value;
    }
}
