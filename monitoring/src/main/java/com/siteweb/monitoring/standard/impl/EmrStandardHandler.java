package com.siteweb.monitoring.standard.impl;

import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.mapper.StandardDicSigMapper;
import com.siteweb.monitoring.standard.AbstractStandardHandler;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.entity.EventBaseDic;
import com.siteweb.utility.service.EquipmentBaseTypeService;
import com.siteweb.utility.service.EventBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Component
public class EmrStandardHandler extends AbstractStandardHandler {
    @Autowired
    StandardDicSigMapper sigMapper;
    @Autowired
    EventBaseDicService eventBaseDicService;
    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;

    @Override
    public List<IdValueDTO<Long, String>> handleSignalStandard() {
        return sigMapper.findErmStandardSig();
    }

    @Override
    public List<IdValueDTO<Long, String>> handleEventStandard() {
        List<EventBaseDic> eventBaseDics = eventBaseDicService.findEventBaseDics();
        List<EquipmentBaseType> equipmentTypes = equipmentBaseTypeService.findEquipmentBaseTypes();
        return eventBaseDics.stream()
                            .filter(event -> (event.getBaseTypeId() % 1000) == 1)
                            .sorted(Comparator.comparing(EventBaseDic::getBaseEquipmentId)
                                              .thenComparing(EventBaseDic::getBaseTypeName))
                            .map(event -> createEmrDTO(event, equipmentTypes))
                            .toList();
    }

    @Override
    public List<Signal> getSignalBySignalBaseEntryIds(List<Integer> entryIds) {
        return sigMapper.getErmSignalIdBySignalBaseEntryIds(entryIds);
    }

    private IdValueDTO<Long, String> createEmrDTO(EventBaseDic event, List<EquipmentBaseType> equipmentTypes) {
        EquipmentBaseType baseType = equipmentTypes.stream()
                                                   .filter(type -> Objects.equals(type.getBaseEquipmentId(),
                                                           event.getBaseEquipmentId()))
                                                   .findFirst()
                                                   .orElse(null);

        String value = baseType == null ? event.getBaseTypeName()
                : baseType.getBaseEquipmentName() + "_" + event.getBaseTypeName();
        return new IdValueDTO<>(event.getBaseTypeId() / 1000, value);
    }
}
