package com.siteweb.admin.security;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.admin.enums.AuthenicationMessageEnum;
import com.siteweb.admin.service.*;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.util.CaptchaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * token相关过滤器
 */
@Slf4j
public class GenerateTokenForUserFilter extends AbstractAuthenticationProcessingFilter {

    private final TokenUtil tokenUtil;
    private final SystemConfigService systemConfigService;
    private final CaptchaUtil captchaUtil;

    private final AccountPasswordErrRecordService accountPasswordErrRecordService;


    private final AccountTimeSpanService accountTimeSpanService;

    private final IpFilterPolicyService ipFilterPolicyService;

    private final SecurityFileIntegrityService securityFileIntegrityService;
    private final UserConfigService userConfigService;

    protected GenerateTokenForUserFilter(UserAuthenticationManager authenticationManager, TokenUtil tokenUtil, SystemConfigService systemConfigService, CaptchaUtil captchaUtil, AccountPasswordErrRecordService accountPasswordErrRecordService, AccountTimeSpanService accountTimeSpanService, IpFilterPolicyService ipFilterPolicyService, SecurityFileIntegrityService securityFileIntegrityService, UserConfigService userConfigService) {
        super(new AntPathRequestMatcher("/login", HttpMethod.POST.name()));
        this.userConfigService = userConfigService;
        setAuthenticationManager(authenticationManager);
        this.tokenUtil = tokenUtil;
        this.systemConfigService = systemConfigService;
        this.captchaUtil = captchaUtil;
        this.accountPasswordErrRecordService = accountPasswordErrRecordService;
        this.accountTimeSpanService = accountTimeSpanService;
        this.ipFilterPolicyService = ipFilterPolicyService;
        this.securityFileIntegrityService = securityFileIntegrityService;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {
        try {
            String ipAddr = IpUtil.getIpAddr(request);
            checkLoginIpTimeSpan(ipAddr);
            checkFileIntegrity();
            // 解密后的用户名密码
            String tmpStr = new String(Base64.getDecoder().decode(getRequestId(request)), StandardCharsets.UTF_8);

            if (StringUtils.isNotEmpty(tmpStr)) {
                // 获取APP.json字段
                String systemConfig = systemConfigService.findAppJsonValue("login.imageCode.enable");
                if (systemConfig != null && Boolean.parseBoolean(systemConfig)) {
                    // 登录验证码校验
                    loginVerificationCode(request);
                }
            }
            UsernamePasswordAuthenticationToken authToken = null;

            int index;
            if (StringUtils.isNotEmpty(tmpStr) && (index = tmpStr.indexOf(":")) >= 1) {
                String username = tmpStr.substring(0, index);
                String password = tmpStr.substring(index + 1);
                // 判断用户是否在可访问的时间段内
                checkLoginTimeSpan(username);
                authToken = new UsernamePasswordAuthenticationToken(URLDecoder.decode(username, StandardCharsets.UTF_8), password);
            }
            // this will take to successfulauthentication or faliureauthentication function
            return getAuthenticationManager().authenticate(authToken);
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            throw new AuthenticationServiceException(e.getMessage());
        }
    }

    private String getRequestId(HttpServletRequest request) {
        String parameterValue = request.getParameter("requestId");
        String characterPattern = "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$";
        if (!parameterValue.matches(characterPattern)) {
            return "";
        }
        return parameterValue;
    }

    private String getTextParameter(HttpServletRequest request, String parameterName) {
        String parameterValue = request.getParameter(parameterName);
        String characterPattern = "[0-9a-zA-Z]";
        Pattern p = Pattern.compile(characterPattern);
        Matcher m = p.matcher(parameterValue);
        if (!m.find()) {
            return "";
        }
        return parameterValue;
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain, Authentication authToken) {
        try {
            String loginType = getTextParameter(req, "loginType");
            if ("".equals(loginType)) {
                loginType = "web";
            }
            SecurityContextHolder.getContext().setAuthentication(authToken);
            ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
            ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
            TokenUser tokenUser = (TokenUser) authToken.getPrincipal();
            String newToken = this.tokenUtil.createTokenForUser(tokenUser, loginType);
            String userTheme = userConfigService.findUserTheme(tokenUser.getUserId());
            jsonResp.put("token", newToken);
            jsonResp.put("UserName", tokenUser.getUser().getLogonId());
            jsonResp.put("LogonId", tokenUser.getUser().getLogonId());
            jsonResp.put("role", tokenUser.getRole());
            jsonResp.put("userId", tokenUser.getUserId());
            jsonResp.put("personId", tokenUser.getUserId());
            jsonResp.put("themeName", userTheme);
            jsonResp.put("needResetPwd", tokenUser.getUser().getNeedResetPwd());
            jsonArray.add(jsonResp);
            // 正确授权清除之前的密码错误记录
            accountPasswordErrRecordService.passwordSuccessAction(tokenUser.getUserId());
            res.setHeader(HttpHeaders.CONTENT_TYPE, "text/html;charset=UTF-8");
            res.setCharacterEncoding("utf-8");
            res.setStatus(HttpServletResponse.SC_OK);
            res.getWriter().write(jsonArray.toString());
            res.getWriter().flush();
            res.getWriter().close();
        } catch (Exception e) {
            log.error("登录异常:{}", ExceptionUtil.stacktraceToString(e));
            throw new AuthenticationServiceException(e.getMessage());
        }
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response, AuthenticationException failed) throws IOException {
        ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
        ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
        String message = failed.getMessage();
        jsonResp.put("token", "");
        jsonResp.put("error", message);
        String respCode = "errorcode";
        if (AuthenicationMessageEnum.ACCOUNT_EXPIRED.getMsg().equals(message) ||
                AuthenicationMessageEnum.PASSWORD_EXPIRED.getMsg().equals(message)) {
            jsonResp.put(respCode, 7);
        } else if (AuthenicationMessageEnum.LOCKED.getMsg().equals(message)) {
            jsonResp.put(respCode, 11);
        } else if (AuthenicationMessageEnum.DISABLED.getMsg().equals(message)) {
            jsonResp.put(respCode, 12);
        } else if (AuthenicationMessageEnum.WRONG_PASSWORD.getMsg().equals(message) ||
                AuthenicationMessageEnum.USERNAME_NOT_FOUND.getMsg().equals(message) ||
                AuthenicationMessageEnum.USER_NOT_EXIST.getMsg().equals(message)) {
            jsonResp.put(respCode, 13);
            // 增加密码最大错误次数提示
            SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("max.try.login.count");
            String pwdErrCount = "";
            if (systemConfig != null && StrUtil.isNotEmpty(systemConfig.getSystemConfigValue())) {
                pwdErrCount = systemConfig.getSystemConfigValue();
            }
            jsonResp.put("pwdErrCount", pwdErrCount);
            jsonResp.put("error", AuthenicationMessageEnum.USERNAME_OR_PWD_ERR.getMsg());
        } else if (CharSequenceUtil.contains(message, AuthenicationMessageEnum.FREEZE.getMsg())) {
            jsonResp.put(respCode, 15);
        } else if (CharSequenceUtil.contains(message, AuthenicationMessageEnum.VIOLENT_HACK.getMsg())) {
            jsonResp.put(respCode, 14);
        } else if (CharSequenceUtil.contains(message, AuthenicationMessageEnum.ACCOUNT_TIME_SPAN.getMsg())) {
            jsonResp.put(respCode, 16);
        } else if (CharSequenceUtil.contains(message, AuthenicationMessageEnum.IP_TIME_SPAN.getMsg())) {
            jsonResp.put(respCode, 17);
        } else {
            jsonResp.put(respCode, 3);
        }
        jsonArray.add(jsonResp);
        response.setHeader("content-type", "application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(jsonArray.toString());
        response.getWriter().flush();
        response.getWriter().close();
    }

    /**
     * 登录验证码校验
     */
    private void loginVerificationCode(HttpServletRequest request) {
        Boolean loop = captchaUtil.verifyCaptcha(getTextParameter(request, "code"), getTextParameter(request, "imgKey"));
        if (Boolean.FALSE.equals(loop)) {
            throw new BusinessException("login.captcha.code.error");
        }
    }

    private void checkLoginTimeSpan(String userName) {
        boolean result = accountTimeSpanService.isTimeSpanByUserName(userName);
        if (Boolean.FALSE.equals(result)) {
            throw new BusinessException(AuthenicationMessageEnum.ACCOUNT_TIME_SPAN.getMsg());
        }
    }

    private void checkLoginIpTimeSpan(String ip) {
        boolean result = ipFilterPolicyService.isIpTimeSpan(ip);
        if (Boolean.FALSE.equals(result)) {
            throw new BusinessException(AuthenicationMessageEnum.IP_TIME_SPAN.getMsg());
        }
    }

    /**
     * 安全认证处理：当系统文件自检验不通过时，用户不可以登录系统，返回提示
     */
    private void checkFileIntegrity() {
        boolean result = securityFileIntegrityService.fileIntergrityStatus();
        if (Boolean.FALSE.equals(result)) {
            throw new BusinessException("security file integrity unsuccessfu!");
        }
    }

}
