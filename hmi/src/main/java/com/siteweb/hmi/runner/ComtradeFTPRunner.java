package com.siteweb.hmi.runner;

import com.siteweb.hmi.manager.ComtradeFileManager;
import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.PortService;
import com.siteweb.monitoring.service.SamplerUnitService;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/5/19
 */
@Slf4j
@Configuration
public class ComtradeFTPRunner implements ApplicationRunner {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    private PortService portService;

    @Autowired
    private ComtradeFtpService compleTradeftpservice;

    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ConfigSignalManager configSignalManager;

    @Autowired
    private ComtradeFileManager comtradeFileManager;

    @Override
    @Async
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 主备双机判断，只有主机才执行
            if (!haStatusService.isMasterHost()) {
                log.info("HAStatus is BACKUP，COMTRADE FTP初始化任务退出");
                return;
            }

            // 检查COMTRADE功能是否启用
            try {
                var config = systemConfigService.findBySystemConfigKey("comtrade.enable");
                if (config == null || config.getSystemConfigValue() == null) {
                    log.info("未找到COMTRADE启用状态配置或配置值为空，默认不启用，COMTRADE FTP初始化任务退出");
                    return;
                }

                String comtradeEnable = config.getSystemConfigValue();
                if (comtradeEnable.trim().isEmpty() || !"true".equalsIgnoreCase(comtradeEnable)) {
                    log.info("COMTRADE功能未启用，COMTRADE FTP初始化任务退出");
                    return;
                }
            } catch (Exception e) {
                log.warn("获取COMTRADE启用状态失败，默认不启用，COMTRADE FTP初始化任务退出", e);
                return;
            }

            log.info("ComtradeParser服务初始化开始...");

            // 通过信号基类ID获取设备列表
            List<Long> signalBaseTypeIds = getComtradeSignalBaseTypeIds();
            log.info("获取到COMTRADE信号基类ID: {}", signalBaseTypeIds);

            // 获取匹配的信号配置
            List<ConfigSignalItem> configSignals = configSignalManager.getConfigSignalsByBaseTypeIds(signalBaseTypeIds);
            log.info("获取到匹配的信号配置数量: {}", configSignals.size());

            // 从信号配置中提取设备模板ID
            List<Integer> equipmentTemplateIds = configSignals.stream()
                    .map(ConfigSignalItem::getEquipmentTemplateId)
                    .distinct()
                    .collect(Collectors.toList());
            log.info("获取到设备模板ID集合: {}", equipmentTemplateIds);

            // 通过设备模板ID获取设备列表
            List<EquipmentDTO> equipmentList = equipmentService.findEquipmentsByEquipmentTemplateIds(equipmentTemplateIds);
            log.info("获取到设备列表，数量: {}", equipmentList != null ? equipmentList.size() : 0);
            // 调取 getIpAddressByEquipmentId 方法获取设备对应的IP地址
            Map<Integer, String> equipmentIpMap = new HashMap<>();
            if (equipmentList == null || equipmentList.isEmpty()) {
                log.info("没有找到任何设备，跳过COMTRADE文件下载");
                return;
            }
            for (EquipmentDTO equipment : equipmentList) {
                String ipAddress = getIpAddressByEquipmentId(equipment.getEqId());
                log.info("设备ID: {}, IP地址: {}", equipment.getEqId(), ipAddress);
                equipmentIpMap.put(equipment.getEqId(), ipAddress);
            }
            // 遍历 equipmentIpMap 进行文件下载
            for (Map.Entry<Integer, String> entry : equipmentIpMap.entrySet()) {
                Integer equipmentId = entry.getKey();
                String ipAddress = entry.getValue();
                try {
                    log.info("开始下载设备ID: {}, IP地址: {} 的COMTRADE文件...", equipmentId, ipAddress);
                    int downloadedCount = compleTradeftpservice.downloadLatestComtradeFiles(ipAddress, equipmentId);
                    log.info("下载设备ID: {}, IP地址: {} 的COMTRADE文件完成，下载数量: {}", equipmentId, ipAddress, downloadedCount);
                } catch (Exception ex) {
                    log.error("下载设备ID: {}, IP地址: {} 的COMTRADE文件失败", equipmentId, ipAddress, ex);
                }
            }
            log.info("ComtradeParser服务初始化完成。");

        } catch (Exception e) {
            log.error("COMTRADE FTP初始化任务执行失败", e);
        } finally {
            // 无论初始化成功还是失败，都通知ComtradeFileManager可以开始定时任务
            try {
                comtradeFileManager.setInitializationCompleted();
                log.info("已通知ComtradeFileManager初始化完成，定时任务可以开始运行");
            } catch (Exception e) {
                log.error("通知ComtradeFileManager初始化完成时出错", e);
            }
        }
    }

    /**
     * 根据设备ID获取对应的IP地址
     * <p>
     * 该方法通过设备ID查找关联的采样单元和端口信息，从端口的setting字段中提取IP地址。
     * 查找过程：设备 -> 采样单元 -> 端口 -> 端口配置中的IP地址
     * f
     *
     * @param equipmentId 设备ID
     * @return 设备对应的IP地址，如果任何步骤失败则返回空字符串
     */
    public String getIpAddressByEquipmentId(Integer equipmentId) {
        // 获取设备对象
        Equipment equipment = equipmentService.findById(equipmentId);
        if (equipment == null) {
            return "";
        }

        // 获取采样单元对象
        Integer samplerUnitId = equipment.getSamplerUnitId();
        if (samplerUnitId == null) {
            return "";
        }

        SamplerUnit samplerUnit = samplerUnitService.findBySamplerUnitId(samplerUnitId);
        if (samplerUnit == null) {
            return "";
        }

        // 获取端口对象
        Integer portId = samplerUnit.getPortId();
        if (portId == null) {
            return "";
        }

        Port port = portService.findByPortIdAndMonitorUnitId(portId, samplerUnit.getMonitorUnitId());
        if (port == null) {
            return "";
        }

        // setting字段中获取IP地址
        String setting = port.getSetting();
        if (setting == null) {
            return "";
        }
        return setting.replaceAll("(.*?)(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})(.*)", "$2");
    }

    /**
     * 从系统配置中获取baseTypeIds
     *
     * @return baseTypeIds列表
     */
    private List<Long> getComtradeSignalBaseTypeIds() {
        try {
            var config = systemConfigService.findBySystemConfigKey("comtrade.signal.basetypeids");
            if (config == null || config.getSystemConfigValue() == null) {
                log.warn("未找到COMTRADE信号基类ID配置或配置值为空，使用默认值");
                return Arrays.asList(1L, 2L, 3L);
            }

            String baseTypeIdsConfig = config.getSystemConfigValue();
            if (StringUtils.hasText(baseTypeIdsConfig)) {
                return Arrays.stream(baseTypeIdsConfig.split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("从系统配置获取COMTRADE信号基类ID失败，使用默认值", e);
        }
        // 如果获取失败，返回默认值
        return Arrays.asList(1L, 2L, 3L);
    }

}
