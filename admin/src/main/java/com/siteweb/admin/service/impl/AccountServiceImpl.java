package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.dto.AccountOnlineDTO;
import com.siteweb.admin.entity.*;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.*;
import com.siteweb.admin.sso.cqctcc.CqctccService;
import com.siteweb.admin.vo.AccountEnableVO;
import com.siteweb.admin.vo.AccountVO;
import com.siteweb.admin.vo.ForgetPasswordVO;
import com.siteweb.common.properties.CqctccSSOProperties;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SensitiveInfoUtils;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.util.CaptchaUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountServiceImpl implements AccountService {

    private static final String LOGIN_USER_TOKEN = "LoginUserToken";
    private static final String KEY_PREFIX = "Freeze:";

    @Autowired
    CaptchaUtil captchaUtil;
    @Autowired
    AccountMapper accountMapper;

    @Autowired
    UserRoleMapper userRoleMapper;

    @Autowired
    HistoryPasswordService historyPasswordService;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    DataItemService dataItemService;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    AccountAliasService accountAliasService;

    @Autowired
    EmployeeService employeeService;
    @Autowired
    DepartmentPermissionService departmentPermissionService;
    @Autowired
    TokenUtil tokenUtil;
    @Autowired
    CqctccService cqctccService;
    @Autowired
    CqctccSSOProperties cqctccSSOProperties;

    private static final String USER_HEARTBEAT = "UserHeartbeat:";


    private static final int PASSWORD_EFFECTIVE_DAYS = 90;

    private long userOnlineTTL = 30L;

    @Override
    public List<AccountDTO> findByLogonId(String logonId) {
        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.selectList(new QueryWrapper<Account>().eq("LogonId", logonId));
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            accountDTO.setRoleIds(userRoleMapper.findRolesByUserId(account.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    public List<AccountDTO> findByMobile(String mobile) {
        List<AccountDTO> result = new ArrayList<>();
        List<Account> accountList = accountMapper.findByMobile(mobile);
        for (Account account : accountList) {
            AccountDTO accountDTO = new AccountDTO();
            BeanUtils.copyProperties(account, accountDTO);
            accountDTO.setRoleIds(userRoleMapper.findRolesByUserId(account.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
            result.add(accountDTO);
        }
        return result;
    }

    @Override
    public AccountDTO findByUserId(Integer userId) {
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return null;
        }
        AccountDTO accountDTO = new AccountDTO();
        BeanUtils.copyProperties(account, accountDTO);
        accountDTO.setRoleIds(userRoleMapper.findRolesByUserId(account.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
        AccountAlias accountAlias = accountAliasService.currentAccountAlias(userId);
        accountDTO.setAlias(accountAlias != null ? accountAlias.getAlias() : null);
        return accountDTO;
    }

    @Override
    public List<AccountDTO> findAll() {
        List<Account> accounts = accountMapper.selectList(null);
        if (CollUtil.isEmpty(accounts)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(accounts, AccountDTO.class);
    }

    @Override
    public List<Integer> findAllUserIds() {
        List<Account> accounts = accountMapper.selectList(Wrappers.lambdaQuery(Account.class).select(Account::getUserId));
        return accounts.stream().map(Account::getUserId).toList();
    }

    @Override
    @Transactional
    public int updateAccountPassword(Integer userId, String oldPassword, String newPassword) {
        if (userId == null || StringUtils.isEmpty(oldPassword) || StringUtils.isEmpty(newPassword)) {
            return -1;
        }
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return -2;
        }
        if (!oldPassword.equals(account.getPassword())) {
            return -3;
        }
        int historyPasswordSaveCounts = getHistoryPasswordSaveCounts();
        if (historyPasswordSaveCounts > 0) {
            List<HistoryPassword> historyPasswordList = historyPasswordService.findHistoryPasswordsByLogonId(account.getLogonId());
            for (HistoryPassword historyPassword : historyPasswordList) {
                if (historyPassword.getPassword().equals(newPassword)) {
                    return -4;
                }
            }
        }
        //********修改为前端密码加密
        account.setPassword(newPassword);
        int passwordEffectiveDays = getPasswordEffectiveDays();
        if (account.getPasswordValidTime() != null) {
            Date passwordValidTime = DateUtil.dateAddDays(new Date(), passwordEffectiveDays);
            passwordValidTime = DateUtil.getLastSecondsOfToday(passwordValidTime);
            account.setPasswordValidTime(passwordValidTime);
        }
        account.setNeedResetPwd(false);
        //update history password
        if (historyPasswordSaveCounts > 0) {
            HistoryPassword historyPassword = new HistoryPassword();
            historyPassword.setLogonId(account.getLogonId());
            historyPassword.setPassword(newPassword);
            historyPassword.setUpdateTime(new Date());
            historyPasswordService.insertHistoryPassword(historyPassword);
        }
        int result = accountMapper.updateById(account);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.updatePassword") + "：" + account.getUserName());
        if (result > 0) {
            // 清除对应token
            String loginType = TokenUserUtil.getLoginType();
            redisUtil.hdel(LOGIN_USER_TOKEN, account.getLogonId() + ":" + loginType);
        }
        return result;
    }


    @Override
    @Transactional
    public int forgetPassword(ForgetPasswordVO forgetPasswordVO) {
        if (forgetPasswordVO.getUserName() == null || StringUtils.isEmpty(forgetPasswordVO.getNewPassword())) {
            return -1;
        }
        Account account = accountMapper.selectOne(Wrappers.lambdaQuery(Account.class).eq(Account::getLogonId, forgetPasswordVO.getUserName()));
        if (null == account) {
            return -2;
        }
        //校验图形码
        Boolean loop = captchaUtil.verifyCaptcha(forgetPasswordVO.getImgCode(), forgetPasswordVO.getImgKey());
        if (Boolean.FALSE.equals(loop)) {
            return -6;
        }
        //校验短信验证码
        Object redisValue = redisUtil.get("verificationCode" + forgetPasswordVO.getUserName());
        String smsCodeInRedis = redisValue == null ? "" : redisValue.toString();
        if (!forgetPasswordVO.getSmsCode().equals(smsCodeInRedis)) {
            return -5;
        }

        if (forgetPasswordVO.getNewPassword().equals(account.getPassword())) {
            return -3;
        }
        int historyPasswordSaveCounts = getHistoryPasswordSaveCounts();
        if (historyPasswordSaveCounts > 0) {
            List<HistoryPassword> historyPasswordList = historyPasswordService.findHistoryPasswordsByLogonId(account.getLogonId());
            for (HistoryPassword historyPassword : historyPasswordList) {
                if (historyPassword.getPassword().equals(forgetPasswordVO.getNewPassword())) {
                    return -4;
                }
            }
        }
        //********修改为前端密码加密
        account.setPassword(forgetPasswordVO.getNewPassword());
        int passwordEffectiveDays = getPasswordEffectiveDays();
        if (account.getPasswordValidTime() != null) {
            Date passwordValidTime = DateUtil.dateAddDays(new Date(), passwordEffectiveDays);
            passwordValidTime = DateUtil.getLastSecondsOfToday(passwordValidTime);
            account.setPasswordValidTime(passwordValidTime);
        }
        account.setNeedResetPwd(false);
        //update history password
        if (historyPasswordSaveCounts > 0) {
            HistoryPassword historyPassword = new HistoryPassword();
            historyPassword.setLogonId(account.getLogonId());
            historyPassword.setPassword(forgetPasswordVO.getNewPassword());
            historyPassword.setUpdateTime(new Date());
            historyPasswordService.insertHistoryPassword(historyPassword);
        }
        int result = accountMapper.updateById(account);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.forgetPassword") + "：" + account.getUserName());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetAccountPassword(Integer userId, String password) {
        if (userId == null || StringUtils.isEmpty(password)) {
            return -1;
        }
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return -2;
        }
        //注意：重置密码不记入历史密码中，用户首次登录后必须修改密码
        //********修改为前端密码加密
        account.setPassword(password);
        account.setNeedResetPwd(true);
        int passwordEffectiveDays = this.getPasswordEffectiveDays();
        account.setPasswordValidTime(DateUtil.dateAddDays(new Date(), passwordEffectiveDays));
        int result = accountMapper.updateById(account);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.resetPassword") + "：" + account.getUserName());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setAccountThemeName(Integer userId, String themeName) {
        if (userId == null || StringUtils.isEmpty(themeName)) {
            return -1;
        }
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return -2;
        }
        account.setThemeName(themeName);
        int result = accountMapper.updateById(account);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.setUserTheme") + "：" + account.getUserName());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createAccount(Integer operatorId,AccountVO accountVO) {
        if (accountVO.getUserId() == null) {
            return -1;
        }
        List<Account> accounts = checkRepeatByLogonId(accountVO.getLogonId(), null);
        if (CollUtil.isNotEmpty(accounts)) {
            return -3;
        }
        if (accountVO.getRoleIds() != null) {
            String[] roleIdArray = accountVO.getRoleIds().split(",");
            for (String roleId : roleIdArray) {
                userRoleMapper.createUserRoleMap(accountVO.getUserId(), Integer.parseInt(roleId));
            }
        }
        Employee employee = employeeService.findByEmployeeId(accountVO.getUserId());
        Account account = accountVO.build();
        account.setCenterId(dataItemService.getCenterId());
        account.setUserName(employee.getEmployeeName());
        int result = accountMapper.insert(account);
        AccountDTO accountDTO = Optional.ofNullable(this.findByUserId(operatorId)).orElse(new AccountDTO());
        securityAuditManager.recordAuditReport(accountDTO.getLogonId(),AuditReportTypeEnum.ACCOUNT.getLevel(),AuditReportTypeEnum.ACCOUNT.getDescribe(), localeMessageSourceUtil.getMessage("audit.report.addAccount") + "：" + account.getUserName(),IpUtil.getIpAddr(),"");
        if (Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
           // 重庆电信开关启动，需要创建人主平台的权限
            cqctccService.loginPrivControl(accountVO.getLogonId(), CqctccService.ADD_ACTION);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateAccount(Integer operatorId, List<AccountVO> accountVOList) {
        //userid和logenid非null
        List<UserRoleMap> userRoleMapList = accountVOList.stream().map(AccountVO::buildUserRoleMap).toList();
        userRoleMapper.batchCreateUserRoleMap(userRoleMapList);
        List<Account> accountList = accountVOList.stream().map(AccountVO::build).toList();
        accountVOList.forEach(vo -> vo.setCenterId(dataItemService.getCenterId()));
        int result = accountMapper.batchInsert(accountList);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAccount(AccountVO accountVO) {
        if (accountVO.getUserId() == null) {
            return -1;
        }
        Account oldAccount = accountMapper.selectById(accountVO.getUserId());
        if (null == oldAccount) {
            return -2;
        }
        // 避免修改账号名重复，还需要排除自己本身账号名
        List<Account> accounts = checkRepeatByLogonId(accountVO.getLogonId(), oldAccount.getLogonId());
        if (CollUtil.isNotEmpty(accounts)) {
            return -3;
        }
        AccountDTO oldAccountVO = new AccountDTO();
        BeanUtils.copyProperties(oldAccount, oldAccountVO);
        oldAccountVO.setRoleIds(userRoleMapper.findRolesByUserId(oldAccount.getUserId()).stream().map(UserRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
        Account account = accountVO.build();
        //由于accountVO相关字段有脱敏处理，所以更新时要比较其内容是否有变化
        if (SensitiveInfoUtils.chineseName(oldAccount.getUserName()).equals(accountVO.getUserName())) {
            account.setUserName(oldAccount.getUserName());
        }
        updateAccountRole(accountVO, oldAccountVO);
        //修改用户账号时不允许修改密码
        account.setPassword(oldAccountVO.getPassword());
        int result = accountMapper.updateById(account);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.updateAccount") + "：" + JSONUtil.toJsonStr(account));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAccountEnable(AccountEnableVO accountEnableVO) {
        if (accountEnableVO.getUserId() == null) {
            return -1;
        }
        Account account = accountMapper.selectById(accountEnableVO.getUserId());
        if (null == account) {
            return -2;
        }
        account.setEnable(accountEnableVO.isEnable());
        int result = accountMapper.updateById(account);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.startStopAccount") + "：" + account.getUserName());
        return result;
    }

    private void updateAccountRole(AccountVO accountVO, AccountDTO oldAccountVO) {
        if (accountVO.getRoleIds() == null) {
            if (oldAccountVO.getRoleIds() != null) {
                userRoleMapper.deleteUserRoleMapsByUserId(accountVO.getUserId());
            }
            return;
        }
        List<String> roleIdList = Arrays.stream(accountVO.getRoleIds().split(",")).toList();
        List<String> oldRoleIdList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(oldAccountVO.getRoleIds())) {
            oldRoleIdList = Arrays.stream(oldAccountVO.getRoleIds().split(",")).toList();
        }
        for (String roleId : roleIdList) {
            if (!oldRoleIdList.contains(roleId)) {
                userRoleMapper.createUserRoleMap(accountVO.getUserId(), Integer.parseInt(roleId));
            }
        }
        for (String oldRoleId : oldRoleIdList) {
            if (!roleIdList.contains(oldRoleId)) {
                userRoleMapper.deleteUserRoleMapsByUserIdAndRoleId(accountVO.getUserId(), Integer.parseInt(oldRoleId));
            }
        }
    }

    @Override
    @Transactional
    public int deleteAccountByUserId(int userId) {
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return -2;
        }
        userRoleMapper.deleteUserRoleMapsByUserId(userId);
        int result = accountMapper.deleteById(userId);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, localeMessageSourceUtil.getMessage("audit.report.deleteAccount") + "：" + account.getUserName());
        if (Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            // 重庆电信开关启动，需要注销人主平台的权限
            cqctccService.loginPrivControl(account.getLogonId(), CqctccService.DELETE_ACTION);
        }
        return result;
    }

    @Override
    public void accountHeartbeat(String token, Integer userId, String ipAddr) {
        String key = USER_HEARTBEAT + token;
        boolean hasKey = redisUtil.hasKey(key);
        if (hasKey) {
            // 存在就刷新TTL
            redisUtil.expire(key, userOnlineTTL);
            log.info("Refresh user heartbeat：{}", userId);
        } else {
            Account account = accountMapper.selectById(userId);
            if (account == null) {
                return;
            }
            AccountOnlineDTO accountOnlineDTO = new AccountOnlineDTO();
            accountOnlineDTO.setUserId(account.getUserId());
            accountOnlineDTO.setLogonId(account.getLogonId());
            accountOnlineDTO.setUserName(account.getUserName());
            accountOnlineDTO.setLoginTime(DateUtil.dateToString(new Date()));
            accountOnlineDTO.setLoginIp(ipAddr);
            accountOnlineDTO.setDescription(account.getDescription());
            accountOnlineDTO.setToken(token);
            redisUtil.set(key, JSONUtil.toJsonStr(accountOnlineDTO), userOnlineTTL);
            log.info("Add user heartbeat：{}", userId);
        }
    }

    @Override
    public List<AccountOnlineDTO> findAccountOnline() {
        List<AccountOnlineDTO> accountOnlineDTOS = null;
        try {
            accountOnlineDTOS = redisUtil.getObjectsByPreKey(USER_HEARTBEAT + "*", AccountOnlineDTO.class);
        } catch (IOException e) {
            throw new RuntimeException("Get online user exceptions！");
        }
        return accountOnlineDTOS;
    }

    @Override
    public boolean accountOffline(String token) {
        if (CharSequenceUtil.isBlank(token)) {
            return false;
        }
        // 用户下线
        TokenUser tokenUser = tokenUtil.parseUserFromToken(token);
        if (Objects.isNull(tokenUser)) {
            return false;
        }
        String tokenKey = tokenUser.getUser().getLogonId() + ":" + tokenUser.getUser().getLoginType();
        redisUtil.hdel(LOGIN_USER_TOKEN, tokenKey);
        //移除健康状态
        String userHeartbeatKey = USER_HEARTBEAT + token;
        redisUtil.del(userHeartbeatKey);
        return true;
    }

    @Override
    public List<Account> checkRepeatByLogonId(String newLogonId, String oldLogonId) {
        return accountMapper.selectList(new QueryWrapper<Account>()
                .eq("LogonId", newLogonId)
                .ne(StrUtil.isNotEmpty(oldLogonId), "LogonId", oldLogonId));
    }

    private List<AccountDTO> constructAccountDTOsFrozen(List<AccountDTO> accountDTOs) {
        List<String> keys = accountDTOs.stream()
                .map(dto -> KEY_PREFIX + dto.getLogonId()).toList();
        List<Object> values = redisUtil.mget(keys);
        for (int i = 0; i < accountDTOs.size(); i++) {
            accountDTOs.get(i).setFrozen(values.get(i) != null);
        }
        return accountDTOs;
    }

    @Override
    public List<AccountDTO> findAllByPermission(Integer userId) {
        if (!departmentPermissionService.enableDepartmentPermission()) {
            return this.findAll();
        }
        //开启部门权限获取部门权限
        Collection<Integer> departmentPermissionList = departmentPermissionService.findDepartmentPermissionByUserId(userId);
        List<Integer> employeeIds = employeeService.findEmployeesByDepartmentIds(departmentPermissionList).stream().map(Employee::getEmployeeId).toList();
        List<Account> accounts = this.findByUserIds(employeeIds);
        return constructAccountDTOsFrozen(BeanUtil.copyToList(accounts, AccountDTO.class));
    }

    @Override
    public Set<Integer> findUserIdByPermission(Integer userId) {
        return this.findAllByPermission(userId)
                .stream()
                .map(AccountDTO::getUserId)
                .collect(Collectors.toSet());
    }

    @Override
    public boolean validAccountPassword(Integer userId, String password) {
        AccountDTO account = this.findByUserId(userId);
        if (ObjectUtil.isNull(account)) {
            return false;
        }
        return ObjectUtil.equals(account.getPassword(), password);
    }

    @Override
    public List<AccountDTO> findByDepartmentId(Integer departmentId) {
        List<Employee> employeeList = employeeService.findEmployeesByDepartmentIds(List.of(departmentId));
        List<Account> accounts = this.findByUserIds(employeeList.stream().map(Employee::getEmployeeId).toList());
        return constructAccountDTOsFrozen(BeanUtil.copyToList(accounts, AccountDTO.class));
    }

    private List<Account> findByUserIds(List<Integer> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return accountMapper.selectList(Wrappers.lambdaQuery(Account.class)
                .in(Account::getUserId, userIds));
    }

    private int getHistoryPasswordSaveCounts() {
        //默认不保存历史密码
        int result = 0;
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("password.history.save.counts");
        if (systemConfig != null && !systemConfig.getSystemConfigValue().trim().isEmpty()) {
            result = Integer.parseInt(systemConfig.getSystemConfigValue());
        }
        return result;
    }

    /**
     * 获取密码有效天数
     *
     * @return int 有效的天数
     */
    private int getPasswordEffectiveDays(){
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.PASSWORD_EFFECTIVE_DAYS.getSystemConfigKey());
        if (systemConfig != null && !systemConfig.getSystemConfigValue().trim().isEmpty()) {
            return Integer.parseInt(systemConfig.getSystemConfigValue());
        }
        return PASSWORD_EFFECTIVE_DAYS;
    }

    @PostConstruct
    private void setUserOnlineTTL() {
        long result = 30L;
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("user.online.expire.time");
        if (systemConfig != null && !systemConfig.getSystemConfigValue().trim().isEmpty()) {
            result = Long.parseLong(systemConfig.getSystemConfigValue());
        }
        userOnlineTTL = result;
    }

    @Override
    public Integer findUserIdByLogonId(String logonId) {
        List<Account> accountList = accountMapper.selectList(new QueryWrapper<Account>().eq("LogonId", logonId));
        if (!accountList.isEmpty()) {
            return accountList.get(0).getUserId();
        }
        return null;
    }

    @Override
    public int updatePassword(Integer userId, String password) {
        Account account = accountMapper.selectById(userId);
        if (null == account) {
            return -2;
        }
        account.setPassword(password);
        return accountMapper.updateById(account);
    }

    @Override
    public Map<Integer, String> findUserNameMapByUserIds(List<Integer> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<Account> accountList = accountMapper.selectList(Wrappers.lambdaQuery(Account.class)
                                                                     .select(Account::getUserId, Account::getUserName)
                                                                     .in(Account::getUserId, userIds));
        return accountList.stream()
                          .collect(Collectors.toMap(Account::getUserId, Account::getUserName));
    }

    @Override
    public boolean validAccountPasswordVO(String userName, String password) {
        List<AccountDTO> accounts = this.findByLogonId(userName);
        if (CollUtil.isEmpty(accounts)) {
            return false;
        }
        AccountDTO account = accounts.get(0);
        return ObjectUtil.equals(account.getPassword(), password);
    }

    @Override
    public String findUserNameByLogonId(String logonId) {
        Account account = accountMapper.selectOne(Wrappers.lambdaQuery(Account.class)
                                                          .select(Account::getUserName)
                                                          .eq(Account::getLogonId, logonId));
        if (Objects.isNull(account)) {
            return "";
        }
        return account.getUserName();
    }

    @Override
    public String findUserNameByUserId(Integer userId){
        return accountMapper.findUserNameByUserId(userId);
    }

    @Override
    public boolean passwordInValidTime(String userName) {
        Account account = accountMapper.selectOne(Wrappers.lambdaQuery(Account.class).eq(Account::getLogonId, userName));
        if (null == account) {
            return true;
        }
        return accountMapper.passwordInValidTime(userName);
    }
}
