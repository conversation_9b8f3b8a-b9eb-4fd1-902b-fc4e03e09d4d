package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.as.dto.ActiveEventASDTO;
import com.siteweb.as.dto.ActiveEventDataRecord;
import com.siteweb.as.service.ActiveEventASService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.ProjectStatus;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.StandardDicEventManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.HouseService;
import com.siteweb.monitoring.service.StationStructureService;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.CoreEventSeverityService;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.StandardVerService;
import com.siteweb.utility.service.SystemConfigService;
import org.apache.commons.lang3.builder.CompareToBuilder;
import org.apache.poi.ss.usermodel.Cell;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("activeEventServiceAS")
public class ActiveEventASServiceImpl implements ActiveEventASService {

    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    StandardVerService standardVerService;
    @Autowired
    StandardDicEventManager standardDicEventManager;
    @Autowired
    StationManager stationManager;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    CoreEventSeverityService coreEventSeverityService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    HouseService houseService;

    @Override
    public Page<ActiveEventASDTO> findActiveEvents(int userId, Pageable pageable, ActiveEventFilterVO activeEventFilterVO) {
        //过滤条件
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, activeEventFilterVO);
        if (CollUtil.isEmpty(activeEvents)) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
        sort(pageable.getSort(), activeEvents);
        List<ActiveEvent> slice = activeEvents.stream()
                                              .skip((long) pageable.getPageNumber() * pageable.getPageSize())
                                              .limit(pageable.getPageSize())
                                              .toList();
        List<ActiveEventASDTO> dtoList = toActiveEventASDTO(slice);
        return new PageImpl<>(dtoList, pageable, activeEvents.size());
    }

    @Override
    public ExcelWriter exportActiveEvents(Integer userId, List<String> columns, List<String> titles) {
        // 分页查询活动事件DTO
        PageRequest pageable = PageRequest.of(0, 100000, Sort.by(Sort.Direction.DESC, "startTime"));
        Page<ActiveEventASDTO> activeEventDTOS = findActiveEvents(userId, pageable, new ActiveEventFilterVO());

        // 创建ExcelWriter
        ExcelWriter writer = ExcelUtil.getWriter(true);

        // 添加表头别名映射
        for (int i = 0; i < columns.size(); i++) {
            writer.addHeaderAlias(columns.get(i), titles.get(i));
        }

        // 仅导出设置了别名的字段
        writer.setOnlyAlias(true);

        // 自动调整列宽
        writer.autoSizeColumnAll();

        // 写入数据
        writer.write(activeEventDTOS.getContent(), true);

        // 手动更新某些单元格值
        for (int i = 0; i < activeEventDTOS.getContent().size(); i++) {
            ActiveEventASDTO activeEvent = activeEventDTOS.getContent().get(i);
            // 是否确认列
            processCell(writer, 1, i + 1, activeEvent.getIsConfirmed(), "common.yesOrNo.");
            // 事件等级列
            processCell(writer, 0, i + 1, activeEvent.getEventLevel(), "common.eventLevel.");
        }

        return writer;
    }

    private void processCell(ExcelWriter writer, int columnIndex, int rowIndex, Object value, String keyPrefix) {
        Cell cell = writer.getCell(columnIndex, rowIndex);
        if (cell != null) {
            if (value instanceof Boolean convertValue) {
                cell.setCellValue(messageSourceUtil.getMessage(keyPrefix + (Boolean.TRUE.equals(convertValue) ? GlobalConstants.YES : GlobalConstants.NO)));
            } else if (value instanceof Integer) {
                cell.setCellValue(messageSourceUtil.getMessage(keyPrefix + value));
            }
        }
    }

    @Override
    public List<IdValueDTO<Integer, Long>> findActiveEventStatistics(Integer userId, ActiveEventFilterVO activeEventFilterVO) {
        List<IdValueDTO<Integer, Long>> statistics = constructEventStatistics();
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, activeEventFilterVO);
        if (CollUtil.isEmpty(activeEvents)) {
            return statistics;
        }
        boolean standardAlarmNameIdIsNotNull = systemConfigService.standardAlarmNameIdIsNotNull();
        // 使用Map计算事件级别统计
        Map<Integer, Long> eventCounts = activeEvents.stream()
                                                     .filter(e -> !standardAlarmNameIdIsNotNull || Objects.nonNull(e.getStandardAlarmNameId()))
                                                     .collect(Collectors.groupingBy(ActiveEvent::getEventLevel, Collectors.counting()));
        // 更新统计结果
        statistics.forEach(dto -> dto.setLabel(eventCounts.getOrDefault(dto.getValue(), 0L)));

        return statistics;
    }

    private List<IdValueDTO<Integer, Long>> constructEventStatistics() {
        return coreEventSeverityService.getCoreEventSeverities()
                                       .stream()
                                       .map(e -> new IdValueDTO<>(e.getEventLevel(), 0L))
                                       .sorted(Comparator.comparing(IdValueDTO::getValue))
                                       .toList();
    }

    public List<ActiveEvent> sort(Sort sort, List<ActiveEvent> list) {
        if (CollUtil.isEmpty(list) || CollUtil.isEmpty(sort)) {
            return list;
        }
        list.sort((ActiveEvent activeEventA, ActiveEvent activeEventB) -> {
            CompareToBuilder compToBuild = new CompareToBuilder();
            sort.stream().forEachOrdered(sc -> {
                switch (sc.getProperty()) {
                    case "isConfirmed":
                        //TODO 不确认null的排序规则是向上还是向下
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getConfirmTime(), activeEventB.getConfirmTime());
                        } else {
                            compToBuild.append(activeEventB.getConfirmTime(), activeEventA.getConfirmTime());
                        }
                        break;
                    case "eventLevel":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEventLevel(), activeEventB.getEventLevel());
                        } else {
                            compToBuild.append(activeEventB.getEventLevel(), activeEventA.getEventLevel());
                        }
                        break;
                    case "defaultStationGroupName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getStructureName(), activeEventB.getStructureName());
                        } else {
                            compToBuild.append(activeEventB.getStructureName(), activeEventA.getStructureName());
                        }
                        break;
                    case "stationTypeName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getStationCategoryId(), activeEventB.getStationCategoryId());
                        } else {
                            compToBuild.append(activeEventB.getStationCategoryId(), activeEventA.getStationCategoryId());
                        }
                        break;
                    case "stationName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getStationName(), activeEventB.getStationName());
                        } else {
                            compToBuild.append(activeEventB.getStationName(), activeEventA.getStationName());
                        }
                        break;
                    case "triggerValue":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEndValue(), activeEventB.getEndValue());
                        } else {
                            compToBuild.append(activeEventB.getEndValue(), activeEventA.getEndValue());
                        }
                        break;
                    case "name":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEventName(), activeEventB.getEventName());
                        } else {
                            compToBuild.append(activeEventB.getEventName(), activeEventA.getEventName());
                        }
                        break;
                    case "meaning":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getMeanings(), activeEventB.getMeanings());
                        } else {
                            compToBuild.append(activeEventB.getMeanings(), activeEventA.getMeanings());
                        }
                        break;
                    case "startTime":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getStartTime(), activeEventB.getStartTime());
                        } else {
                            compToBuild.append(activeEventB.getStartTime(), activeEventA.getStartTime());
                        }
                        break;
                    case "endTime":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEndTime(), activeEventB.getEndTime());
                        } else {
                            compToBuild.append(activeEventB.getEndTime(), activeEventA.getEndTime());
                        }
                        break;
                    case "confirmTime":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getConfirmTime(), activeEventB.getConfirmTime());
                        } else {
                            compToBuild.append(activeEventB.getConfirmTime(), activeEventA.getConfirmTime());
                        }
                        break;
                    case "confirmUserName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getConfirmerName(), activeEventB.getConfirmerName());
                        } else {
                            compToBuild.append(activeEventB.getConfirmerName(), activeEventA.getConfirmerName());
                        }
                        break;
                    case "memo":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getDescription(), activeEventB.getDescription());
                        } else {
                            compToBuild.append(activeEventB.getDescription(), activeEventA.getDescription());
                        }
                        break;
                    default:
                        break;
                }
            });
            return compToBuild.toComparison();
        });
        return list;
    }

    private List<ActiveEventASDTO> toActiveEventASDTO(List<ActiveEvent> activeEvents) {
        // 1. 提前检查空集合
        if (CollUtil.isEmpty(activeEvents)) {
            return Collections.emptyList();
        }

        // 2. 预先获取所有配置，避免在stream中重复调用
        ActiveEventDataRecord activeEventDataRecord = loadDataMaps();
        int standardVer = standardVerService.getStandardVer();
        boolean filterByAlarmNameId = systemConfigService.standardAlarmNameIdIsNotNull();

        // 3. 使用单个stream操作链完成转换和过滤
        return activeEvents.stream()
                           .filter(dto -> !filterByAlarmNameId || Objects.nonNull(dto.getStandardAlarmNameId()))
                           .map(activeEvent -> createEventDTO(activeEvent, activeEventDataRecord, standardVer))
                           .collect(Collectors.toList());
    }

    private ActiveEventDataRecord loadDataMaps() {
        //构建StructureNameMap
        List<StationStructure> stationStructureList = stationStructureService.findAll();
        return new ActiveEventDataRecord(
                dataItemService.findIdValueMapByEntryId(DataEntryEnum.EVENT_CATEGORY.getValue()),
                dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_STATE.getValue()),
                dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_CATEGORY.getValue()),
                dataItemService.findIdValueMapByEntryId(DataEntryEnum.DISPATCH_STATUS.getValue()),
                convertToNamePathMap(stationStructureList),
                houseService.houseIdNameMap()
        );
    }

    /**
     * 将站点结构列表转换为ID到命名路径的映射
     *
     * @param structures 站点结构列表
     * @return 结构ID到完整命名路径的映射，命名路径使用下划线连接各级名称
     */
    public static Map<Integer, String> convertToNamePathMap(List<StationStructure> structures) {
        // 参数校验
        if (CollUtil.isEmpty(structures)) {
            return Collections.emptyMap();
        }

        // 构建ID到结构对象的映射，便于快速查找
        Map<Integer, StationStructure> structureIdMap = structures.stream()
                                                                  .collect(Collectors.toMap(
                                                                          StationStructure::getStructureId,
                                                                          Function.identity(),
                                                                          // 处理可能的ID重复情况
                                                                          (existing, replacement) -> existing
                                                                  ));

        // 结果映射：结构ID -> 命名路径
        Map<Integer, String> resultMap = new HashMap<>(structures.size());

        // 为每个结构构建命名路径
        for (StationStructure structure : structures) {
            String namePath = buildNamePath(structure, structureIdMap);
            resultMap.put(structure.getStructureId(), namePath);
        }

        return resultMap;
    }

    /**
     * 递归构建结构的命名路径
     * 从根节点到当前节点，使用下划线连接各级节点名称
     * 根节点(parentStructureId为0)的名称不包含在路径中
     *
     * @param structure     当前结构节点
     * @param structureIdMap ID到结构对象的映射
     * @return 构建好的命名路径，如"分部_区域_站点"
     */
    private static String buildNamePath(StationStructure structure, Map<Integer, StationStructure> structureIdMap) {
        // 判断是否为根节点(一级中心)
        if (isRootNode(structure)) {
            return ""; // 根节点不包含在路径中
        }

        // 获取父节点
        StationStructure parentStructure = structureIdMap.get(structure.getParentStructureId());

        // 处理父节点不存在的情况
        if (parentStructure == null) {
            return structure.getStructureName(); // 直接返回当前节点名称
        }

        // 递归获取父路径
        String parentPath = buildNamePath(parentStructure, structureIdMap);

        // 路径组合逻辑：若父路径为空则不添加分隔符
        return parentPath.isEmpty()
                ? structure.getStructureName()
                : parentPath + "_" + structure.getStructureName();
    }

    /**
     * 判断节点是否为根节点(一级中心)
     *
     * @param structure 结构节点
     * @return 如果是根节点返回true，否则返回false
     */
    private static boolean isRootNode(StationStructure structure) {
        return Objects.equals(structure.getParentStructureId(), 0);
    }

    private ActiveEventASDTO createEventDTO(ActiveEvent activeEvent, ActiveEventDataRecord activeEventDataRecord, int standardVer) {
        Station station = Optional.ofNullable(stationManager.findStationById(activeEvent.getStationId())).orElse(new Station());
        ActiveEventASDTO dto = new ActiveEventASDTO(activeEvent);

        // 设置基本信息
        setBasicInfo(dto, activeEvent, station, activeEventDataRecord);
        // 设置工程状态
        setProjectStateNames(dto);
        // 设置标准化信息
        setStandardInfo(dto, activeEvent, standardVer);
        // 设置分组信息
        dto.setDefaultStationGroupName(activeEventDataRecord.stationStructureNamePathMap().getOrDefault(activeEvent.getStructureId(),""));
        if (activeEvent.getResourceStructureId() > 0) {
            Equipment equipment = Optional.ofNullable(equipmentService.findById(activeEvent.getEquipmentId()))
                                          .orElse(new Equipment());
            // 设置局房信息
            dto.setHouseName(activeEventDataRecord.HouseIdNameMap().getOrDefault(String.format("%s%s", equipment.getHouseId(), equipment.getStationId()), ""));
        }
        return dto;
    }

    private void setBasicInfo(ActiveEventASDTO dto, ActiveEvent activeEvent, Station station, ActiveEventDataRecord activeEventDataRecord) {
        dto.setEventCategoryName(activeEventDataRecord.eventCategoryMap().get(activeEvent.getEventCategoryId()));
        dto.setStationTypeName(activeEventDataRecord.stationCategoryMap().get(station.getStationCategory()));
        dto.setInstructionStatus(activeEventDataRecord.instructionStatusMap().get(activeEvent.getInstructionStatus()));
    }

    /**
     * 设置 DTO 的设备和局站状态名称。
     * 使用枚举替换硬编码 if-else，提升可读性和扩展性。
     * @param dto ActiveEventASDTO 对象
     */
    private void setProjectStateNames(ActiveEventASDTO dto) {
        ProjectStatus status = ProjectStatus.fromCode(dto.getStatus());
        if (status == null) {
            // 默认设置为在网状态
            dto.setEquipmentStateName(getStateName(false));
            dto.setStationStateName(getStateName(false));
            return;
        }

        // 设置设备状态名称
        dto.setEquipmentStateName(getStateName(status.isEquipmentProject()));

        // 设置局站状态名称
        dto.setStationStateName(getStateName(status.isStationProject()));
    }

    /**
     * 获取状态名称字符串。
     * 提取此方法以避免重复调用消息源，提升代码复用性。
     * @param isProject 是否为工程态
     * @return 对应的国际化字符串
     */
    private String getStateName(boolean isProject) {
        if (isProject) {
            return messageSourceUtil.getMessage("projectstate.text");  // 工程态
        } else {
            return messageSourceUtil.getMessage("projectstate.network");  // 网络态
        }
    }

    private void setStandardInfo(ActiveEventASDTO dto, ActiveEvent activeEvent, int standardVer) {
        if (activeEvent.getStandardAlarmNameId() == null) {
            return;
        }
        StandardDicEvent standardDicEvent = standardDicEventManager.getStandardDicEvent(activeEvent.getStandardAlarmNameId(), standardVer);
        if (standardDicEvent == null) {
            return;
        }
        dto.setEquipmentLogicCategory(standardDicEvent.getEquipmentLogicClass());
        dto.setAlarmLogicCategory(standardDicEvent.getEventLogicClass());
        dto.setStdSignalDescription(standardDicEvent.getExtendFiled1());
        dto.setStdSignalMeanings(standardDicEvent.getMeanings());
        dto.setStdNote(standardDicEvent.getExtendFiled2());
    }

    @Override
    public List<ActiveEventASDTO> getActiveEventByStationAndEquipment(Integer stationId, Integer equipmentId) {
        List<ActiveEvent> activeEvents = activeEventManager.getActiveEventsByStationIdAndEquipmentId(stationId, equipmentId);
        List<ActiveEventASDTO> activeEventASDTO = toActiveEventASDTO(activeEvents);
        CollUtil.sort(activeEventASDTO, Comparator.comparing(ActiveEventASDTO::getStartTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        return activeEventASDTO;
    }

    @Override
    public List<ActiveEventASDTO> getActiveEventByStationAndHouseId(Integer stationId, Integer houseId) {
        List<ActiveEvent> activeEvents = activeEventManager.queryAllActiveEvents()
                                                   .stream()
                                                   .filter(e -> Objects.equals(e.getStationId(), stationId))
                                                   .filter(e -> {
                                                       Equipment equipment = Optional.ofNullable(equipmentService.findById(e.getEquipmentId()))
                                                                                     .orElse(new Equipment());
                                                       return Objects.equals(equipment.getHouseId(), houseId);
                                                   })
                                                   .toList();
        List<ActiveEventASDTO> activeEventASDTO = toActiveEventASDTO(activeEvents);
        CollUtil.sort(activeEventASDTO, Comparator.comparing(ActiveEventASDTO::getStartTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        return activeEventASDTO;
    }
}
