package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.MenuItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MenuItemMapper extends BaseMapper<MenuItem> {

    List<MenuItem> findAllMenuItems();

    MenuItem findMenuItemById(Integer menuItemId);

    List<MenuItem> findChildrenMenuItems(Integer parentMenuItemId);

    String findTopMenuItemPath();

    List<MenuItem> findByParentId(Integer parentId);

    int batchUpdate(List<MenuItem> menuItemList);

    List<MenuItem> findBySceneIdAndParentId(Integer sceneId, Integer parentId);

    /**
     * @param serverIp 服务器ip
     */
    void updateVideoMenuItem(@Param("serverIp") String serverIp);
}

