<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-111" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            -- 菜单方案MenuProfile
            INSERT INTO "MenuProfile" ("MenuProfileId", "Name", "Checked", "DESCRIPTION") VALUES (1, 'IDC', 1 ,'IDC场景');
--             INSERT INTO "MenuProfile" ("MenuProfileId", "Name", "Checked", "DESCRIPTION") VALUES (2,'SiteWeb2',0 ,'电信场景');
            INSERT INTO "MenuProfile" ("MenuProfileId", "Name", "Checked", "DESCRIPTION") VALUES (3,'机器人',0 ,'机器人场景');
            INSERT INTO "MenuProfile" ("MenuProfileId", "Name", "Checked", "DESCRIPTION") VALUES (5,'储能电站',0 ,'储能场景');



            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (1,0,'entry',NULL,'fa fa-bar-chart',1,'0','0',NULL,'0','1','1',NULL,1,NULL);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (10,1,'hmi?pageId=2000','综合展示',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'Dashboard');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (20,1,'alarms/lists','活动告警','iconfont icon-shebeizugaojing','1','0',NULL,'prefix','1','1',NULL,NULL,1,'Alarm List');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (30,1,'report','报表',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'Report');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (40,1,'basicinfo','用户信息','iconfont icon-quanxianguanli',1,'1','0','prefix','1','1','1',NULL,1,'User Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (41,1,'onlineUser','在线用户','iconfont icon-organization',1,'1','0','prefix','1','1','1',NULL,1,'Online User');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES(42, 1, 'usersetting', '用户配置', 'iconfont icon-organization', 1, 1, 0, 'prefix', 1, 1, '1', NULL, '1', 'User Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (50,1,'usermanagement','用户管理','iconfont icon-quanxianguanli',1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'User Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (51,50,'person','人员管理','iconfont icon-group',1,'0','0','prefix','1','1','0',NULL,1,'Staff Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (52,50,'account','帐号管理','iconfont icon-group',1,'0','0','prefix','1','1','0',NULL,1,'Account Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (53,50,'roleauthorization','角色授权',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Role Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (54,50,'areapermissions','区域权限',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Area Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (55,50,'operationpermissions','操作权限',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Operation Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (56,50,'menupermissions','菜单权限',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Menu Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (60,1,'systemmanage','系统设置','iconfont icon-shezhi',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'System Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (61,60,'systemconfig','系统参数',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'System Params');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (62,60,'custommenu','自定义菜单','fa fa-bars',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Custom Menu');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION") VALUES (63,60,'scene','场景切换','fa fa-bars',1,NULL,NULL,NULL,'1','0',NULL,NULL,1);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (64,60,'themelogo','主题及logo',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Theme &amp; Logo');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (65, 60, 'about', '关于', NULL, 1, NULL, NULL, 'prefix', 1, '1', NULL, NULL, 1,'About');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (66,60,'securityConfig','安全配置',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Security Params');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (67,60,'pageConfig','页面参数',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Webpage Params');

            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (68,60,  'mainpageconfig', '首页组态配置',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'Homepage Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (74,68, 'usergraphicpage', '用户首页组态',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'User Homepage');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (75,68, 'rolegraphicpage', '角色首页组态',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Role Homepage');

            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (70,1,'monitor','数据监控','iconfont icon-realtimedata',1,'0','0',NULL,'1','1','0',NULL,1,'Data Monitor');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (71,70,'idcdevicemonitor','设备监控',null,1,'0','0',NULL,'1','1','0',NULL,1,'Device Monitor');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (72,70,'focussignal','关注信号',null,1,'0','0',NULL,'1','1','0',NULL,1,'Focus Signal');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION") VALUES (73,70,'stationstate','局站状态',null,1,'0','0',NULL,'1','0','0',NULL,NULL);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (80,1,'idcmanage','对象管理','fa fa-building-o',1,FALSE,FALSE,NULL,'1','1',NULL,NULL,1,'Object Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (81,80,'hierarchy','层级','fa fa-sitemap',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Hierarchy');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (82,80,'devicemanagement','设备管理','iconfont icon-device-info',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Device');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (83,1,'idcmanage','机架管理','iconfont icon-serverinfo',1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'Rack Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (84,83,'rackmanage','机架管理','fa fa-align-justify',15,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Rack Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (85,83,'itdevicemanage','IT设备管理',NULL,15,NULL,NULL,'prefix','1','1',NULL,NULL,1,'IT-Device');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (86,83,'itdevicemodel','IT设备模型',NULL,15,NULL,NULL,'prefix','1','1',NULL,NULL,1,'IT-Device Model');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (87,83,'rackbranch','机架支路','fa fa-tasks',15,NULL,NULL,'prefix','1','0',NULL,NULL,1,'Rack Branch');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (101,83,'tagmanage','标签管理','fa fa-tasks',15,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Tag Management');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION") VALUES (102,83,'equipmentbranch','设备支路管理','fa fa-tasks',15,NULL,NULL,'prefix','1','0',NULL,NULL,NULL);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (104, 83, 'rackequipmentconfig', '机架设备配置', 'fa fa-tasks', '15', NULL, NULL, NULL, '1', '1', NULL, NULL, '1', 'Rack Device Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (105, 1, 'racksignalconfig', '机架信号配置', NULL, 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', 'Rack Signal Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (106, 1, 'capacitymanagementboard', '容量管理展板', NULL, 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', '容量管理展板');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (107, 1, 'itcabinet', 'IT机柜', NULL, 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', 'It Cabinet');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (108, 1, 'alarms/history/list', '历史告警', 'iconfont icon-document', 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', 'History Alarm');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (109, 1, 'report-tree', '树型报表', NULL, 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', 'ReportTree');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (88,80,'generalobject','通用对象','fa fa-sitemap',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'General Object');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (89, 1, 'rackcustomermanage', '租户管理', 'iconfont icon-jueceguanli', '1', NULL, NULL, 'prefix', '1', '1', NULL, NULL, '1', 'Rack Customer Manage');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (100,1,'','告警管理','iconfont icon-module',1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'Monitoring');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (125,1,'alarmfitting','告警收敛','iconfont icon-xinhaozugaojing',1,NULL,NULL,'prefix','1','0',NULL,NULL,1,'Alarm Convergence');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (120,1,'mask','屏蔽管理','iconfont icon-cancle',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Suppression Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (121,120,'devicemask','已屏蔽设备',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Device Suppression');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (122,120,'alarmmask','已屏蔽告警',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Alarm Suppression');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (123,120,'devicebulkblock','批量屏蔽',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Bulk Suppression');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (124,120,'blockbyconfig','条件屏蔽',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Condition Suppression');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (128, 1, 'idcmanage/capacitymanage', '容量管理', 'iconfont icon-jindutiao', 11,FALSE, FALSE, 'prefix', '1', '1', FALSE, NULL, 1,'Capacity');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (129,1,'energypanorama','能耗全景图','iconfont icon-sangjitu',13,0,0,'prefix',1,1,'1',0,1,'Energy Panorama');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (130,1,'hmi?pageId=2010','能耗看板','iconfont icon-aligaojingtongji',13,FALSE,FALSE,NULL,1,TRUE,'1', NULL, 1,'Energy Dashboard');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (131,1, 'saveenergy', '能耗节能措施', 'iconfont icon-jueceguanli', 13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1,'Energy-Saving Measure');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (132,131, 'energyinfo', '节能措施信息', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1,'Measure Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (133,131, 'energyreport', '节能分析', NULL, 13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1,'Analysis');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (144,154, 'multidimensionalConfig', '多维度管理', 'iconfont icon-expertrecommendations', 13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1,'Multi-Dimensional');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (135,144, 'hierarchicaltreeconfig', '多维度方案配置', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1,'Solution Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (136,144, 'indicatorconfig', '多维度指标配置', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1,'Indicator Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (143,154, 'energyoperate', '计价方案', 'iconfont icon-dianyuan1', 13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1, 'Peak-valley Price');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (138,143, 'electricinfo', '单价配置', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Price Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (139,143, 'operatelog', '操作日志', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Operation Log');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (140,133, 'measuresBeforeAfter', '措施前后用电量', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Measures Electricity Consumption');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (141,133, 'measuresIsNot', '措施与否对比', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Measures Contrast');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (142,133, 'measuresRank', '措施效果排名', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Measures Rank');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (134,154, 'energyconfig', '字典', 'iconfont icon-knowledgebase', 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Energy Dictionary');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (137,154, 'energyclassify', '能源指标管理', 'iconfont icon-index-comparison', 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Energy ComplexIndex');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (145,1, 'energyefficiency', '能效等级评定', 'iconfont icon-jueceguanli', 13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1, 'Energy Efficiency Assessment');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (146,145, 'gradeevaluate', '等级评定', 'iconfont icon-realtimedata', 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Gradeevaluate');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (147,145, 'parameterconfig', '等级配置', 'iconfont icon-pingguguanli', 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Parameterconfig');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (148, 1, '', '查询分析', 'iconfont icon-jueceguanli',  13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1, 'Query Analysis');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (149, 148, 'totaltrendreport', '用能总量趋势', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Total Consumption');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (150, 148, 'totalpuereport', '用能效率趋势', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Energy Efficiency');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (151, 148, 'totaloptionreport', '用能分项趋势', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Option Consumption');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (152, 148, 'totalotherreport', '其他用能趋势', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Other Usage');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (153, 148, 'totalfeereport', '用能费用趋势', NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Energy Fee');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (154, 1, '', '配置管理', 'iconfont icon-gaojingliebiao', 13,FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1, 'Config Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (155,154,'carbonquota','碳配额管理',NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'CarbonQuota Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (156,154,'carbonemission','碳排强度核算',NULL, 13,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'CarbonEmission Management');

            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (160,1,'','预警管理','iconfont icon-signals-alarm',1,NULL,NULL,NULL,'1','1',NULL,NULL,1,'Warning Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (161, 160, 'capacitysystem/capacitywarning', '预警列表',  'iconfont icon-gaojingliebiao', 14,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Warning List');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (162, 160, 'idcmanage/prealarmmanage', '预警配置',  NULL, 14,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Warning Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (163, 160, 'prealarmmanage/prealarmconfig', '预警等级',  NULL, 14,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Warning Level');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (164, 160, 'prealarmmanage/maskprealarmpoint', '预警屏蔽',  NULL, 14,FALSE, FALSE, 'prefix', 1, TRUE, '1', NULL, 1, 'Warning Suppression');
--             INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (165,1,  '', '设备管理工具',  'iconfont icon-rack-devices', 1, FALSE, FALSE, NULL, 1, TRUE, FALSE, NULL, 1, 'Self Protocol');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (166,165,  'selfprocotocol', '协议自助设备',  '', 1, FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1, 'Self Protocol');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (167, 166, 'snmp', 'SNMP配置管理', '', 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'SNMP');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (168, 166, 'bacnet', 'BACNet配置管理', '', 1, 0, 0, 'prefix', 1, 1, '1', NULL, 1, 'BACNet');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (169, 166, 'drivertemplate', '驱动模板管理', '', 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Driver Template');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (170, 165, 'selfprocotocol/virtualequipment', '虚拟设备构造', '', 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Device Split Tool');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (171,1,'battery', '蓄电池管理',  'iconfont icon-battery-string', 1, FALSE, FALSE, NULL, 1, TRUE, '1', NULL, 1, 'Battery Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (172, 171, 'batteryModel', '电池模型', '', 1, 0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Battery Model');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (173, 171, 'batteryString', '电池配置', '', 1, 0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Battery Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (260,1,'hmi?pageId=8','配电管理', 'iconfont icon-powerdistribution', 1, 1, 0, NULL, 1, 1, '1', NULL, 1,'Distribution Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (261,1,'s2/bms','电池管理', 'iconfont icon-battery-string', 1, 1, 0, NULL, 1, 1, '1', NULL, 1,'Battery Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (262,261,'bmsdashboard','电池看板', NULL, 1, 1, 0, 'prefix', 1, 1, '1', NULL, 1,'Battery Dashboard');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (263,261,'bmsviewbase','电池监控', NULL, 1, 1, 0, 'prefix', 1, 1, '1', NULL, 1,'Battery Monitor');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (264,261,'','电池配置', NULL, 1, 1, 0, NULL, 1, 1, '1', NULL, 1,'Battery Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (265,264,'assetdevice1','资产配置', NULL, 1, 1, 0, 'prefix', 1, 1, '1', NULL, 1,'Asset Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (267,264,'preventionmaintain','数据分析', NULL, 1, 1, 0, NULL, 1, 1, '1', NULL, 1,'Data Analysis');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (268,264,'bmssystemsetting','系统设置', NULL, 1, 1, 0, 'prefix', 1, 1, '1', NULL, 1,'System Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (266, 1,  'topography', '系统自诊断', 'iconfont icon-zizhenduan', 1, 1, 0, 'prefix', 1, 1, '1', NULL, 1,  'System Self Diagnosis');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (290,100, 'alarmnotification', '告警通知',  NULL, 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Alarm Notification');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (291,290, 'tts', 'TTS语音',  NULL, 1, 0,0, 'prefix', 1, 1, '1', NULL, 1, 'TTS Speech');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (292,290, 'notificationpolicy', '通知策略',  NULL, 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Notification Policy');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (293,290, 'safemsg', '平安短信',  NULL, 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Safe SMS');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (294,100,'featuremanage/monitor/alarmconfig','告警','iconfont icon-bell-outline',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Alarm');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION") VALUES (295,100,'alarmvideolink','告警视频联动','iconfont icon-bell-outline',1,NULL,NULL,'prefix','1','1',NULL,NULL,NULL);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (296,1, 'ubitmanagement', '机架管理',  'iconfont icon-server-frame', 1,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Rack Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (320,1,  'linkagestrategy', '后台联动',  'iconfont icon-runing', 18,0, 0, 'prefix', 1, 1, '1', NULL, 1, 'Linkage Strategy');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (321,100,'featuremanage/distributiontopology/relationship','配电关系管理','iconfont icon-bell-outline',1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Distribution Relation');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (542,1,'accesscontrol-plus','门禁管理','iconfont icon-accesscontrol',19,NULL,NULL,NULL,1,'1',NULL,NULL,1,'Access Control');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (543,542,'area-management','区域管理',NULL,19,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Area Permissions');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (544,542,'card-management','卡管理',NULL,19,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Card Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (545,542,'door-management','门管理',NULL,19,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Door Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (546,542,'data-maintenance/timegroup','准进时间',NULL,19,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Permission Time Group');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (547,542,'card-authorization','卡授权',NULL,19,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Card Authorization');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (548,542,'control-queue','控制队列',NULL,19,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Control Queue');
            -- 图表数据分离配置
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (340,1,'chartdataseparateconfig','图表数据分离配置','iconfont icon-jueceguanli',1,NULL,NULL,NULL,1,'1',NULL,NULL,1,'Chart Data Setting');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (341,340,'charttheme','图表主题','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Chart Theme');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (342,340,'chartapi','图表数据接口','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Chart Interface');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (343,340,'chartstyle','图表样式','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Chart Style');
            -- 空调节能
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (344,1,'airconditionControl','空调节能','iconfont icon-air-condition-group',1,NULL,NULL,NULL,1,'1',NULL,NULL,1,'Air Energy Efficiency');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (345,344,'airBoard','空调看板','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Air Board');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (346,344,'templateMap','模板映射','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Template Map');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (347,344,'parameterConfig','参数配置','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Parameter Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (348,344,'aircontrolgroup','批量控制','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Air Control Group');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (349,344,'electricityanalysis','用电分析','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Electricity Analysis');
            -- AI空调
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (400,1,'','AI机房节能','iconfont icon-jifangjieneng',1,NULL,NULL,NULL,1,'1',NULL,NULL,1,'AI Room Energy Saving');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (401,400,'hmi?pageId=2001','AI机房总览',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,1,'AI ROOM Overview');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (402,400,'aienergysaving/home','AI驾驶舱',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Cockpit');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (403,400,'aienergysaving/groupcnf','AI模型分组配置',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Model Group Configuration');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (404,400,'aienergysaving/modelmanage','AI模型管理',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Model Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (405,400,'aienergysaving/policyhistory','AI策略历史',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Strategy History');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (406,400,'aienergysaving/aistrategymanagement','AI节能参数设置',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Energy Saving Parameter Settings');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (407,400,'aienergysaving/assetsmanage','AI资产管理',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Asset Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (408,400,'aienergysaving/consumptionreport','AI能耗报表',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Energy Report');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (409,400,'aienergysaving/compare','AI对比分析',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Compare Analysis');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (410,400,'aienergysaving/scheduledhalt','AI排班计划',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'AI Schedule Plan');
            -- 排班管理
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (350,1,'shiftmanagement','排班管理','iconfont icon-calendar-copy',1,NULL,NULL,NULL,1,'1',NULL,NULL,1,'Shift Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (351,350,'shift','班次管理','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Shift Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (352,350,'shiftgroup','班组管理','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Shift Group Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (353,350,'schedule','排班管理','',1,NULL,NULL,'prefix',1,'1',NULL,NULL,1,'Schedule Management');

            -- 功能管理
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (355,1,'assetdevice','资产列表','iconfont icon-device-info',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Asset List');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (356,1,'assetmanager','资产管理','iconfont icon-device-info',1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Asset');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (357,356,'assetcategory','资产类型',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Asset Category');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (358,356,'assetextend','扩展字段',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Asset ExtendField');

            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (359,60,'businessconfig','业务模块配置',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Business Config');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (360,1,'helpdoc','在线文档','iconfont icon-document',1,NULL,NULL,'prefix','1','0',NULL,NULL,1,'Document');

            -- idc专家建议
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES(570, 1, 's6/expertadvicecollect', '专家系统', 'iconfont icon-expertrecommendations', 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '2', 'Knowledge Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES(571, 570, 'expertmanagement', '知识库', NULL, 1, NULL, NULL, 'prefix', 1, '1', NULL, NULL, NULL, 'Repository');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES(572, 570, 'expertsearch', '关联案例', NULL, 1, NULL, NULL, 'prefix', 1, '1', NULL, NULL, NULL, 'Relvant Case');

            -- 告警抓图
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (361,1,'alarms/alarmsnapshot','告警抓图','iconfont icon-image','1','0',NULL,NULL,'1','1',NULL,NULL,1,'Alarm Snapshot');

            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (362,1,'video','视频','iconfont icon-shipin1',1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Camera');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (363,362,'cameragroup','视频分组',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Camera Group');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (364,362,'cameras','视频配置',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Camera Config');

            -- 链路管理
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION", "Alias") VALUES (420, 1, 'linkmanagement', '链路管理', 'iconfont icon-powerdistribution', 1, NULL, NULL, NULL, 1, '1', NULL, NULL, 1, 'Link Management');

            -- 四分屏
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(600, 1, '/entry?isQuad=true', '四分屏', NULL, 1, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', 'Four Panel Layout', NULL);


            -- 网点
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (11,1,'hmi?pageId=2022','综合展示',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,2,'Dashboard');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (512,1,'monitor/telealarms','事件浏览','iconfont icon-alarm',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Event Browsing');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (513,1,'monitor/teleshieldevent','屏蔽管理','fa fa-shield',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Suppression Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (507,1,  '', '数据监控', 'iconfont icon-realtimedata', 1,FALSE,FALSE, 'prefix', 1, TRUE, '1', NULL, 2, 'Data Monitor');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (508,507,'monitor/teledevicemonitor','设备监控','iconfont icon-devicemonitor',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Device Monitoring');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (509,507,'monitor/telefocussignal','关注信号','iconfont icon-real-signal',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Attention Signal');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (510,507,'monitor/telestationstate','局站状态','iconfont icon-status',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Station State');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (511,507,'s2/digitalmap','电子地图','fa fa-futbol-o',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Electronic Map');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (533,1,'idcautopatrol','自动巡检','iconfont icon-zidongxunjian',1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Auto Patrol');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (534,533,'ruleconfig','规则配置',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Rule Define');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (535,533,'groupmanagement','分组管理',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Group Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (536,533,'patroltask','巡检任务',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Patrol Task');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (537,533,'reportmanagement','报表下载',NULL,1,NULL,NULL,'prefix',1,0,NULL,NULL,2,'Report Download');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (538,533,'alarminfo','预警信息',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Warning Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (539,1,'monitor/teleexpertadvice','专家系统','iconfont icon-expertrecommendations',1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Knowledge Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (540,539,'expertmanagement','知识库',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Repository');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (541,539,'expertsearch','关联案例',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Relvant Case');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (553,1,  '', '用户管理',  'iconfont icon-operation', 1,FALSE,FALSE, NULL, 1, TRUE, '1', NULL, 2, 'Privilege Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (554,553,'usermanagement/person','人员管理',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Staff Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (555,553,'usermanagement/account','账号管理',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Account Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (556,553,'usermanagement/roleauthorization','角色授权',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Role Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (557,553,'s2/authoritymanagement/professionalauthority','专业权限',NULL,1,NULL,null,'prefix',1,'1',NULL,NULL,2,'Major Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (558,553,'usermanagement/teleareaauthority','片区权限',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Area Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (559, 553, 'usermanagement/areapermissions', '区域权限', NULL, 1, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '2', 'Region Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (562, 553, 'usermanagement/operationpermissions', '操作权限', NULL, 1, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '2', 'Operation Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (563,553,'usermanagement/menupermissions','菜单权限',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,1,'Menu Permission');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (528,1,'s2/versionmanagement-plus','版本管理','iconfont icon-versionmanagement1',1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Version Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (529,528,'fsumanagement','FSU信息',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Gateway Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (519,529,'fsupie','FSU统计图',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Gateway Pie Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (520,529,'fsutable','FSU列表',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Gateway List');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (530,528,'contractinfo','合同信息',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Contract Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (521,530,'stationcontract','局站合同',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Station Contract Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (522,530,'equipmentcontract','设备合同',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Device Contract Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (523,530,'fsucontract','FSU合同',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Gateway Contract Info');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (531,528,'contractmanagement','合同管理',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'Contract Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (524,531,'installcontract','安装合同',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Install Contract Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (525,531,'maintenancecontract','维保合同',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Maintenance Contract Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (532,528,'qrcode','二维码管理',NULL,1,NULL,NULL,NULL,1,'1',NULL,NULL,2,'QRcode Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (526,532,'platformqrcode','平台二维码',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Platform QRcode');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (527,532,'fsuqrcode','FSU二维码',NULL,1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Gateway QRcode');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (550,1,'s2/reportmanagement/reportscan','报表浏览','iconfont icon-devicemonitor',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Browse Report');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (551,1,'s2/reportmanagement/reportconfig','报表配置','iconfont icon-baobiaoguanli',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Report Config');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (552,1,'s2/reportmanagement/reporttask','报表任务','iconfont icon-baobiaoliebiao',1,NULL,NULL,'prefix',1,'1',NULL,NULL,2,'Report Task');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (549,1,'s2/reportmanagement/assetManagement','资产管理','iconfont icon-device-info',16,NULL,NULL,'prefix',1,'1',NULL,NULL,NULL,'Asset Management');
            -- 电信场景下能耗页面
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (560,1,'hmi?pageId=2011','能耗看板','iconfont icon-aligaojingtongji',13,FALSE,FALSE,NULL,1,TRUE,FALSE, NULL, 2,'Energy Dashboard');
            -- INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (561,1,'hmi?pageId=2012','碳看板','iconfont icon-aligaojingtongji',13,FALSE,FALSE,NULL,1,TRUE,FALSE, NULL, 2,'EnergyCarbon Dashboard');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias") VALUES(580, 1, 'http://127.0.0.1:5000', '接入管理', 'iconfont icon-jieruguanli', 1, NULL, NULL, 'prefix', 1, '1', 1, NULL, '2', 'Access Management');
--             INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias") VALUES (581, 1, 'topologysystem', '系统拓扑', 'iconfont icon-zuzhiguanli', 1, NULL, NULL, 'prefix', 1, 0, 0, NULL, '2', 'System Topology');


            -- 机器人  800-900
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (700,1,'hmi?pageId=2057','首页',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,NULL,'Home Page');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (701,1,'hmi?pageId=2058','机器人',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,NULL,'Robot');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (702,1,'robotalarm','告警总览',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,NULL,'Alarm Overview');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (703,1,'robottask','任务管理',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,NULL,'Task Management');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (704,1,'robotchart/chart','数据分析',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,NULL,'Data Analysis');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (705,1,'hmi?pageId=2062','远程控制',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,NULL,'Remote Control');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (706,1,'robotmaintain/robotvisitormanagement','维护管理',NULL,1,NULL,NULL,'prefix','1','1',NULL,NULL,NULL,'Maintain Management');

            -- 储能电站
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (12,1,'hmi?pageId=2025','首页',NULL,1,NULL,NULL,NULL,'1','1',NULL,NULL,NULL,'Home Page');
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias") VALUES (15, 1, 'hmi?pageId=2030', '电池安全', NULL, 1, NULL, NULL, NULL, 1, '1', NULL, NULL, NULL, 'Battery Security');

            -- IDC门禁
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6000, 1, 'accesscontrol-plus', '门禁管理', 'iconfont icon-accesscontrol', 19, NULL, NULL, NULL, 1, '1', NULL, NULL, '1', 'Access Control', 0);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6001, 6000, 'area-management', '区域管理', NULL, 19, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '1', 'Area Permissions', 0);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6002, 6000, 'card-management', '卡管理', NULL, 19, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '1', 'Card Management', 0);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6003, 6000, 'door-management', '门管理', NULL, 19, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '1', 'Door Management', 0);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6004, 6000, 'data-maintenance/timegroup', '准进时间', NULL, 19, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '1', 'Permission Time Group', 0);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6005, 6000, 'card-authorization', '卡授权', NULL, 19, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '1', 'Card Authorization', 0);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description", "Alias", "isEmbed") VALUES(6006, 6000, 'control-queue', '控制队列', NULL, 19, NULL, NULL, 'prefix', 1, '1', NULL, NULL, '1', 'Control Queue', 0);




            -- 告警管理
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (1,1,0,'个人中心','iconfont icon-protect-record',0,1,1,999,1,NULL,'Personal');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (2,1,0,'配置管理','iconfont icon-shezhi',0,1,1,999,1,NULL,'Configuration');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (3,1,0,'后台管理','iconfont icon-theme',0,1,1,999,1,NULL,'Backstage');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (4,1,0,'告警管理',NULL,NULL,NULL,0,20,0, NULL,'Alarm');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (5,1,0,'实时监控',NULL,NULL,NULL,0,30,0, NULL,'Realtime');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (6,1,0,'业务分析',NULL,NULL,NULL,0,40,0, NULL,'Business');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (7,1,0,'能源管理',NULL,NULL,NULL,0,50,0, NULL,'Energy');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (8,1,0,'维护管理',0,0,1,0,60,1,NULL,'Maintain');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (9,3,0,'后台管理','iconfont icon-theme',FALSE,TRUE,TRUE,998,TRUE, NULL,'Backstage');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (52,2,0,'告警管理',NULL,NULL,NULL,0,20,0,NULL,'Alarm');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (51,2,0,'实时监控',NULL,NULL,NULL,0,30,0,NULL,'Realtime');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (55,2,0,'维护管理',NULL,NULL,NULL,0,60,0,NULL,'Maintain');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (56,2,0,'报表管理',NULL,NULL,NULL,0,70,0,NULL,'Report');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (60,2,0,'业务分析',NULL,NULL,NULL,0,40,0,NULL,'Business');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (61,2,0,'能源管理',NULL,NULL,NULL,0,50,0,NULL,'Energy');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (91,2,0,'个人中心','iconfont icon-protect-record',FALSE,TRUE,TRUE,998,TRUE, NULL,'Personal');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (92,2,0,'配置管理','iconfont icon-shezhi',FALSE,TRUE,TRUE,998,TRUE, NULL,'Configuration');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES (93,2,0,'后台管理','iconfont icon-theme',FALSE,TRUE,TRUE,998,TRUE, NULL,'Backstage');

            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(150, 5, 0, '个人中心', 'iconfont icon-protect-record', 0, 1, 1, 999, 1, NULL, 'Personal');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(151, 5, 0, '配置管理', 'iconfont icon-shezhi', 0, 1, 1, 999, 1, NULL, 'Configuration');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(152, 5, 0, '后台管理', 'iconfont icon-theme', 0, 1, 1, 999, 1, NULL, 'Backstage');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(153, 5, 0, '告警管理', NULL, NULL, NULL, 0, 20, 0, NULL, 'Alarm');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(154, 5, 0, '实时监控', NULL, NULL, NULL, 0, 30, 0, NULL, 'Realtime');
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(155, 5, 0, '维护管理', '0', 0, 1, 0, 60, 1, NULL, 'Maintain');



            -- 综合展示
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (1,1,0,10,1);
            -- 告警管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (2,1,4,20,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (3,1,4,125,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (4,1,4,120,1);
            -- 实时监控
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (8,1,5,70,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (9,1,5,6000,1);

            -- 业务分析
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (21,1,6,260,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (22,1,6,296,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (23,1,6,261,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (24,1,6,161,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (25,1,6,355,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (26,1,6,400,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (27,1,6,344,1);

            -- 能源管理
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (50,1,129,7,2);
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (51,1,130,7,1);
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (52,1,131,7,5);
            /*
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (54,1,137,7,999);
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (55,1,143,7,999);
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (56,1,144,7,999);
            */
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (57,1,145,7,4);
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (58,1,148,7,3);
            INSERT INTO "MenuItemStructureMap"("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (59,1,154,7,6);

            -- 维护管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (60,1,8,266,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (61, 1, 8, 581, 1);


            -- 报表管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (70,1,0,30,100);

            -- 配置管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (80,1,2,50,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (81,1,2,100,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (82,1,2,83,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (83,1,2,320,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (84,1,2,340,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (85,1,2,350,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (87,1,2,356,8);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (88,1,2,357,9);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (89,1,2,358,10);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (90,1,2,420,11);

            -- 后台管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (120,1,3,80,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (121,1,3,160,3);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (122,1,3,128,2);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (124,1,3,165,5);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (126,1,3,60,6);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (127,1,3,171,4);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (128,1,3,360,7);

            -- 个人中心
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (150,1,1,40,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (151,1,1,41,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (152, 1, 1, 42, 999);

            -- idc专家建议
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(160, 1, 8, 570, 999);

            -- 综合展示
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES('248','2','0','11','1');
            -- 告警管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES('212','2','52','512','999');
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES('213','2','52','513','999');
            -- 实时监控
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES('214','2','51','507','999');
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES('218','2','51','542','999');

            -- 业务分析
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (300,2,60,260,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (301,2,60,296,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (302,2,60,261,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (303,2,60,161,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (304,2,60,400,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (305,2,60,344,999);

            -- 能源管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (350,2,129,61,2);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (351,2,560,61,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (352,2,131,61,5);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (357,2,145,61,4);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (359,2,148,61,3);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (360,2,154,61,6);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuItemId","MenuStructureId", "SortIndex") VALUES (361,2,561,61,7);

            -- 维护管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuStructureId","MenuItemId", "SortIndex") VALUES (370,2,55,533,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuStructureId","MenuItemId", "SortIndex") VALUES (371,2,55,539,999);
            -- INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuStructureId","MenuItemId", "SortIndex") VALUES (372,2,55,266,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuStructureId","MenuItemId", "SortIndex") VALUES (373,2,55,549,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId","MenuProfileId","MenuStructureId","MenuItemId", "SortIndex") VALUES (374,2,55,580,999);

            -- 报表
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (236,2,56,550,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (237,2,56,551,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (238,2,56,552,999);
            -- 个人中心
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (101,2,91,40,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (102,2,91,41,999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (103,2,91,42,999);
            -- 配置管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (380,2,92,553,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (381,2,92,100,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (382,2,92,83,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (383,2,92,320,1);

            -- 后台管理
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (400,2,93,80,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (401,2,93,160,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (402,2,93,128,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (403,2,93,528,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (404,2,93,165,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (405,2,93,60,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (406,2,93,360,1);

            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (700,3,0,700,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (701,3,0,701,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (702,3,0,702,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (703,3,0,703,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (704,3,0,704,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (705,3,0,705,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (706,3,0,706,1);

            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (707,3,9,80,1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (708,3,9,60,6);

            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(800, 5, 150, 40, 999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(801, 5, 150, 41, 999);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(802, 5, 0, 15, 2);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(810, 5, 151, 50, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(811, 5, 151, 100, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(812, 5, 151, 320, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(813, 5, 151, 340, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(814, 5, 151, 350, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(820, 5, 152, 80, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(821, 5, 152, 60, 6);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(822, 5, 153, 20, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(823, 5, 153, 125, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(824, 5, 153, 120, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(830, 5, 154, 70, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(831, 5, 154, 542, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(840, 5, 155, 266, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(850, 5, 0, 12, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(851, 5, 0, 30, 100);
            -- 告警抓图，idc和网点场景
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(852, 1, 4, 361, 100);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(853, 2, 52, 361, 999);

            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(854, 1, 2, 362, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(855, 2, 92, 362, 1);

        </sql>
    </changeSet>
</databaseChangeLog>