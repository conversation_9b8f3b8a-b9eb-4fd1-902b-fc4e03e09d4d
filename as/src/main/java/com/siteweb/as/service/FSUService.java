package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.as.entity.IcsFsuFtpDownloadInfo;
import com.siteweb.monitoring.vo.FsuFilterVo;

import java.io.IOException;
import java.util.List;

public interface FSUService {
    List<IdValueDTO<String, Integer>> findFsuTypeStatistics(Integer userId);

    List<IdValueDTO<String, Integer>> findSiteUnitStatistics(Integer userId);

    List<IdValueDTO<String, Integer>> findFsuFlashUsedStatistics(Integer userId);

    Page<IcsFsuDataNewInfoDTO> findFsuPage(Integer userId, Page<IcsFsuDataNewInfoDTO> page, FsuFilterVo filterVo);

    ExcelWriter exportFsu(FsuFilterVo filterVo, Integer userId);

    List<IcsFsuFtpDownloadInfo> findFtpDownLoadFile(String ip);

    boolean fileDirectoryExists(String fileDirectory);

    boolean checkFsuType(String ip, String fsuType);

    boolean resetFsu(String fsuIp, String fileDirectory) throws IOException;

    List<IdValueDTO<String, Integer>> findFsuVersionStatusStatistics(Integer userId);
}
