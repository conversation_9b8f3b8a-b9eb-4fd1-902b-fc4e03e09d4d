package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.DepartmentService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.dto.HistoryEventReportDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.mapper.HistoryEventParserMapper;
import com.siteweb.report.parser.model.HistoryEventReportParam;
import com.siteweb.report.parser.querywrapper.HistoryEventQueryWrapper;
import com.siteweb.report.vo.ReportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.*;

/**
 * 历史告警报表
 * @Author: lzy
 * @Date: 2022/5/23 19:11
 */
@Slf4j
@Component
public class HistoryEventParser extends ReportParser {
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    HistoryEventParserMapper historyEventParserMapper;
    @Autowired
    DepartmentService departmentService;
    @Autowired
    AccountService accountService;

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.serialNo"));
        defaultColumns.put(HistoryEventReportDTO.Fields.eventSeverity, messageSourceUtil.getMessage("common.report.form.eventSeverity"));
        defaultColumns.put(HistoryEventReportDTO.Fields.baseEquipmentName, messageSourceUtil.getMessage("common.report.form.baseEquipmentName"));
        defaultColumns.put(HistoryEventReportDTO.Fields.equipmentName, messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(HistoryEventReportDTO.Fields.equipmentCategoryName, messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(HistoryEventReportDTO.Fields.meanings, messageSourceUtil.getMessage("eventNotification.activeEvent.meanings"));
        defaultColumns.put(HistoryEventReportDTO.Fields.startTime, messageSourceUtil.getMessage("common.report.form.startTime"));
        defaultColumns.put(HistoryEventReportDTO.Fields.endTime, messageSourceUtil.getMessage("common.report.form.endTime"));
        defaultColumns.put(HistoryEventReportDTO.Fields.reversalNum, messageSourceUtil.getMessage("eventNotification.activeEvent.reversalNum"));
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.duration"));
        defaultColumns.put(HistoryEventReportDTO.Fields.equipmentPosition, messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(HistoryEventReportDTO.Fields.confirmTime, messageSourceUtil.getMessage("common.report.form.confirmTime"));
        defaultColumns.put(HistoryEventReportDTO.Fields.description, messageSourceUtil.getMessage("common.report.form.Comment"));
        defaultColumns.put(HistoryEventReportDTO.Fields.confirmerName, messageSourceUtil.getMessage("common.report.form.confirmerName"));
        defaultColumns.put(HistoryEventReportDTO.Fields.eventValue, messageSourceUtil.getMessage("common.report.form.eventValue"));
        defaultColumns.put(HistoryEventReportDTO.Fields.eventName, messageSourceUtil.getMessage("common.report.form.eventName"));
        return defaultColumns;
    }



    @Override
    public JSONObject parser(ReportVO reportVO) {
        HistoryEventReportParam params = new HistoryEventReportParam(reportVO.getReportParameterPresetList());

        JSONObject jsonObject = new JSONObject(true);

        Pageable pageable = reportVO.getPageable();
        List<HistoryEventReportDTO> historyEventReportList = findHistoryEventByPage(reportVO.getUserId(), params, pageable, jsonObject);
        // 设置表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());

        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        JSONArray jsonArray = new JSONArray();
        // 设置内容
        int index = pageable == null ? 1 : 1 + pageable.getPageNumber() * pageable.getPageSize();
        JSONObject tdJsonObject;
        for (HistoryEventReportDTO historyEventReport : historyEventReportList) {
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), index++);
            tdJsonObject.set(HistoryEventReportDTO.Fields.eventSeverity, historyEventReport.getEventSeverity());
            tdJsonObject.set(HistoryEventReportDTO.Fields.equipmentName, historyEventReport.getEquipmentName());
            tdJsonObject.set(HistoryEventReportDTO.Fields.equipmentPosition, historyEventReport.getEquipmentPosition());
            tdJsonObject.set(HistoryEventReportDTO.Fields.baseEquipmentName, historyEventReport.getBaseEquipmentName());
            tdJsonObject.set(HistoryEventReportDTO.Fields.equipmentCategoryName, historyEventReport.getEquipmentCategoryName());
            tdJsonObject.set(HistoryEventReportDTO.Fields.eventName, historyEventReport.getEventName());
            tdJsonObject.set(HistoryEventReportDTO.Fields.eventValue, ReportParamParserUtils.getTwoDecimal(historyEventReport.getEventValue()));
            tdJsonObject.set(HistoryEventReportDTO.Fields.meanings, historyEventReport.getMeanings());
            tdJsonObject.set(HistoryEventReportDTO.Fields.reversalNum, historyEventReport.getReversalNum());
            tdJsonObject.set(HistoryEventReportDTO.Fields.startTime, DateUtil.dateToStringAndValidIsNull(historyEventReport.getStartTime()));
            tdJsonObject.set(HistoryEventReportDTO.Fields.confirmTime, DateUtil.dateToStringAndValidIsNull(historyEventReport.getConfirmTime()));
            tdJsonObject.set(HistoryEventReportDTO.Fields.confirmerName, historyEventReport.getConfirmerName());
            tdJsonObject.set(HistoryEventReportDTO.Fields.endTime, DateUtil.dateToStringAndValidIsNull(historyEventReport.getEndTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), DateUtil.getTimeDifference(historyEventReport.getStartTime(), historyEventReport.getEndTime()));
            tdJsonObject.set(HistoryEventReportDTO.Fields.description, historyEventReport.getDescription());

            jsonArray.add(tdJsonObject);
        }
        jsonObject.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return jsonObject;
    }

    public List<HistoryEventReportDTO> findHistoryEventByPage(Integer userId, HistoryEventReportParam params, Pageable pageable, JSONObject jsonObject) {
        if (userId == null) {
            return Collections.emptyList();
        }
        //部门权限过滤
        if (CollUtil.isEmpty(params.getOperatorIds())) {
            List<Integer> userIds = new ArrayList<>(accountService.findUserIdByPermission(userId));
            params.setOperatorIds(userIds);
        }

        HistoryEventQueryWrapper queryWrapper = getHistoryEventQueryWrapper(userId, params);
        if (ObjectUtil.isNotNull(pageable)) {
            Page<HistoryEventReportDTO> page = new Page<>(pageable.getPageNumber() + 1L, pageable.getPageSize(), false);
            IPage<HistoryEventReportDTO> result = historyEventParserMapper.findHistoryEventByPage(page, queryWrapper);
            result.setTotal(historyEventParserMapper.findHistoryEventCount(queryWrapper));
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), result.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), result.getTotal());
            return result.getRecords();
        }
        return historyEventParserMapper.findHistoryEvent(queryWrapper);
    }

    /**
     * @param userId 用户id
     * @param params 历史告警查询参数
     * @return {@link HistoryEventQueryWrapper}
     */
    private HistoryEventQueryWrapper getHistoryEventQueryWrapper(Integer userId, HistoryEventReportParam params) {
        HistoryEventQueryWrapper queryWrapper = new HistoryEventQueryWrapper();
        // 开始时间
        queryWrapper.setStartTime(DateUtil.dateToString(params.getStartDate()));
        // 结束时间
        queryWrapper.setEndDate(DateUtil.dateToString(params.getEndDate()));
        // 设备基类id
        queryWrapper.setBaseEquipmentId(params.getBaseEquipmentId());
        // 设备类型ids
        queryWrapper.setEquipmentCategories(params.getEquipmentCategories());
        // 告警等级
        queryWrapper.setEventLevelList(params.getEventLevelList());
        // 事件名
        queryWrapper.setEventName(params.getEventName());
        // 关键字
        queryWrapper.setKeyword(params.getKeyword());
        // 确认人
        queryWrapper.setOperatorIds(params.getOperatorIds());
        // 注释
        queryWrapper.setDescription(params.getDescription());
        Map<Integer, Set<Integer>> frontEndEventIds = params.getEventIds();
        // 前端传入了事件,则根据事件查询
        if (CollUtil.isNotEmpty(frontEndEventIds)) {
            queryWrapper.getSql().add("and (" + ReportParamParserUtils.buildEventSql(frontEndEventIds) + ")");
        }
        // 选择设备则，设备查询
        List<Integer> frontEndEquipmentIds = params.getEquipmentIds();
        if (CollUtil.isNotEmpty(frontEndEquipmentIds)) {
            queryWrapper.getSql().add(String.format("and a.EquipmentId in (%s)", CollUtil.join(frontEndEquipmentIds, ",")));
        }
        //设备权限统一过滤
        Set<Integer> allEquipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isNotEmpty(allEquipmentIds)) {
            queryWrapper.getSql().add(String.format("and a.EquipmentId in (%s)", CollUtil.join(allEquipmentIds, ",")));
        }
        return queryWrapper;
    }

    @Override
    public long countSample(ReportVO reportVO) {
        HistoryEventReportParam params = new HistoryEventReportParam(reportVO.getReportParameterPresetList());
        HistoryEventQueryWrapper queryWrapper = getHistoryEventQueryWrapper(reportVO.getUserId(), params);
        return historyEventParserMapper.findHistoryEventCount(queryWrapper);
    }

    @Override
    public long export(ReportVO reportVO, String filePath) {
        try (CsvWriter writer = CsvUtil.getWriter(Paths.get(filePath).toUri().getPath(), StandardCharsets.UTF_8, false)) {
            HistoryEventReportParam params = new HistoryEventReportParam(reportVO.getReportParameterPresetList());
            HistoryEventQueryWrapper queryWrapper = getHistoryEventQueryWrapper(reportVO.getUserId(), params);
            List<HistoryEventReportDTO> historyEvent = historyEventParserMapper.findHistoryEvent(queryWrapper);
            writer.writeHeaderLine(messageSourceUtil.getMessage("common.report.form.eventSeverity"), messageSourceUtil.getMessage("common.report.form.equipmentName"), messageSourceUtil.getMessage("common.report.form.equipmentPosition"),
                    messageSourceUtil.getMessage("common.report.form.baseEquipmentName"), messageSourceUtil.getMessage("common.report.form.equipmentType"),messageSourceUtil.getMessage("common.report.form.eventName"),messageSourceUtil.getMessage("common.report.form.eventValue"),messageSourceUtil.getMessage("eventNotification.activeEvent.meanings"),
                    messageSourceUtil.getMessage("common.report.form.startTime"),messageSourceUtil.getMessage("common.report.form.confirmTime"),messageSourceUtil.getMessage("common.report.form.confirmerName"),
                    messageSourceUtil.getMessage("common.report.form.endTime"),messageSourceUtil.getMessage("common.report.form.endPerson"),messageSourceUtil.getMessage("common.report.form.duration"),messageSourceUtil.getMessage("common.report.form.eventRemarks"));
            writer.write(historyEvent);
            return historyEvent.size();
        }
    }

    private HistoryEventParser() {
        super(ReportDataSourceEnum.HISTORY_EVENTS.getValue());
    }

}
