package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> z<PERSON>
 * @description ConfigChangeMacroLog
 * @createTime 2022-04-12 14:12:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_configchangemacrolog")
public class ConfigChangeMacroLog {

    private String objectId;

    private Integer configId;

    private Integer editType;

    private Date updateTime;
}
