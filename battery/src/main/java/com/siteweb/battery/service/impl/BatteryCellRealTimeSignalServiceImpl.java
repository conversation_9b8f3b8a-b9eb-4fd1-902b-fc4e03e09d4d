package com.siteweb.battery.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.battery.entity.BatteryCellModel;
import com.siteweb.battery.entity.BatteryCellRealTimeSignal;
import com.siteweb.battery.entity.BatteryCellRealTimeStatistic;
import com.siteweb.battery.entity.BatteryString;
import com.siteweb.battery.enums.BatteryKeyEnum;
import com.siteweb.battery.manager.BatteryConfigManager;
import com.siteweb.battery.service.BatteryCellModelService;
import com.siteweb.battery.service.BatteryCellRealTimeSignalService;
import com.siteweb.battery.service.BatteryStringService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.EquipmentTemplate;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.service.EquipmentTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @author: Habits
 * @time: 2022/5/27 10:32
 * @description:
 **/
@Service
@Slf4j
public class BatteryCellRealTimeSignalServiceImpl implements BatteryCellRealTimeSignalService {

    private Map<Integer, List<BatteryCellRealTimeSignal>> equipmentBatteryCellLivePoints = new ConcurrentHashMap<>();

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    BatteryCellModelService batteryCellModelService;

    @Autowired
    BatteryConfigManager batteryConfigManager;

    @Autowired
    ActiveSignalManager activeSignalManager;

    @Autowired
    BatteryStringService batteryStringService;

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    EquipmentTemplateService equipmentTemplateService;


    @PostConstruct  // 使用Spring的@PostConstruct注解确保在bean初始化完成后自动执行
    public void initAllEquipmentBatteryCellRealTimeSignals() {
        log.info("开始初始化所有设备的电池单元实时信号...");

        // 查询所有的电池串
        List<BatteryString> allBatteryStrings = batteryStringService.findBatteryStrings();
        if (CollUtil.isEmpty(allBatteryStrings)) {
            log.warn("未找到电池串数据，初始化终止");
            return;
        }

        // 遍历所有电池串进行初始化
        for (BatteryString batteryString : allBatteryStrings) {
            initEquipmentBatteryCellRealTimeSignalInternal(batteryString);
        }

        log.info("所有设备的电池单元实时信号初始化完成，共初始化{}个设备", equipmentBatteryCellLivePoints.size());
    }

    /**
     * 内部方法，用于初始化单个电池串的实时信号
     */
    private void initEquipmentBatteryCellRealTimeSignalInternal(BatteryString batteryString) {
        List<BatteryCellRealTimeSignal> batteryCellRealTimeSignals = new ArrayList<>();
        if (ObjectUtil.isNull(batteryString) || batteryString.getCellCount() == null) {
            log.warn("电池串数据无效或节数为空，设备ID: {}", batteryString != null ? batteryString.getEquipmentId() : "未知");
            return;
        }

        BatteryCellModel batteryCellModel = batteryCellModelService.findBatteryCellModelById(batteryString.getBatteryCellModelId());
        if (ObjectUtil.isNull(batteryCellModel)) {
            log.warn("未找到电池单元模型，电池单元模型ID: {}, 设备ID: {}",
                    batteryString.getBatteryCellModelId(), batteryString.getEquipmentId());
            return;
        }

        Optional<EquipmentTemplate> eqOptional = equipmentTemplateService.findByEquipmentId(batteryString.getEquipmentId());
        Integer equipmentBaseType = eqOptional.map(EquipmentTemplate::getEquipmentBaseType).orElse(null);
        String cellName = messageSourceUtil.getMessage("battery.cell.signal");

        for (int i = 1; i <= batteryString.getCellCount(); i++) {
            BatteryCellRealTimeSignal batteryCellRealTimeSignal = new BatteryCellRealTimeSignal();
            batteryCellRealTimeSignal.setBatteryStringId(batteryString.getBatteryStringId());
            batteryCellRealTimeSignal.setCellId(i);
            batteryCellRealTimeSignal.setCellName(String.format(cellName, i));
            batteryCellRealTimeSignal.setCellTemperatureBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_TEMPERATURE_KEY.getName(), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellTempLowAlarmBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_TEMPERATURELOW_KEY.getName(), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellTempHighAlarmBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_TEMPERATUREHIGH_KEY.getName(), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellVoltageBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(getVoltagebaseType(batteryCellModel.getVoltageType()), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellVoltageAlarmLowBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(getVoltageAlarmBaseType(batteryCellModel.getVoltageType()).get(0), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellVoltageAlarmHighBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(getVoltageAlarmBaseType(batteryCellModel.getVoltageType()).get(1), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellIRBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_IR_KEY.getName(), equipmentBaseType)));
            batteryCellRealTimeSignal.setCellIRAlarmBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_IR_KEY.getName(), equipmentBaseType)));
            batteryCellRealTimeSignals.add(batteryCellRealTimeSignal);
        }

        log.debug("电池串初始化成功,设备ID:{},电池节数:{}", batteryString.getEquipmentId(), batteryCellRealTimeSignals.size());
        equipmentBatteryCellLivePoints.put(batteryString.getEquipmentId(), batteryCellRealTimeSignals);
    }

    @Override
    public List<BatteryCellRealTimeSignal> createBatteryCellRealTimeSignal(BatteryString batteryString, List<SimpleActiveSignal> activeSignals) {
        List<BatteryCellRealTimeSignal> batteryCellRealTimeSignals = new ArrayList<>();
        if (ObjectUtil.isNull(batteryString) && CollUtil.isEmpty(activeSignals)) {
            return batteryCellRealTimeSignals;
        }

        batteryCellRealTimeSignals = equipmentBatteryCellLivePoints.get(batteryString.getEquipmentId());

        if (CollUtil.isEmpty(batteryCellRealTimeSignals)) {
            log.trace("未获取到对应的equipmentBatteryCellLivePoints 设备ID{}", batteryString.getEquipmentId());
            return batteryCellRealTimeSignals;
        }
        // 遍历赋值，当前值，状态
        // 打印条数
//        log.trace("获取电池节数,设备ID:{},内存中电池节数:{}, 实际数据库中电池节数{}", batteryString.getEquipmentId(), batteryCellRealTimeSignals.size(), batteryString.getCellCount());
        // 将List<SimpleActiveSignal>转换为Map<Long, SimpleActiveSignal>
        // key为baseTypeId
        // Filter out null baseTypeIds
        Map<Long, List<SimpleActiveSignal>> activeSignalsMap = activeSignals.stream()
                .filter(signal -> signal.getBaseTypeId() != null)
                .collect(Collectors.groupingBy(SimpleActiveSignal::getBaseTypeId));
        for (BatteryCellRealTimeSignal batteryCellRealTimeSignal : batteryCellRealTimeSignals) {
            // 电压 (Voltage)
            Long cellVoltageBaseTypeId = batteryCellRealTimeSignal.getCellVoltageBaseTypeId();
            List<SimpleActiveSignal> voltageActiveSignal = activeSignalsMap.get(cellVoltageBaseTypeId);
            if (CollUtil.isNotEmpty(voltageActiveSignal)) {
                batteryCellRealTimeSignal.setCellVoltageCurrentValue(voltageActiveSignal.get(0).getCurrentValue());
                batteryCellRealTimeSignal.setCellVoltageValue(getCellValue(voltageActiveSignal.get(0)));
            } else {
                log.warn("电压信号未找到 - 设备ID: {}, BaseTypeID: {}, 电池节数: {}, 电池节数名称: {}",
                        batteryString.getEquipmentId(),
                        cellVoltageBaseTypeId,
                        batteryCellRealTimeSignal.getCellId(),
                        batteryCellRealTimeSignal.getCellName());
            }

            // 温度 (Temperature)
            Long cellTemperatureBaseTypeId = batteryCellRealTimeSignal.getCellTemperatureBaseTypeId();
            List<SimpleActiveSignal> temperatureActiveSignal = activeSignalsMap.get(cellTemperatureBaseTypeId);
            if (CollUtil.isNotEmpty(temperatureActiveSignal)) {
                batteryCellRealTimeSignal.setCellTemperatureCurrentValue(temperatureActiveSignal.get(0).getCurrentValue());
                batteryCellRealTimeSignal.setCellTemperatureValue(getCellValue(temperatureActiveSignal.get(0)));
            } else {
                log.warn("温度信号未找到 - 设备ID: {}, BaseTypeID: {}, 电池节数: {}, 电池节数名称: {}",
                        batteryString.getEquipmentId(),
                        cellVoltageBaseTypeId,
                        batteryCellRealTimeSignal.getCellId(),
                        batteryCellRealTimeSignal.getCellName());
            }

            // 内阻 (Internal Resistance)
            Long cellIRBaseTypeId = batteryCellRealTimeSignal.getCellIRBaseTypeId();
            List<SimpleActiveSignal> irActiveSignal = activeSignalsMap.get(cellIRBaseTypeId);
            if (CollUtil.isNotEmpty(irActiveSignal)) {
                batteryCellRealTimeSignal.setCellIRCurrentValue(irActiveSignal.get(0).getCurrentValue());
                batteryCellRealTimeSignal.setCellIRValue(getCellValue(irActiveSignal.get(0)));
            } else {
                log.warn("内阻信号未找到 - 设备ID: {}, BaseTypeID: {}, 电池节数: {}, 电池节数名称: {}",
                        batteryString.getEquipmentId(),
                        cellVoltageBaseTypeId,
                        batteryCellRealTimeSignal.getCellId(),
                        batteryCellRealTimeSignal.getCellName());
            }

            // 电压状态 (Voltage Status)
            if (CollUtil.isNotEmpty(voltageActiveSignal)) {
                batteryCellRealTimeSignal.setCellVoltageEventLevel(voltageActiveSignal.get(0).getCurrentState());
            }

            // 温度状态 (Temperature Status)
            if (CollUtil.isNotEmpty(temperatureActiveSignal)) {
                batteryCellRealTimeSignal.setCellTemperatureEventLevel(temperatureActiveSignal.get(0).getCurrentState());
            }

            // 内阻状态 (Internal Resistance Status)
            if (CollUtil.isNotEmpty(irActiveSignal)) {
                batteryCellRealTimeSignal.setCellIREventLevel(irActiveSignal.get(0).getCurrentState());
            }
        }
        return batteryCellRealTimeSignals;
    }

    /**
     * 根据 baseTypeId 过滤 SimpleActiveSignal 列表
     *
     * @param signals 原始信号列表
     * @param baseTypeId 需要过滤的 baseTypeId
     * @return 过滤后的信号列表
     */
    public List<SimpleActiveSignal> filterSignalsByBaseTypeId(List<SimpleActiveSignal> signals, Long baseTypeId) {
        if (signals == null || signals.isEmpty() || baseTypeId == null) {
            return new ArrayList<>();
        }

        return signals.stream()
                .filter(signal -> signal.getBaseTypeId() != null && baseTypeId.equals(signal.getBaseTypeId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据多个 baseTypeId 过滤 SimpleActiveSignal 列表
     *
     * @param signals 原始信号列表
     * @param baseTypeIds 需要过滤的 baseTypeId 集合
     * @return 过滤后的信号列表
     */
    public List<SimpleActiveSignal> filterSignalsByBaseTypeIds(List<SimpleActiveSignal> signals, List<Long> baseTypeIds) {
        if (signals == null || signals.isEmpty() || baseTypeIds == null || baseTypeIds.isEmpty()) {
            return new ArrayList<>();
        }

        return signals.stream()
                .filter(signal -> signal.getBaseTypeId() != null && baseTypeIds.contains(signal.getBaseTypeId()))
                .collect(Collectors.toList());
    }

    public Integer getCurrentState(Integer eqId, Long baseTypeId) {
        List<Long> baseTypeIds = new ArrayList<>();
        baseTypeIds.add(baseTypeId);
        List<SimpleActiveSignal> simpleActiveSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(eqId, baseTypeIds);
        if (CollUtil.isNotEmpty(simpleActiveSignals)) {
            return simpleActiveSignals.get(0).getCurrentState();
        }
        return 0;
    }

    @Override
    public List<BatteryCellRealTimeSignal> createBatteryCellRealTimeSignal(Integer equipmentId) {
        BatteryString batteryString = batteryStringService.findBatteryStringByEqId(equipmentId);
        List<SimpleActiveSignal> simpleActiveSignals = activeSignalManager.getActiveSignalsByEquipmentId(equipmentId);
        log.trace("获取设备实时数据,设备ID:{}, 信号条数：{}", equipmentId, simpleActiveSignals.size());
        return createBatteryCellRealTimeSignal(batteryString, simpleActiveSignals);
    }


    private Integer getMultiPointAlarmStatus(Integer equipmentId, Long alarmLowBaseTypeId, Long alarmHighBaseTypeId) {
        int eventLevel = 0;
        List<ActiveEvent> simpleActiveEventLow = new ArrayList<>();
        List<ActiveEvent> simpleActiveEventHigh = new ArrayList<>();
        List<ActiveEvent> activeEvents = activeEventManager.getActiveEventsByEquipmentId(equipmentId);
        simpleActiveEventLow = activeEvents.stream().filter(o -> alarmLowBaseTypeId.equals(o.getBaseTypeId())).toList();
        simpleActiveEventHigh = activeEvents.stream().filter(o -> alarmHighBaseTypeId.equals(o.getBaseTypeId())).toList();
        if (CollUtil.isNotEmpty(simpleActiveEventLow) && CollUtil.isEmpty(simpleActiveEventHigh)) {
            eventLevel = simpleActiveEventLow.stream()
                    .mapToInt(ActiveEvent::getEventLevel)
                    .max()
                    .orElse(0);
        }
        if (CollUtil.isNotEmpty(simpleActiveEventHigh) && CollUtil.isEmpty(simpleActiveEventLow)) {
            eventLevel = simpleActiveEventHigh.stream()
                    .mapToInt(ActiveEvent::getEventLevel)
                    .max()
                    .orElse(0);
        }
        if (CollUtil.isNotEmpty(simpleActiveEventLow) && CollUtil.isNotEmpty(simpleActiveEventHigh)) {
            int maxLowEventLevel = simpleActiveEventLow.stream()
                    .mapToInt(ActiveEvent::getEventLevel)
                    .max()
                    .orElse(0);
            int maxHighEventLevel = simpleActiveEventHigh.stream()
                    .mapToInt(ActiveEvent::getEventLevel)
                    .max()
                    .orElse(0);
            eventLevel = Math.min(maxLowEventLevel, maxHighEventLevel);
        }
        return eventLevel;

    }

    private Integer getPointAlarmStatus(Integer equipmentId, Long BaseTypeId) {
        List<Long> baseTypeIds = new ArrayList<Long>();
        baseTypeIds.add(BaseTypeId);
        List<SimpleActiveSignal> simpleActiveSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds);
        int eventLevel = 0;
        if (CollUtil.isNotEmpty(simpleActiveSignals) && simpleActiveSignals.get(0).getCurrentState() != null) {
            eventLevel = simpleActiveSignals.get(0).getCurrentState();
        }
        return eventLevel;
    }


    /**
     * 根据设备id和信号基类查询出OriginalValue
     *
     * @return
     */
    private Double getCellValue(SimpleActiveSignal simpleActiveSignal) {
        if (StrUtil.isNotBlank(simpleActiveSignal.getCurrentValue())) {
            String unit = simpleActiveSignal.getUnit();
            String valueWithoutUnit;
            if (StrUtil.isEmpty(unit)) {
                valueWithoutUnit = simpleActiveSignal.getCurrentValue();
            } else {
                valueWithoutUnit = simpleActiveSignal.getCurrentValue().replace(unit, "").trim();
            }
            // log.trace("baseTypeId:{},equipmentId:{},valueWithoutUnit:{}", simpleActiveSignal.getBaseTypeId(), simpleActiveSignal.getEquipmentId(), valueWithoutUnit);
            if (NumberUtil.isNumber(valueWithoutUnit)) {
                return Double.valueOf(valueWithoutUnit);
            }
        } else {
            log.trace("simpleActiveSignals is empty, baseTypeId:{},simpleActiveSignals:{}", simpleActiveSignal.getBaseTypeId(), simpleActiveSignal);
        }
        return null;
    }

    /**
     * 根据设备id和信号基类查询出CurrentValue
     *
     * @param baseTypeId
     * @param equipmentId
     * @return
     */
    private SimpleActiveSignal getCellCurrentSimpleActiveSignal(Long baseTypeId, Integer equipmentId) {
        List<Long> baseTypeIds = new ArrayList<>();
        baseTypeIds.add(baseTypeId);
        List<SimpleActiveSignal> simpleActiveSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds);
        if (CollUtil.isNotEmpty(simpleActiveSignals)) {
//            log.trace("baseTypeId:{},equipmentId:{},currentValue:{}", baseTypeId, equipmentId, simpleActiveSignals.get(0).getCurrentValue());
            return simpleActiveSignals.get(0);
        } else {
            log.trace("simpleActiveSignals is empty, baseTypeId:{},equipmentId:{},simpleActiveSignals:{}", baseTypeId, equipmentId, simpleActiveSignals);
        }
        return null;
    }

    private Integer getEventLevel(Long baseTypeId, Integer equipmentId) {
        List<ActiveEvent> activeEvents = activeEventManager.getActiveEventsByEquipmentId(equipmentId);
        int eventLevel = 0;
        activeEvents = activeEvents.stream().filter(o -> baseTypeId.equals(o.getBaseTypeId())).toList();
        if (!activeEvents.isEmpty()) {
            eventLevel = activeEvents.get(0).getEventLevel();
        }
        return eventLevel;
    }

    /**
     * 保留原方法作为手动初始化单个电池串的入口
     * 可以在需要时重新初始化或更新单个电池串
     */
    public void initEquipmentBatteryCellRealTimeSignal(BatteryString batteryString) {
        initEquipmentBatteryCellRealTimeSignalInternal(batteryString);
    }


    /**
     * 初始化BatteryCellRealTimeSignal
     *
     * @param batteryString
     */
//    public void initEquipmentBatteryCellRealTimeSignal(BatteryString batteryString) {
//        List<BatteryCellRealTimeSignal> batteryCellRealTimeSignals = new ArrayList<>();
//        if (ObjectUtil.isNull(batteryString) || batteryString.getCellCount() == null) {
//            return;
//        }
//        BatteryCellModel batteryCellModel = batteryCellModelService.findBatteryCellModelById(batteryString.getBatteryCellModelId());
//        if (ObjectUtil.isNull(batteryCellModel)) {
//            return;
//        }
//        Optional<EquipmentTemplate> eqOptional = equipmentTemplateService.findByEquipmentId(batteryString.getEquipmentId());
//        Integer equipmentBaseType = eqOptional.map(EquipmentTemplate::getEquipmentBaseType).orElse(null);
//        String cellName = messageSourceUtil.getMessage("battery.cell.signal");
//        for (int i = 1; i <= batteryString.getCellCount(); i++) {
//            BatteryCellRealTimeSignal batteryCellRealTimeSignal = new BatteryCellRealTimeSignal();
//            batteryCellRealTimeSignal.setEquipmentId(batteryString.getEquipmentId());
//            batteryCellRealTimeSignal.setBatteryStringId(batteryString.getBatteryStringId());
//            batteryCellRealTimeSignal.setCellId(i);
//            batteryCellRealTimeSignal.setCellName(String.format(cellName, i));
//            batteryCellRealTimeSignal.setCellTemperatureBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_TEMPERATURE_KEY.getName(), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellTempLowAlarmBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_TEMPERATURELOW_KEY.getName(), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellTempHighAlarmBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_TEMPERATUREHIGH_KEY.getName(), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellVoltageBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(getVoltagebaseType(batteryCellModel.getVoltageType()), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellVoltageAlarmLowBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(getVoltageAlarmBaseType(batteryCellModel.getVoltageType()).get(0), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellVoltageAlarmHighBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(getVoltageAlarmBaseType(batteryCellModel.getVoltageType()).get(1), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellIRBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_IR_KEY.getName(), equipmentBaseType)));
//            batteryCellRealTimeSignal.setCellIRAlarmBaseTypeId(reSetBaseType(i, batteryConfigManager.getSystemConfigValueByKey(BatteryKeyEnum.BATTERYCELL_IR_KEY.getName(), equipmentBaseType)));
//
//            List<Long> temperatureBaseTypeIds = new ArrayList<>();
//            temperatureBaseTypeIds.add(batteryCellRealTimeSignal.getCellTemperatureBaseTypeId());
//            List<ConfigSignalItem> templateSignalId = configSignalManager.getConfigSignalByEquipmentIdAndBaseTypeId(batteryString.getEquipmentId(), temperatureBaseTypeIds);
//            if (CollUtil.isNotEmpty(templateSignalId)) {
//                batteryCellRealTimeSignal.setCellTemperatureSignalId(templateSignalId.get(0).getSignalId());
//            }
//
//            List<Long> voltageBaseTypeIds = new ArrayList<>();
//            voltageBaseTypeIds.add(batteryCellRealTimeSignal.getCellVoltageBaseTypeId());
//            List<ConfigSignalItem> voltageSignalId = configSignalManager.getConfigSignalByEquipmentIdAndBaseTypeId(batteryString.getEquipmentId(), voltageBaseTypeIds);
//            if (CollUtil.isNotEmpty(voltageSignalId)) {
//                batteryCellRealTimeSignal.setCellVoltageSignalId(voltageSignalId.get(0).getSignalId());
//            }
//
//
//            List<Long> irBaseTypeIds = new ArrayList<>();
//            irBaseTypeIds.add(batteryCellRealTimeSignal.getCellIRBaseTypeId());
//            List<ConfigSignalItem> irSignalId = configSignalManager.getConfigSignalByEquipmentIdAndBaseTypeId(batteryString.getEquipmentId(), irBaseTypeIds);
//            if (CollUtil.isNotEmpty(irSignalId)) {
//                batteryCellRealTimeSignal.setCellIRSignalId(irSignalId.get(0).getSignalId());
//            }
//
//            List<Long> socBaseTypeIds = new ArrayList<>();
//            socBaseTypeIds.add(batteryCellRealTimeSignal.getCellSocBaseTypeId());
//            List<ConfigSignalItem> socSignalId = configSignalManager.getConfigSignalByEquipmentIdAndBaseTypeId(batteryString.getEquipmentId(), socBaseTypeIds);
//            if (CollUtil.isNotEmpty(socSignalId)) {
//                batteryCellRealTimeSignal.setCellSocSignalId(socSignalId.get(0).getSignalId());
//            }
//
//
//
//
//            batteryCellRealTimeSignals.add(batteryCellRealTimeSignal);
//        }
//        log.trace("电池节数初始化成功,设备ID:{},电池节数:{}", batteryString.getEquipmentId(), batteryCellRealTimeSignals.size());
//        equipmentBatteryCellLivePoints.put(batteryString.getEquipmentId(), batteryCellRealTimeSignals);
//    }

    @Override
    public BatteryCellRealTimeStatistic findBatteryCellRealTimeStatisticsByEqId(Integer equipmentId) {
        BatteryCellRealTimeStatistic batteryCellRealTimeStatistic = new BatteryCellRealTimeStatistic();
        if (null == equipmentId) {
            return batteryCellRealTimeStatistic;
        }
        BatteryString batteryString = batteryStringService.findBatteryStringByEqId(equipmentId);
        List<SimpleActiveSignal> simpleActiveSignals =  activeSignalManager.getActiveSignalsByEquipmentId(equipmentId);
        List<BatteryCellRealTimeSignal> batteryCellRealTimeSignals = createBatteryCellRealTimeSignal(batteryString, simpleActiveSignals);
        setBatteryRealTimeStatistic(batteryCellRealTimeSignals, batteryCellRealTimeStatistic);
        return batteryCellRealTimeStatistic;
    }

    private void setBatteryRealTimeStatistic(List<BatteryCellRealTimeSignal> batteryCellRealTimeSignals, BatteryCellRealTimeStatistic batteryCellRealTimeStatistic) {
        if (CollUtil.isEmpty(batteryCellRealTimeSignals)) {
            return;
        }
        batteryCellRealTimeStatistic.setBatteryStringId(batteryCellRealTimeSignals.get(0).getBatteryStringId());
        batteryCellRealTimeStatistic.setCellCount(batteryCellRealTimeSignals.size());

        BatteryCellRealTimeSignal maxIRObject = null;
        Optional<BatteryCellRealTimeSignal> maxIRValue = batteryCellRealTimeSignals.stream()
                .filter(signal -> signal.getCellIRValue() != null)
                .max(Comparator.comparing(BatteryCellRealTimeSignal::getCellIRValue));
        if (maxIRValue.isPresent()) {
            maxIRObject = maxIRValue.get();
        }
        if (maxIRObject != null && maxIRObject.getCellIRValue() != null) {
            batteryCellRealTimeStatistic.setMaxIR(maxIRObject.getCellIRValue());
            batteryCellRealTimeStatistic.setMaxIRCurrentValue(maxIRObject.getCellIRCurrentValue());
            batteryCellRealTimeStatistic.setMaxIRCellId(maxIRObject.getCellId());
            String cellValue = maxIRObject.getCellIRValue().toString();
            String cellId = batteryCellRealTimeSignals.stream()
                    .filter(oc -> oc.getCellIRValue() != null && cellValue.equals(oc.getCellIRValue().toString()))
                    .map(p -> String.valueOf(p.getCellId()))
                    .collect(Collectors.joining(","));
            batteryCellRealTimeStatistic.setMaxIRCellIds(cellId);
        }

        BatteryCellRealTimeSignal minIRObject = null;
        Optional<BatteryCellRealTimeSignal> minIRValue = batteryCellRealTimeSignals.stream()
                .filter(signal -> signal.getCellIRValue() != null)
                .min(Comparator.comparing(BatteryCellRealTimeSignal::getCellIRValue));
        if (minIRValue.isPresent()) {
            minIRObject = minIRValue.get();
        }
        if (minIRObject != null && minIRObject.getCellIRValue() != null) {
            batteryCellRealTimeStatistic.setMinIR(minIRObject.getCellIRValue());
            batteryCellRealTimeStatistic.setMinIRCurrentValue(minIRObject.getCellIRCurrentValue());
            batteryCellRealTimeStatistic.setMinIRCellId(minIRObject.getCellId());
            String cellValue = minIRObject.getCellIRValue().toString();
            String cellId = batteryCellRealTimeSignals.stream()
                    .filter(oc -> oc.getCellIRValue() != null && cellValue.equals(oc.getCellIRValue().toString()))
                    .map(p -> String.valueOf(p.getCellId()))
                    .collect(Collectors.joining(","));
            batteryCellRealTimeStatistic.setMinIRCellIds(cellId);
        }

        BatteryCellRealTimeSignal maxVoltageObject = null;
        Optional<BatteryCellRealTimeSignal> maxVoltageValue = batteryCellRealTimeSignals.stream()
                .filter(signal -> signal.getCellVoltageValue() != null)
                .max(Comparator.comparing(BatteryCellRealTimeSignal::getCellVoltageValue));
        if (maxVoltageValue.isPresent()) {
            maxVoltageObject = maxVoltageValue.get();
        }
        if (maxVoltageObject != null && maxVoltageObject.getCellVoltageValue() != null) {
            batteryCellRealTimeStatistic.setMaxVoltage(maxVoltageObject.getCellVoltageValue());
            batteryCellRealTimeStatistic.setMaxVoltageCurrentValue(maxVoltageObject.getCellVoltageCurrentValue());
            batteryCellRealTimeStatistic.setMaxVoltageCellId(maxVoltageObject.getCellId());
            String cellValue = maxVoltageObject.getCellVoltageValue().toString();
            String cellId = batteryCellRealTimeSignals.stream()
                    .filter(oc -> oc.getCellVoltageValue() != null && cellValue.equals(oc.getCellVoltageValue().toString()))
                    .map(p -> String.valueOf(p.getCellId()))
                    .collect(Collectors.joining(","));
            batteryCellRealTimeStatistic.setMaxVoltageCellIds(cellId);
        }

        BatteryCellRealTimeSignal minVoltageObject = null;
        Optional<BatteryCellRealTimeSignal> minVoltageValue = batteryCellRealTimeSignals.stream()
                .filter(signal -> signal.getCellVoltageValue() != null)
                .min(Comparator.comparing(BatteryCellRealTimeSignal::getCellVoltageValue));
        if (minVoltageValue.isPresent()) {
            minVoltageObject = minVoltageValue.get();
        }
        if (minVoltageObject != null && minVoltageObject.getCellVoltageValue() != null) {
            batteryCellRealTimeStatistic.setMinVoltage(minVoltageObject.getCellVoltageValue());
            batteryCellRealTimeStatistic.setMinVoltageCurrentValue(minVoltageObject.getCellVoltageCurrentValue());
            batteryCellRealTimeStatistic.setMinVoltageCellId(minVoltageObject.getCellId());
            String cellValue = minVoltageObject.getCellVoltageValue().toString();
            String cellId = batteryCellRealTimeSignals.stream()
                    .filter(oc -> oc.getCellVoltageValue() != null && cellValue.equals(oc.getCellVoltageValue().toString()))
                    .map(p -> String.valueOf(p.getCellId()))
                    .collect(Collectors.joining(","));
            batteryCellRealTimeStatistic.setMinVoltageCellIds(cellId);
        }

        BatteryCellRealTimeSignal maxTemperatureObject = null;
        Optional<BatteryCellRealTimeSignal> maxTemperatureValue = batteryCellRealTimeSignals.stream()
                .filter(signal -> signal.getCellTemperatureValue() != null)
                .max(Comparator.comparing(BatteryCellRealTimeSignal::getCellTemperatureValue));
        if (maxTemperatureValue.isPresent()) {
            maxTemperatureObject = maxTemperatureValue.get();
        }
        if (maxTemperatureObject != null && maxTemperatureObject.getCellTemperatureValue() != null) {
            batteryCellRealTimeStatistic.setMaxTemp(maxTemperatureObject.getCellTemperatureValue());
            batteryCellRealTimeStatistic.setMaxTempCurrentValue(maxTemperatureObject.getCellTemperatureCurrentValue());
            batteryCellRealTimeStatistic.setMaxTempCellId(maxTemperatureObject.getCellId());
            String cellValue = maxTemperatureObject.getCellTemperatureValue().toString();
            String cellId = batteryCellRealTimeSignals.stream()
                    .filter(oc -> oc.getCellTemperatureValue() != null && cellValue.equals(oc.getCellTemperatureValue().toString()))
                    .map(p -> String.valueOf(p.getCellId()))
                    .collect(Collectors.joining(","));
            batteryCellRealTimeStatistic.setMaxTempCellIds(cellId);
        }
        BatteryCellRealTimeSignal minTemperatureObject = null;
        Optional<BatteryCellRealTimeSignal> minTemperatureValue = batteryCellRealTimeSignals.stream()
                .filter(signal -> signal.getCellTemperatureValue() != null)
                .min(Comparator.comparing(BatteryCellRealTimeSignal::getCellTemperatureValue));
        if (minTemperatureValue.isPresent()) {
            minTemperatureObject = minTemperatureValue.get();
        }
        if (minTemperatureObject != null && minTemperatureObject.getCellTemperatureValue() != null) {
            batteryCellRealTimeStatistic.setMinTemp(minTemperatureObject.getCellTemperatureValue());
            batteryCellRealTimeStatistic.setMinTempCurrentValue(minTemperatureObject.getCellTemperatureCurrentValue());
            batteryCellRealTimeStatistic.setMinTempCellId(minTemperatureObject.getCellId());
            String cellValue = minTemperatureObject.getCellTemperatureValue().toString();
            String cellId = batteryCellRealTimeSignals.stream()
                    .filter(oc -> oc.getCellTemperatureValue() != null && cellValue.equals(oc.getCellTemperatureValue().toString()))
                    .map(p -> String.valueOf(p.getCellId()))
                    .collect(Collectors.joining(","));
            batteryCellRealTimeStatistic.setMinTempCellIds(cellId);
        }
    }


    /**
     * 转换基类ID
     *
     * @param i
     * @param baseType
     * @return
     */
    private Long reSetBaseType(int i, String baseType) {
        if (StrUtil.isBlank(baseType)) {
            return null;
        }
        return Long.valueOf(baseType) * 1000 + i;
    }

    /**
     * 根据不同的电压类型获取key
     *
     * @return
     */
    private String getVoltagebaseType(Integer voltageType) {
        String result = "";
        switch (voltageType) {
            case 1:
                result = BatteryKeyEnum.BATTERYCELL_VOLTAGE2V_KEY.getName();
                break;
            case 2:
                result = BatteryKeyEnum.BATTERYCELL_VOLTAGE6V_KEY.getName();
                break;
            case 3:
                result = BatteryKeyEnum.BATTERYCELL_VOLTAGE12V_KEY.getName();
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 根据不同的电压类型获取key
     *
     * @param voltageType
     * @return
     */
    private List<String> getVoltageAlarmBaseType(Integer voltageType) {
        List<String> result = new ArrayList<>();
        switch (voltageType) {
            case 1:
                result.add(BatteryKeyEnum.BATTERYCELL_VOLTAGELOW2V_KEY.getName());
                result.add(BatteryKeyEnum.BATTERYCELL_VOLTAGEHIGH2V_KEY.getName());
                break;
            case 2:
                result.add(BatteryKeyEnum.BATTERYCELL_VOLTAGELOW6V_KEY.getName());
                result.add(BatteryKeyEnum.BATTERYCELL_VOLTAGEHIGH6V_KEY.getName());
                break;
            case 3:
                result.add(BatteryKeyEnum.BATTERYCELL_VOLTAGELOW12V_KEY.getName());
                result.add(BatteryKeyEnum.BATTERYCELL_VOLTAGEHIGH12V_KEY.getName());
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 从缓存中获取 BatteryCellRealTimeSignal
     *
     * @return
     */
    public List<BatteryCellRealTimeSignal> findBatteryCellRealTimeSignalByEqId(Integer equipmentId) {
        return equipmentBatteryCellLivePoints.get(equipmentId);
    }


}
