package com.siteweb.admin.service.impl;


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.entity.AccountPasswordErrRecord;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.mapper.AccountPasswordErrRecordMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.AccountPasswordErrRecordService;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Map;

@Service
public class AccountPasswordErrRecordServiceImpl implements AccountPasswordErrRecordService {

    @Autowired
    AccountPasswordErrRecordMapper accountPasswordErrRecordMapper;

    @Autowired
    AccountService accountService;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    private static final String MAX_TRY_LOGIN_COUNT = "max.try.login.count";

    private static final String LOGIN_FREEZE_TIME = "login.freeze.time";

    private static final String VIOLENT_HACK_DURATION = "security.violence.login.duration";

    private static final String KEY_PREFIX = "Freeze:";

    private static final String VIOLENT_HACK_KEY = "violentHacking:";

    private static final String SITEWEB6 = "[SiteWeb6]";

    /**
     * 后台重启需要将原先账号封锁key全部删除
     */
    @PostConstruct
    public void clearAccountPasswordErrReCord() {
        accountPasswordErrRecordMapper.delete(null);
        redisUtil.deleteByKeyPrefix(KEY_PREFIX + "*");
        redisUtil.deleteByKey(VIOLENT_HACK_KEY);
    }


    @Override
    public int createAccountPasswordErrRecord(AccountPasswordErrRecord accountPasswordErrRecord) {
        return accountPasswordErrRecordMapper.insert(accountPasswordErrRecord);
    }

    @Override
    public int updateAccountPasswordErrRecord(AccountPasswordErrRecord accountPasswordErrRecord) {
        return accountPasswordErrRecordMapper.updateById(accountPasswordErrRecord);
    }

    @Override
    public AccountPasswordErrRecord findByUserId(Integer userId) {
        return accountPasswordErrRecordMapper.selectById(userId);
    }


    @Override
    public void passwordErrAction(Integer userId) {
        int cnt = 0;
        int maxCount = getMaxCount();
        int freezeTime = getFreezeTime();
        AccountPasswordErrRecord accountPasswordErrRecord = findByUserId(userId);
        AccountDTO account = accountService.findByUserId(userId);
        // 记录安全日志
        securityAuditManager.recordSecurityReport(account.getLogonId(), SITEWEB6 + account.getLogonId() + messageSourceUtil.getMessage("audit.report.passwordError"), SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
        violentHackingCount(account);
        if (accountPasswordErrRecord == null) {
            // 首次增加记录
            accountPasswordErrRecord = new AccountPasswordErrRecord();
            accountPasswordErrRecord.setUserId(userId);
            accountPasswordErrRecord.setPasswordErrCnt(cnt + 1);
            createAccountPasswordErrRecord(accountPasswordErrRecord);
            return;
        }
        cnt = accountPasswordErrRecord.getPasswordErrCnt();
        cnt++;
        if (cnt == maxCount) {
            // 满足连续错误输入密码次数，将账号锁定，以及增加冻结目标时间
            Date date = DateUtil.dateAddSeconds(new Date(), freezeTime);
            accountPasswordErrRecord.setPasswordErrCnt(cnt);
            accountPasswordErrRecord.setFreezeTime(date);
            updateAccountPasswordErrRecord(accountPasswordErrRecord);
            // redis插入记录，key账户名称，value为目标解冻时间戳，设置缓存失效时间
            redisUtil.set(KEY_PREFIX + account.getLogonId(), String.valueOf(date.getTime() / 1000), freezeTime);
            securityAuditManager.recordSecurityReport(account.getLogonId(), SITEWEB6 + account.getLogonId() + messageSourceUtil.getMessage("audit.report.useraccountfreeze"), SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
        } else if (cnt > maxCount) {
            // 解冻后依旧输错密码，重新开始计数
            accountPasswordErrRecord.setPasswordErrCnt(0);
            accountPasswordErrRecord.setFreezeTime(null);
            updateAccountPasswordErrRecord(accountPasswordErrRecord);
        } else {
            // 计数
            accountPasswordErrRecord.setPasswordErrCnt(cnt);
            updateAccountPasswordErrRecord(accountPasswordErrRecord);
        }
    }


    @Override
    public void passwordSuccessAction(Integer userId) {
        // 登录成功后清空原先的密码错误记录
        AccountPasswordErrRecord accountPasswordErrRecord = findByUserId(userId);
        if (accountPasswordErrRecord == null) {
            return;
        }
        accountPasswordErrRecordMapper.deleteById(userId);
    }

    @Override
    public int remainingSecond(String logonId) {
        Object redisValue = redisUtil.get(KEY_PREFIX + logonId);
        if (redisValue == null) {
            return 0;
        }
        Date freezeTime = new Date(Long.valueOf(redisValue.toString()) * 1000L);
        return DateUtil.differentSecondsByMillisecond(new Date(), freezeTime);
    }

    @Override
    public boolean unfreezeAccount(Integer userId) {
        if (userId == null) {
            return false;
        }
        AccountDTO account = accountService.findByUserId(userId);
        if (account == null) {
            return false;
        }
        redisUtil.deleteByKey(KEY_PREFIX + account.getLogonId());
        AccountPasswordErrRecord errRecord = findByUserId(userId);
        if (errRecord == null) {
            return true; // 没有错误记录也算成功解冻
        }
        // 判断是否真的需要更新（避免不必要写库）
        if (errRecord.getPasswordErrCnt() > 0 || errRecord.getFreezeTime() != null) {
            errRecord.setPasswordErrCnt(0);
            errRecord.setFreezeTime(null);
            updateAccountPasswordErrRecord(errRecord);
        }
        return true;
    }

    public boolean checkVolentHack(String logonId) {
        String ipaddr = IpUtil.getIpAddr();
        Map<Object, Object> hmget = redisUtil.hmget(VIOLENT_HACK_KEY + ipaddr);
        if (MapUtil.isEmpty(hmget)) {
            return false;
        }
        int cnt = 0;
        for (Map.Entry<Object, Object> entry : hmget.entrySet()) {
            cnt += Integer.valueOf(entry.getValue().toString());
        }
        // 检查是否有两个以上的账号密码输错，共计大于等于最大密码错误次数
        if (hmget.size() >= 2 && cnt >= getMaxCount()) {
            // accountPasswordErrRecordMapper.delete(null);
            // redisUtil.deleteByKeyPrefix(KEY_PREFIX + "*");
            securityAuditManager.recordSecurityReport(logonId, SITEWEB6 + messageSourceUtil.getMessage("audit.report.volenthack"), SecurityReportTypeEnum.BRUTE_FORCE);
            return true;
        }
        return false;
    }

    /**
     * 密码错误最大尝试次数
     *
     * @return
     */
    private int getMaxCount() {
        SystemConfig systemconfig = systemConfigService.findBySystemConfigKey(MAX_TRY_LOGIN_COUNT);
        int maxCount = 3;
        if (systemconfig != null && StrUtil.isNotBlank(systemconfig.getSystemConfigValue())) {
            maxCount = Integer.valueOf(systemconfig.getSystemConfigValue());
        }
        return maxCount;
    }

    /**
     * 冻结时间
     *
     * @return
     */
    private int getFreezeTime() {
        SystemConfig systemconfig = systemConfigService.findBySystemConfigKey(LOGIN_FREEZE_TIME);
        int freezeTime = 600;
        if (systemconfig != null && StrUtil.isNotBlank(systemconfig.getSystemConfigValue())) {
            freezeTime = Integer.valueOf(systemconfig.getSystemConfigValue());
        }
        return freezeTime;
    }

    /**
     * 用户账号暴力破解持续时间
     *
     * @return
     */
    private int getViolentHackDuration() {
        SystemConfig systemconfig = systemConfigService.findBySystemConfigKey(VIOLENT_HACK_DURATION);
        int second = 300;
        if (systemconfig != null && StrUtil.isNotBlank(systemconfig.getSystemConfigValue())) {
            second = Integer.valueOf(systemconfig.getSystemConfigValue());
        }
        return second;
    }

    /**
     * 统计持续时间内账号密码错误次数
     */
    private void violentHackingCount(AccountDTO accountDTO) {
        String ipaddr = IpUtil.getIpAddr();
        String key = VIOLENT_HACK_KEY + ipaddr;
        Map<Object, Object> hmget = redisUtil.hmget(key);
        int violentHackingDuration = getViolentHackDuration();
        int cnt = 1;
        if (MapUtil.isEmpty(hmget)) {
            redisUtil.hset(key, accountDTO.getLogonId(), cnt, violentHackingDuration);
            return;
        }
        Object value = redisUtil.hget(key, accountDTO.getLogonId());
        if (value == null) {
            redisUtil.hset(key, accountDTO.getLogonId(), cnt);
        } else {
            int result = Integer.valueOf(value.toString());
            redisUtil.hset(key, accountDTO.getLogonId(), result + 1);
        }

    }
}
