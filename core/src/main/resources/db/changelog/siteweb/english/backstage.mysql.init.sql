
UPDATE tbl_account set Password = '2fc84bd28f1ca1946ef31ae4a1c94a00644f9c6668b8306bd02186c3ec909c7e' WHERE UserId = -4;
UPDATE tbl_account set Password = 'ddbeaa9fb7e87e2a4ce22bfe2c062aa4a586546e2286b13be3a35dd93f24fa83' WHERE UserId = -3;
UPDATE tbl_account set Password = '186c63a80a018f0eb3d810d93adb45f0e0c6b4fcf5d2f70f3ba9c907b87a14bd' WHERE UserId = -2;
UPDATE tbl_account set Password = 'e9bd73aab7ddbac5d1d1b723f10edb169eb36603132a964d7ff8d98529c1bd8a' WHERE UserId = -1;
INSERT INTO tbl_employee VALUES('-5', '0', 'SysAdmin2', NULL, NULL, '9999', NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 0, '172800');
INSERT INTO tbl_account(UserId, UserName, LogonId, PASSWORD, ENABLE, MaxError, LOCKED, ValidTime, DESCRIPTION, IsRemote, CenterId, PasswordValidTime, Avatar, ThemeName, NeedResetPwd)
VALUES('-5', 'SysAdmin2', 'sysAdmin', '19ffbcc8a54315c46d5576af5d2b378736e19769daebb50925644b7a0e8659df', 1, NULL, 0, NULL, NULL, 0, '24', NULL, NULL, NULL, 1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES('-5', '-1');
INSERT INTO tbl_account (UserId, UserName, LogonId, Password, Enable, MaxError, Locked, ValidTime, Description, IsRemote, CenterId, PasswordValidTime, Avatar, ThemeName, NeedResetPwd)
VALUES(-7, 'AiManager', 'AiManager', 'f2f5a64d1b42e6323853d67383b094e6d64d5e809fd94dbf55d39904a3147c99', 1, 10, 0, NULL, NULL, 0, 573, NULL, NULL, 'default', 0);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES('-7', '-1'); -- AI应用专属用户，密码adminadmin

-- INSERT INTO PermissionCategory values(1, '系统模块权限','P-Entry-Module', '界面主入口，控制用户对子模块的权限管理方式');
INSERT INTO PermissionCategory values(2, 'Action Permission','P-Entry-Action', 'The way permissions are managed for an action on an interface or data');
-- INSERT INTO PermissionCategory values(3, '设备专业权限','P-DataFilter-DeviceCategory', '对不同设备类型进行过滤的权限管理方式');
-- INSERT INTO PermissionCategory values(4, '工作台模块权限','P-Entry-WorkbenchModule', '对Web工作台界面模块权限管理方式');
INSERT INTO PermissionCategory values(5, 'Report Permission','P-Entry-Report', 'The way to manage the rights of each type of report');
INSERT INTO PermissionCategory values(6, 'Menu display permission','P-Entry-Menu', 'Permission management method to control logged-in users to display different menus');
-- INSERT INTO PermissionCategory values(7, '组态系统导航权限','P-Entry-Graphicpage', '控制登录用户显示不同的菜单的权限管理方式');
INSERT INTO PermissionCategory values(8, 'Professional Permissions','P-Entry-Specialty', 'SiteWeb Professional Permissions');
INSERT INTO PermissionCategory values(9, 'Area Permission','P-Entry-Area', 'SiteWeb Area authority');
INSERT INTO PermissionCategory values(10, 'Region Permission','P-Entry-Region', 'Area permissions have a way of managing permissions to areas and devices');
INSERT INTO PermissionCategory values(11, 'Department Permission','P-Entry-Department', 'Department permissions has the authority management method for departments and personnel');


INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (1, 'Event Confirmation', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (2, 'Event forced end', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (3, 'Record event notes', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (4, 'Lock event page', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (5, 'Export  Events', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (7, 'Turn off alarm sounding', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (8, 'Equipment Control', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (9, 'Failure control command confirmation', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (10, 'Local Station Shield', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (11, 'Equipment Mask', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (12, 'Event Mask', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (13, 'View Report', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (14, 'Interface personality settings', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (15, 'Dynamic Configuration', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (16, 'Maintenance expert advice', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (17, 'View Expert Advice', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (18, 'All filter conditions maintenance', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (19, 'Personal filter conditions maintenance', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (21, 'Template Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (22, 'End Bureau Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (23, 'Collection Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (24, 'Server Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (25, 'User Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (26, 'Event Notification Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (27, 'Access Control Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (29, 'Perform a backup', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (30, 'Perform Replication', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (31, 'Perform recovery', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (42, 'ServerConfig configuration', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (47, 'Login', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (48, 'Exit', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (49, 'Kick out', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (50, 'Sending control commands', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (51, 'Cancel control command', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (52, 'Confirmation of control commands', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (53, 'Open/close door command', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (54, 'Experts recommend adding', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (55, 'Experts recommend changes', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (56, 'Experts recommend deleting', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (57, 'Filter conditions added', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (58, 'Filter Condition Delete', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (59, 'Filter condition modification', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (60, 'Configuration changes', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (65, 'Local station project status setting', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (66, 'Alarm testing', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (67, 'Configuration Tool Login', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (80, 'New Users', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (81, 'Edit Account', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (82, 'Department Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (83, 'People Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (84, 'Module Permissions', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (85, 'Action Permission', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (86, 'Professional Permission', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (87, 'Area Permission', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (88, 'Role Authorization', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (101, 'Graphic Editor', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (102, 'Batch Masking', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (103, 'Alarm sound', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (104, 'Chart Control Style Code', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (105, 'Chart Control Data Processing', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (106, 'Shield Operation', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (107, 'AI Energy Saving Start Stop', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (108, 'AI Operation Strategy', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (109, 'Alarm Template Modified', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (110, 'Alarm Template Delete', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (111, 'APP Enter-U Uit Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (112, 'APP Enter-Cabinet Code', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (113, 'APP Enter-Energy Dashboard', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (114, 'APP Enter-Video', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (115, 'APP Enter-FSU Management', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (116, 'Person Delete', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (117, 'No More Alarm Pop-ups', 2, 'Action Permission');
-- 字节定制 权限点占用 121-140 避免冲突
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (121, 'Level 1 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (122, 'Level 2 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (123, 'Level 3 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (124, 'Level 4 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (125, 'Level 5 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (126, 'Level 6 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (127, 'Level 7 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (128, 'Level 8 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (129, 'Level 9 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (130, 'Level 10 alarm masking', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (131, 'Level 1 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (132, 'Level 2 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (133, 'Level 3 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (134, 'Level 4 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (135, 'Level 5 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (136, 'Level 6 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (137, 'Level 7 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (138, 'Level 8 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (139, 'Level 9 alarm confirmation', 2, 'Action Permission');
-- INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (140, 'Level 10 alarm confirmation', 2, 'Action Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (600, 'Report Export', 5, 'Report Permission');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (601, 'alarm', 5, '1');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (602, 'History Data', 5, '2');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (603, 'Indicator', 5, '3');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (604, 'Log', 5, '4');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (605, 'Other', 5, '5');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (606, 'Battery', 5, '6');
INSERT INTO Permission(PermissionId, name, Category, Caption) VALUES (607, 'Timed Report', 5, '999');
INSERT INTO permission(PermissionId, name, Category, Caption) VALUES (608, 'Security', 5, '7');
INSERT INTO permission(PermissionId, name, Category, Caption) VALUES (609, 'Air Conditioner', 5, '8');
-- 菜单权限
insert into permission (PermissionId, Name, Category, Caption, Description) values(201,'Backstage-Object Management-Device','6','MenuPermission','3-80-82');
insert into permission (PermissionId, Name, Category, Caption, Description) values(202,'Backstage-Object Management-Hierarchy','6','MenuPermission','3-80-81');
insert into permission (PermissionId, Name, Category, Caption, Description) values(203,'Backstage-Object Management-General Object','6','MenuPermission','3-80-88');
insert into permission (PermissionId, Name, Category, Caption, Description) values(204,'Report','6','MenuPermission','0-30');
insert into permission (PermissionId, Name, Category, Caption, Description) values(205,'Personal-User Info','6','MenuPermission','1-40');
insert into permission (PermissionId, Name, Category, Caption, Description) values(206,'Personal-Online User','6','MenuPermission','1-41');
insert into permission (PermissionId, Name, Category, Caption, Description) values(207,'Energy-Multi-dimensional-Indicator Config','6','MenuPermission','7-134-136');
insert into permission (PermissionId, Name, Category, Caption, Description) values(208,'Energy-Multi-dimensional-Solution Config','6','MenuPermission','7-134-135');
insert into permission (PermissionId, Name, Category, Caption, Description) values(209,'Energy-Energy-saving Measure-Measure Info','6','MenuPermission','7-131-132');
-- insert into permission (PermissionId, Name, Category, Caption, Description) values(210,'Energy-Energy-saving Measure-Analysis','6','MenuPermission','7-131-133');
insert into permission (PermissionId, Name, Category, Caption, Description) values(211,'Backstage-System Setting-Theme & Logo','6','MenuPermission','3-60-64');
insert into permission (PermissionId, Name, Category, Caption, Description) values(212,'Backstage-System Setting-Scene Switch','6','MenuPermission','3-60-63');
insert into permission (PermissionId, Name, Category, Caption, Description) values(213,'Backstage-System Setting-System Parameter','6','MenuPermission','3-60-61');
insert into permission (PermissionId, Name, Category, Caption, Description) values(214,'Energy-Energy Dashboard','6','MenuPermission','7-130');
insert into permission (PermissionId, Name, Category, Caption, Description) values(215,'Realtime-Data Monitor-Focus Signal','6','MenuPermission','5-70-72');
insert into permission (PermissionId, Name, Category, Caption, Description) values(216,'Realtime-Data Monitor-Device Monitor','6','MenuPermission','5-70-71');
insert into permission (PermissionId, Name, Category, Caption, Description) values(217,'Backstage-System Setting-Security Config','6','MenuPermission','3-60-66');
insert into permission (PermissionId, Name, Category, Caption, Description) values(218,'Backstage-System Setting-About','6','MenuPermission','3-60-65');
insert into permission (PermissionId, Name, Category, Caption, Description) values(219,'Energy-Peak-valley Price-Operation Log','6','MenuPermission','7-137-139');
insert into permission (PermissionId, Name, Category, Caption, Description) values(220,'Energy-Peak-valley Price-Price Config','6','MenuPermission','7-137-138');
insert into permission (PermissionId, Name, Category, Caption, Description) values(221,'Business-Rack Management','6','MenuPermission','6-296');
insert into permission (PermissionId, Name, Category, Caption, Description) values(222,'Energy-Energy Panorama','6','MenuPermission','7-129');
insert into permission (PermissionId, Name, Category, Caption, Description) values(223,'Backstage-Self Procotocol-Self Procotocol-SNMP','6','MenuPermission','3-***********');
insert into permission (PermissionId, Name, Category, Caption, Description) values(224,'Configuration-Linkage Strategy','6','MenuPermission','2-320');
insert into permission (PermissionId, Name, Category, Caption, Description) values(225,'Dashboard','6','MenuPermission','0-10');
insert into permission (PermissionId, Name, Category, Caption, Description) values(226,'Backstage-Warning Management-Warning Blocking','6','MenuPermission','3-160-164');
insert into permission (PermissionId, Name, Category, Caption, Description) values(227,'Backstage-Warning Management-Warning Level','6','MenuPermission','3-160-163');
insert into permission (PermissionId, Name, Category, Caption, Description) values(228,'Backstage-Warning Management-Warning Config','6','MenuPermission','3-160-162');
-- insert into permission (PermissionId, Name, Category, Caption, Description) values(229,'后台管理-预警管理-预警列表','6','MenuPermission','3-160-161');
insert into permission (PermissionId, Name, Category, Caption, Description) values(230,'Configuration-Rack Management-Rack Branch','6','MenuPermission','2-83-87');
insert into permission (PermissionId, Name, Category, Caption, Description) values(231,'Configuration-Rack Management-IT-Device','6','MenuPermission','2-83-85');
insert into permission (PermissionId, Name, Category, Caption, Description) values(232,'Backstage-Battery Management-Battery Model','6','MenuPermission','3-171-172');
insert into permission (PermissionId, Name, Category, Caption, Description) values(233,'Business-Distribution Management','6','MenuPermission','6-260');
insert into permission (PermissionId, Name, Category, Caption, Description) values(234,'Setting-Rack Management-IT-Device Model','6','MenuPermission','2-83-86');
insert into permission (PermissionId, Name, Category, Caption, Description) values(235,'Backstage-Battery Management-Battery Setting','6','MenuPermission','3-171-173');
insert into permission (PermissionId, Name, Category, Caption, Description) values(236,'Configuration-Rack Management-Rack Management','6','MenuPermission','2-83-84');
insert into permission (PermissionId, Name, Category, Caption, Description) values(237,'Alarm-Blocked Management-Device Blocked','6','MenuPermission','4-120-121');
insert into permission (PermissionId, Name, Category, Caption, Description) values(238,'Alarm-Blocked Management-Alarm Blocked','6','MenuPermission','4-120-122');
insert into permission (PermissionId, Name, Category, Caption, Description) values(239,'Alarm-Blocked Management-Bulk Blocking','6','MenuPermission','4-120-123');
insert into permission (PermissionId, Name, Category, Caption, Description) values(240,'Alarm-Alarm Convergence','6','MenuPermission','4-125');
insert into permission (PermissionId, Name, Category, Caption, Description) values(241,'Configuration-Monitoring-Alarm Notification-Same Message','6','MenuPermission','2-100-290-293');
insert into permission (PermissionId, Name, Category, Caption, Description) values(242,'Configuration-Monitoring-Alarm Notification-Notification Policy','6','MenuPermission','2-100-290-292');
insert into permission (PermissionId, Name, Category, Caption, Description) values(243,'Alarm-Alarm List','6','MenuPermission','4-20');
insert into permission (PermissionId, Name, Category, Caption, Description) values(244,'Configuration-User Management-Staff Management','6','MenuPermission','2-50-51');
insert into permission (PermissionId, Name, Category, Caption, Description) values(245,'Configuration-Monitoring-Alarm','6','MenuPermission','2-100-294');
insert into permission (PermissionId, Name, Category, Caption, Description) values(247,'Configuration-Monitoring-Alarm Notification-TTS Voice','6','MenuPermission','2-100-290-291');
insert into permission (PermissionId, Name, Category, Caption, Description) values(248,'Configuration-User Management-Area Permissions','6','MenuPermission','2-50-54');
insert into permission (PermissionId, Name, Category, Caption, Description) values(249,'Configuration-User Management-Role Authority','6','MenuPermission','2-50-53');
insert into permission (PermissionId, Name, Category, Caption, Description) values(250,'Configuration-System Self Diagnosis','6','MenuPermission','8-266');
insert into permission (PermissionId, Name, Category, Caption, Description) values(251,'Configuration-User Management-Account Management','6','MenuPermission','2-50-52');
insert into permission (PermissionId, Name, Category, Caption, Description) values(252,'Realtime-Access Control-Card Management','6','MenuPermission','5-6000-6002');
insert into permission (PermissionId, Name, Category, Caption, Description) values(253,'Realtime-Access Control-Area Permissions','6','MenuPermission','5-6000-6001');
insert into permission (PermissionId, Name, Category, Caption, Description) values(254,'Realtime-Access Control-Control Queue','6','MenuPermission','5-6000-6006');
insert into permission (PermissionId, Name, Category, Caption, Description) values(255,'Backstage-Self Procotocol-Self Procotocol-Driver Template','6','MenuPermission','3-***********');
insert into permission (PermissionId, Name, Category, Caption, Description) values(256,'Realtime-Access Control-Card Authorization','6','MenuPermission','5-6000-6005');
insert into permission (PermissionId, Name, Category, Caption, Description) values(257,'Backstage-Self Procotocol-Self Procotocol-BACNet','6','MenuPermission','3-***********');
insert into permission (PermissionId, Name, Category, Caption, Description) values(258,'Backstage-Self Procotocol-Equipment Split Tool','6','MenuPermission','3-165-170');
insert into permission (PermissionId, Name, Category, Caption, Description) values(259,'Realtime-Access Control-Permission Timeslot','6','MenuPermission','5-6000-6004');
insert into permission (PermissionId, Name, Category, Caption, Description) values(260,'Backstage-Capacity','6','MenuPermission','3-128');
insert into permission (PermissionId, Name, Category, Caption, Description) values(261,'Realtime-Access Control-Door Management','6','MenuPermission','5-6000-6003');
insert into permission (PermissionId, Name, Category, Caption, Description) values(262,'Realtime-Data Monitor-Electronic Map','6','MenuPermission','51-507-511');
insert into permission (PermissionId, Name, Category, Caption, Description) values(263,'Realtime-Data Monitor-Station State','6','MenuPermission','51-507-510');
insert into permission (PermissionId, Name, Category, Caption, Description) values(264,'Business-Distribution Management','6','MenuPermission','60-260');
insert into permission (PermissionId, Name, Category, Caption, Description) values(265,'Backstage-Object Management-Device','6','MenuPermission','93-80-82');
insert into permission (PermissionId, Name, Category, Caption, Description) values(266,'Maintain-Knowledge Management-Repository','6','MenuPermission','55-539-540');
insert into permission (PermissionId, Name, Category, Caption, Description) values(267,'Maintain-Knowledge Management-Relvant Case','6','MenuPermission','55-539-541');
insert into permission (PermissionId, Name, Category, Caption, Description) values(268,'Backstage-Object Management-Hierarchy','6','MenuPermission','93-80-81');
insert into permission (PermissionId, Name, Category, Caption, Description) values(269,'Report-Report Task','6','MenuPermission','56-552');
insert into permission (PermissionId, Name, Category, Caption, Description) values(270,'Report-Report Config','6','MenuPermission','56-551');
insert into permission (PermissionId, Name, Category, Caption, Description) values(271,'Backstage-System Setting-Security Config','6','MenuPermission','93-60-66');
insert into permission (PermissionId, Name, Category, Caption, Description) values(272,'Report-Browse Report','6','MenuPermission','56-550');
insert into permission (PermissionId, Name, Category, Caption, Description) values(273,'Alarm-Event Browsing','6','MenuPermission','52-512');
insert into permission (PermissionId, Name, Category, Caption, Description) values(274,'Alarm-Blocked Management','6','MenuPermission','52-513');
insert into permission (PermissionId, Name, Category, Caption, Description) values(275,'Configuration-Monitoring-Alarm Notification-Same Message','6','MenuPermission','92-100-290-293');
insert into permission (PermissionId, Name, Category, Caption, Description) values(276,'Configuration-Monitoring-Alarm Notification-Notification Policy','6','MenuPermission','92-100-290-292');
insert into permission (PermissionId, Name, Category, Caption, Description) values(277,'Configuration-Monitoring-Alarm Notification-TTS Voice','6','MenuPermission','92-100-290-291');
insert into permission (PermissionId, Name, Category, Caption, Description) values(278,'Energy-Peak-valley Price-Price Config','6','MenuPermission','61-137-138');
insert into permission (PermissionId, Name, Category, Caption, Description) values(279,'Backstage-Capacity','6','MenuPermission','93-128');
insert into permission (PermissionId, Name, Category, Caption, Description) values(280,'Energy-Peak-valley Price-Operation Log','6','MenuPermission','61-137-139');
insert into permission (PermissionId, Name, Category, Caption, Description) values(281,'Backstage-Self Procotocol-Equipment Split Tool','6','MenuPermission','93-165-170');
insert into permission (PermissionId, Name, Category, Caption, Description) values(282,'Maintain-System Self Diagnosis','6','MenuPermission','55-266');
insert into permission (PermissionId, Name, Category, Caption, Description) values(283,'Backstage-Version Management-Contract Info','6','MenuPermission','93-528-530');
insert into permission (PermissionId, Name, Category, Caption, Description) values(284,'Backstage-Version Management-Contract Management','6','MenuPermission','93-528-531');
insert into permission (PermissionId, Name, Category, Caption, Description) values(285,'Backstage-Version Management-QRcode Management','6','MenuPermission','93-528-532');
insert into permission (PermissionId, Name, Category, Caption, Description) values(286,'Backstage-Self Procotocol-Self Procotocol-Driver Template','6','MenuPermission','93-***********');
insert into permission (PermissionId, Name, Category, Caption, Description) values(287,'Maintain-Auto Patrol-Rule Define','6','MenuPermission','55-533-534');
insert into permission (PermissionId, Name, Category, Caption, Description) values(288,'Backstage-Self Procotocol-Self Procotocol-BACNet','6','MenuPermission','93-***********');
insert into permission (PermissionId, Name, Category, Caption, Description) values(289,'Backstage-Self Procotocol-Self Procotocol-SNMP','6','MenuPermission','93-***********');
insert into permission (PermissionId, Name, Category, Caption, Description) values(290,'Maintain-Auto Patrol-Patrol Task','6','MenuPermission','55-533-536');
insert into permission (PermissionId, Name, Category, Caption, Description) values(291,'Maintain-Auto Patrol-Group Management','6','MenuPermission','55-533-535');
insert into permission (PermissionId, Name, Category, Caption, Description) values(292,'Maintain-Auto Patrol-Warning Info','6','MenuPermission','55-533-538');
insert into permission (PermissionId, Name, Category, Caption, Description) values(293,'Maintain-Auto Patrol-Report Download','6','MenuPermission','55-533-537');
insert into permission (PermissionId, Name, Category, Caption, Description) values(294,'Configuration-Monitoring-Alarm','6','MenuPermission','92-100-294');
insert into permission (PermissionId, Name, Category, Caption, Description) values(296,'Configuration-Rack Management-IT-Device','6','MenuPermission','92-83-85');
insert into permission (PermissionId, Name, Category, Caption, Description) values(297,'Dashboard','6','MenuPermission','0-11');
insert into permission (PermissionId, Name, Category, Caption, Description) values(298,'Energy-Energy Panorama','6','MenuPermission','61-129');
insert into permission (PermissionId, Name, Category, Caption, Description) values(299,'Configuration-Rack Management-Rack Management','6','MenuPermission','92-83-84');
insert into permission (PermissionId, Name, Category, Caption, Description) values(300,'Energy-Energy-saving Measure-Measure Info','6','MenuPermission','61-131-132');
insert into permission (PermissionId, Name, Category, Caption, Description) values(301,'Configuration-Rack Management-Rack Branch','6','MenuPermission','92-83-87');
insert into permission (PermissionId, Name, Category, Caption, Description) values(302,'Energy-Energy-saving Measure-Analysis','6','MenuPermission','61-131-133');
insert into permission (PermissionId, Name, Category, Caption, Description) values(303,'Configuration-Rack Management-IT-Device Model','6','MenuPermission','92-83-86');
insert into permission (PermissionId, Name, Category, Caption, Description) values(304,'Backstage-Version Management-Gateway Info','6','MenuPermission','93-528-529');
insert into permission (PermissionId, Name, Category, Caption, Description) values(305,'Energy-Energy Dashboard','6','MenuPermission','61-130');
-- insert into permission (PermissionId, Name, Category, Caption, Description) values(306,'后台管理-预警管理-预警列表','6','MenuPermission','93-160-161');
insert into permission (PermissionId, Name, Category, Caption, Description) values(307,'Backstage-Warning Management-Warning Level','6','MenuPermission','93-160-163');
insert into permission (PermissionId, Name, Category, Caption, Description) values(308,'Backstage-Warning Management-Warning Config','6','MenuPermission','93-160-162');
insert into permission (PermissionId, Name, Category, Caption, Description) values(309,'Backstage-Warning Management-Warning Blocking','6','MenuPermission','93-160-164');
insert into permission (PermissionId, Name, Category, Caption, Description) values(310,'Configuration-Privilege Management-Area Authority','6','MenuPermission','92-553-558');
insert into permission (PermissionId, Name, Category, Caption, Description) values(311,'Backstage-System Setting-System Parameter','6','MenuPermission','93-60-61');
insert into permission (PermissionId, Name, Category, Caption, Description) values(312,'Configuration-Privilege Management-Expertise-related Authority','6','MenuPermission','92-553-557');
insert into permission (PermissionId, Name, Category, Caption, Description) values(313,'Business-Rack Management','6','MenuPermission','60-296');
insert into permission (PermissionId, Name, Category, Caption, Description) values(314,'Configuration-Privilege Management-Role Authority','6','MenuPermission','92-553-556');
insert into permission (PermissionId, Name, Category, Caption, Description) values(315,'Backstage-System Setting-Theme & Logo','6','MenuPermission','93-60-64');
insert into permission (PermissionId, Name, Category, Caption, Description) values(316,'Backstage-System Setting-About','6','MenuPermission','93-60-65');
insert into permission (PermissionId, Name, Category, Caption, Description) values(317,'Configuration-Linkage Strategy','6','MenuPermission','92-320');
insert into permission (PermissionId, Name, Category, Caption, Description) values(318,'Backstage-System Setting-Scene Switch','6','MenuPermission','93-60-63');
insert into permission (PermissionId, Name, Category, Caption, Description) values(319,'Personal-Online User','6','MenuPermission','91-41');
insert into permission (PermissionId, Name, Category, Caption, Description) values(320,'Realtime-Data Monitor-Device Monitoring','6','MenuPermission','51-507-508');
insert into permission (PermissionId, Name, Category, Caption, Description) values(321,'Personal-User Info','6','MenuPermission','91-40');
insert into permission (PermissionId, Name, Category, Caption, Description) values(322,'Configuration-Privilege Management-Account Management','6','MenuPermission','92-553-555');
insert into permission (PermissionId, Name, Category, Caption, Description) values(323,'Realtime-Data Monitor-Attention Signal','6','MenuPermission','51-507-509');
insert into permission (PermissionId, Name, Category, Caption, Description) values(324,'Configuration-Privilege Management-Staff Management','6','MenuPermission','92-553-554');
insert into permission (PermissionId, Name, Category, Caption, Description) values(325,'Realtime-Access Control-Control Queue','6','MenuPermission','51-542-548');
insert into permission (PermissionId, Name, Category, Caption, Description) values(326,'Energy-Multi-dimensional-Solution Config','6','MenuPermission','61-134-135');
insert into permission (PermissionId, Name, Category, Caption, Description) values(327,'Energy-Multi-dimensional-Indicator Config','6','MenuPermission','61-134-136');
insert into permission (PermissionId, Name, Category, Caption, Description) values(328,'Realtime-Access Control-Permission Timeslot','6','MenuPermission','51-542-546');
insert into permission (PermissionId, Name, Category, Caption, Description) values(329,'Realtime-Access Control-Card Authorization','6','MenuPermission','51-542-547');
insert into permission (PermissionId, Name, Category, Caption, Description) values(330,'Realtime-Access Control-Card Management','6','MenuPermission','51-542-544');
insert into permission (PermissionId, Name, Category, Caption, Description) values(331,'Backstage-Object Management-General Object','6','MenuPermission','93-80-88');
insert into permission (PermissionId, Name, Category, Caption, Description) values(332,'Realtime-Access Control-Door Management','6','MenuPermission','51-542-545');
insert into permission (PermissionId, Name, Category, Caption, Description) values(333,'Realtime-Access Control-Area Permissions','6','MenuPermission','51-542-543');
insert into permission (PermissionId, Name, Category, Caption, Description) values(334,'Realtime-Access Control-Area Permissions','6','MenuPermission','51-542-543');
insert into permission (PermissionId, Name, Category, Caption, Description) values(335,'Configuration-Rack Management-Tag Management','6','MenuPermission','2-83-101');
insert into permission (PermissionId, Name, Category, Caption, Description) values(336,'Configuration-Rack Management-Tag Management','6','MenuPermission','92-83-101');
insert into permission (PermissionId, Name, Category, Caption, Description) values(337,'Configuration-Monitoring-Distribution Relation','6','MenuPermission','2-100-321');
insert into permission (PermissionId, Name, Category, Caption, Description) values(338,'Configuration-Monitoring-Distribution Relation','6','MenuPermission','92-100-321');
-- insert into permission (PermissionId, Name, Category, Caption, Description) values(339,'实时监控-数据监控-局站状态','6','MenuPermission','5-70-73');
insert into permission (PermissionId, Name, Category, Caption, Description) values(340,'Energy-Energy-saving Measure-Analysis-measures contrast','6','MenuPermission','7-131-133-141');
insert into permission (PermissionId, Name, Category, Caption, Description) values(341,'Energy-Energy-saving Measure-Analysis-measures Rank','6','MenuPermission','7-131-133-142');
insert into permission (PermissionId, Name, Category, Caption, Description) values(342,'Energy-Energy-saving Measure-Analysis-Measures Electricity Consumption','6','MenuPermission','7-131-133-140');
insert into permission (PermissionId, Name, Category, Caption, Description) values(343,'Energy-Energy Dictionary','6','MenuPermission','7-143');
insert into permission (PermissionId, Name, Category, Caption, Description) values(344,'Energy-Energy Index','6','MenuPermission','7-144');
insert into permission (PermissionId, Name, Category, Caption, Description) values(345,'Configuration-Monitoring-Alarm Video Link','6','MenuPermission','2-100-295');
insert into permission (PermissionId, Name, Category, Caption, Description) values(347,'Backstage-Version Management-QRcode Management-Platform QRcode','6','MenuPermission','93-528-532-526');
insert into permission (PermissionId, Name, Category, Caption, Description) values(348,'Backstage-Version Management-QRcode Management-Gateway QRcode','6','MenuPermission','93-528-532-527');
insert into permission (PermissionId, Name, Category, Caption, Description) values(349,'Backstage-Version Management-Contract Management-Maintenance Contract Management','6','MenuPermission','93-528-531-525');
insert into permission (PermissionId, Name, Category, Caption, Description) values(350,'Backstage-Version Management-Contract Management-Install Contract Management','6','MenuPermission','93-528-531-524');
insert into permission (PermissionId, Name, Category, Caption, Description) values(351,'Configuration-Monitoring-Alarm Video Link','6','MenuPermission','92-100-295');
insert into permission (PermissionId, Name, Category, Caption, Description) values(353,'Backstage-Version Management-Contract Info-Gateway Contract Info','6','MenuPermission','93-528-530-523');
insert into permission (PermissionId, Name, Category, Caption, Description) values(354,'Backstage-Version Management-Contract Info-Device Contract Info','6','MenuPermission','93-528-530-522');
insert into permission (PermissionId, Name, Category, Caption, Description) values(355,'Backstage-Version Management-Contract Info-Station Contract Info','6','MenuPermission','93-528-530-521');
insert into permission (PermissionId, Name, Category, Caption, Description) values(356,'Energy-Energy Index','6','MenuPermission','61-144');
insert into permission (PermissionId, Name, Category, Caption, Description) values(357,'Energy-Energy Dictionary','6','MenuPermission','61-143');
insert into permission (PermissionId, Name, Category, Caption, Description) values(358,'Energy-Energy-saving Measure-Analysis-Measures Electricity Consumption','6','MenuPermission','61-131-133-140');
insert into permission (PermissionId, Name, Category, Caption, Description) values(359,'Energy-Energy-saving Measure-Analysis-measures Rank','6','MenuPermission','61-131-133-142');
insert into permission (PermissionId, Name, Category, Caption, Description) values(360,'Energy-Energy-saving Measure-Analysis-measures contrast','6','MenuPermission','**************');
insert into permission (PermissionId, Name, Category, Caption, Description) values(361,'Backstage-Version Management-Gateway Info-Gateway Pie Info','6','MenuPermission','**************');
insert into permission (PermissionId, Name, Category, Caption, Description) values(362,'Backstage-Version Management-Gateway Info-Gateway List','6','MenuPermission','**************');
insert into permission (PermissionId, Name, Category, Caption, Description) values(363,'Business-Battery Management-Battery Dashboard','6','MenuPermission','6-261-262');
insert into permission (PermissionId, Name, Category, Caption, Description) values(364,'Business-Battery Management-Battery Monitor','6','MenuPermission','6-261-263');
insert into permission (PermissionId, Name, Category, Caption, Description) values(365,'Business-Battery Management-Battery Setting-asset Setting','6','MenuPermission','6-261-264-265');
insert into permission (PermissionId, Name, Category, Caption, Description) values(366,'Business-Battery Management-Battery Setting-Data Analysis','6','MenuPermission','6-261-264-267');
insert into permission (PermissionId, Name, Category, Caption, Description) values(367,'Business-Battery Management-Battery Setting-System Setting','6','MenuPermission','6-261-264-268');
insert into permission (PermissionId, Name, Category, Caption, Description) values(368,'Business-Battery Management-Battery Dashboard','6','MenuPermission','60-261-262');
insert into permission (PermissionId, Name, Category, Caption, Description) values(369,'Business-Battery Management-Battery Monitor','6','MenuPermission','60-261-263');
insert into permission (PermissionId, Name, Category, Caption, Description) values(370,'Business-Battery Management-Battery Setting-asset Setting','6','MenuPermission','**************');
insert into permission (PermissionId, Name, Category, Caption, Description) values(371,'Business-Battery Management-Battery Setting-Data Analysis','6','MenuPermission','**************');
insert into permission (PermissionId, Name, Category, Caption, Description) values(372,'Business-Battery Management-Battery Setting-System Setting','6','MenuPermission','**************');
-- insert into permission (PermissionId, Name, Category, Caption, Description) values(373,'Config Management-机架管理-设备支路管理','6','MenuPermission','2-83-102');
-- insert into permission (PermissionId, Name, Category, Caption, Description) values(374,'Config Management-机架管理-设备支路管理','6','MenuPermission','92-83-102');
insert into permission (PermissionId, Name, Category, Caption, Description) values(375,'Backstage-System Setting-Page Config','6','MenuPermission','3-60-67');
insert into permission (PermissionId, Name, Category, Caption, Description) values(376,'Backstage-System Setting-Page Config','6','MenuPermission','93-60-67');
insert into permission (PermissionId, Name, Category, Caption, Description) values(377,'Backstage-System Setting-Custom Menu','6','MenuPermission','3-60-62');
insert into permission (PermissionId, Name, Category, Caption, Description) values(378,'Backstage-System Setting-Custom Menu','6','MenuPermission','93-60-62');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(379, 'Backstage-System Setting-Scene Switch', 6, 'MenuPermission', '9-60-63');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(380, 'Backstage-System Setting-Theme & Logo', 6, 'MenuPermission', '9-60-64');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(381, 'Robot', 6, 'MenuPermission', '0-701');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(382, 'Backstage-Object Management-Hierarchy', 6, 'MenuPermission', '9-80-81');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(383, 'Backstage-System Setting-About', 6, 'MenuPermission', '9-60-65');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(384, 'Home Page', 6, 'MenuPermission', '0-700');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(385, 'Backstage-Object Management-Device', 6, 'MenuPermission', '9-80-82');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(386, 'Backstage-System Setting-Security Config', 6, 'MenuPermission', '9-60-66');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(387, 'Task Management', 6, 'MenuPermission', '0-703');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(388, 'Alarm Overview', 6, 'MenuPermission', '0-702');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(389, 'Remote Control', 6, 'MenuPermission', '0-705');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(390, 'Backstage-System Setting-System Parameter', 6, 'MenuPermission', '9-60-61');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(391, 'Data Analysis', 6, 'MenuPermission', '0-704');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(392, 'Backstage-System Setting-Custom Menu', 6, 'MenuPermission', '9-60-62');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(393, 'Maintain', 6, 'MenuPermission', '0-706');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(394, 'Backstage-Object Management-General Object', 6, 'MenuPermission', '9-80-88');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(395, 'Backstage-System Setting-Page Config', 6, 'MenuPermission', '9-60-67');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(396, 'Business-Warning List', 6, 'MenuPermission', '6-161');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(397, 'Business-Warning List', 6, 'MenuPermission', '60-161');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(398, 'Configuration-Chart Data Setting', 6, 'MenuPermission', '2-340');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(399, 'Configuration-Chart Data Setting-Chart Theme', 6, 'MenuPermission', '2-340-341');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(400, 'Configuration-Chart Data Setting-Chart Interface', 6, 'MenuPermission', '2-340-342');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(401, 'Configuration-Chart Data Setting-Chart Style', 6, 'MenuPermission', '2-340-343');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(402, 'Configuration-Shift Management-Shift Management', 6, 'MenuPermission', '2-350-351');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(403, 'Configuration-Shift Management-Shift Management', 6, 'MenuPermission', '92-350-351');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(404, 'Configuration-Shift Management-Shift Group Management', 6, 'MenuPermission', '2-350-352');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(405, 'Configuration-Shift Management-Shift Group Management', 6, 'MenuPermission', '92-350-352');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(406, 'Configuration-Shift Management-Schedule Management', 6, 'MenuPermission', '2-350-353');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(407, 'Configuration-Shift Management-Schedule Management', 6, 'MenuPermission', '92-350-353');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(410, 'Energy-EnergyEfficiency-ParameterConfig', 6, 'MenuPermission', '7-145-146');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(411, 'Energy-EnergyEfficiency-GradeEvaluate', 6, 'MenuPermission', '7-145-147');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(412, 'Battery Security', 6, 'MenuPermission', '0-15');

insert into permission (PermissionId, Name, Category, Caption, Description) values(408,'Backstage-System Setting-Main Page Config',6,'MenuPermission','3-60-68');
insert into permission (PermissionId, Name, Category, Caption, Description) values(409,'Backstage-System Setting-Main Page Config',6,'MenuPermission','93-60-68');
insert into permission (PermissionId, Name, Category, Caption, Description) values(413,'Backstage-System Setting-Main Page Config-User Graphic Page',6,'MenuPermission','3-60-68-74');
insert into permission (PermissionId, Name, Category, Caption, Description) values(414,'Backstage-System Setting-Main Page Config-User Graphic Page',6,'MenuPermission','93-60-68-74');
insert into permission (PermissionId, Name, Category, Caption, Description) values(415,'Backstage-System Setting-Main Page Config-Role Graphic Page',6,'MenuPermission','3-60-68-75');
insert into permission (PermissionId, Name, Category, Caption, Description) values(416,'Backstage-System Setting-Main Page Config-Role Graphic Page',6,'MenuPermission','93-60-68-75');
insert into permission (PermissionId, Name, Category, Caption, Description) values(417,'Business-Asset List',6,'MenuPermission','6-355');
insert into permission (PermissionId, Name, Category, Caption, Description) values(418,'Configuration-Asset Management-Asset Category',6,'MenuPermission','2-356-357');
insert into permission (PermissionId, Name, Category, Caption, Description) values(419,'Configuration-Asset Management-Asset ExtendField',6,'MenuPermission','2-356-358');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(420, 'Business-Air Energy Efficiency-Air Board', 6, 'MenuPermission', '6-344-345');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(421, 'Business-Air Energy Efficiency-Template Map', 6, 'MenuPermission', '6-344-346');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(422, 'Business-Air Energy Efficiency-Parameter Config', 6, 'MenuPermission', '6-344-347');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(423, 'Business-Air Energy Efficiency-Air Control Group', 6, 'MenuPermission', '6-344-348');
-- INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(424, 'Business-Air Energy Efficiency-Electricity Analysis', 6, 'MenuPermission', '6-344-349');
insert into permission (PermissionId, Name, Category, Caption, Description) values(425, 'Configuration-Privilege Management-Region Authority', 6, 'MenuPermission', '92-553-559');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(426, 'Backstage-System Setting-Business Module Config', 6, 'MenuPermission', '3-60-359');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(427, 'Backstage-System Setting-Business Module Config', 6, 'MenuPermission', '93-60-359');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(428, 'Personal-User Config', 6, 'MenuPermission', '1-42');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(429, 'Personal-User Config', 6, 'MenuPermission', '91-42');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(430, 'Backstage-Online Documentation', 6, 'MenuPermission', '3-360');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(431, 'Backstage-Online Documentation', 6, 'MenuPermission', '93-360');
insert into permission (PermissionId, Name, Category, Caption, Description) values(432,'Alarm-Blocked Management-Condition Blocked','6','MenuPermission','4-120-124');
insert into permission (PermissionId, Name, Category, Caption, Description) values(434,'Configuration-User Management-Operation Permissions','6','MenuPermission','2-50-55');
insert into permission (PermissionId, Name, Category, Caption, Description) values(435,'Configuration-User Management-Operation Permissions', '6', 'MenuPermission', '92-553-562');
insert into permission (PermissionId, Name, Category, Caption, Description) values(436,'Configuration-User Management-Menu Permissions','6','MenuPermission','2-50-56');
insert into permission (PermissionId, Name, Category, Caption, Description) values(437,'Configuration-User Management-Menu Permissions','6','MenuPermission','92-553-563');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(438, 'Configuration-Rack Management-Rack Equipment', '6', 'MenuPermission', '2-83-104');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(439, 'Configuration-Rack Management-Rack Equipment', '6', 'MenuPermission', '92-83-104');
insert into permission (PermissionId, Name, Category, Caption, Description) values(441,'Configuration-Video',6,'MenuPermission','2-362');
insert into permission (PermissionId, Name, Category, Caption, Description) values(442,'Configuration-Video-Video Group',6,'MenuPermission','2-362-363');
insert into permission (PermissionId, Name, Category, Caption, Description) values(443,'Configuration-Video-Video Config',6,'MenuPermission','2-362-364');
insert into permission (PermissionId, Name, Category, Caption, Description) values(444,'Maintain-System Topology','6','MenuPermission','8-581');
insert into permission (PermissionId, Name, Category, Caption, Description) values(445,'Configuration-Link Management','6','MenuPermission','2-420');

INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(800, 'Personal-User Info', 6, 'MenuPermission', '150-40', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(801, 'Personal-Online User', 6, 'MenuPermission', '150-41', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(802, 'Configuration-Monitoring-Alarm Notification-TTS Voice', 6, 'MenuPermission', '151-100-290-291', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(803, 'Configuration-Monitoring-Alarm Notification-Notification Policy', 6, 'MenuPermission', '151-100-290-292', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(804, 'Configuration-Monitoring-Alarm Notification-Same Message', 6, 'MenuPermission', '151-100-290-293', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(805, 'Configuration-Monitoring-Alarm', 6, 'MenuPermission', '151-100-294', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(806, 'Configuration-Monitoring-Alarm Video Link', 6, 'MenuPermission', '151-100-295', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(809, 'Configuration-Linkage Strategy', 6, 'MenuPermission', '151-320', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(810, 'Configuration-Chart Data Setting', 6, 'MenuPermission', '151-340', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(811, 'Configuration-Chart Data Setting-Chart Theme', 6, 'MenuPermission', '151-340-341', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(812, 'Configuration-Chart Data Setting-Chart Interface', 6, 'MenuPermission', '151-340-342', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(813, 'Configuration-Chart Data Setting-Chart Style', 6, 'MenuPermission', '151-340-343', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(814, 'Configuration-Shift Management-Shift Management', 6, 'MenuPermission', '151-350-351', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(815, 'Configuration-Shift Management-Shift Group Management', 6, 'MenuPermission', '151-350-352', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(816, 'Configuration-Shift Management-Schedule Management', 6, 'MenuPermission', '151-350-353', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(817, 'Configuration-User Management-Staff Management', 6, 'MenuPermission', '151-50-51', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(818, 'Configuration-User Management-Account Management', 6, 'MenuPermission', '151-50-52', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(819, 'Configuration-User Management-Role Authority', 6, 'MenuPermission', '151-50-53', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(820, 'Configuration-User Management-Area Permissions', 6, 'MenuPermission', '151-50-54', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(821, 'Backstage-System Setting-System Parameter', 6, 'MenuPermission', '152-60-61', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(822, 'Backstage-System Setting-Custom Menu', 6, 'MenuPermission', '152-60-62', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(823, 'Backstage-System Setting-Scene Switch', 6, 'MenuPermission', '152-60-63', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(824, 'Backstage-System Setting-Theme & Logo', 6, 'MenuPermission', '152-60-64', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(825, 'Backstage-System Setting-About', 6, 'MenuPermission', '152-60-65', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(826, 'Backstage-System Setting-Security Config', 6, 'MenuPermission', '152-60-66', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(827, 'Backstage-System Setting-Page Config', 6, 'MenuPermission', '152-60-67', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(829, 'Backstage-Object Management-Hierarchy', 6, 'MenuPermission', '152-80-81', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(830, 'Backstage-Object Management-Device', 6, 'MenuPermission', '152-80-82', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(831, 'Backstage-Object Management-General Object', 6, 'MenuPermission', '152-80-88', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(832, 'Alarm-Blocked Management-Device Blocked', 6, 'MenuPermission', '153-120-121', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(833, 'Alarm-Blocked Management-Alarm Blocked', 6, 'MenuPermission', '153-120-122', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(834, 'Alarm-Blocked Management-Bulk Blocking', 6, 'MenuPermission', '153-120-123', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(835, 'Alarm-Alarm Convergence', 6, 'MenuPermission', '153-125', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(836, 'Alarm-Alarm List', 6, 'MenuPermission', '153-20', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(837, 'Realtime-Access Control-Area Permissions', 6, 'MenuPermission', '154-542-543', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(838, 'Realtime-Access Control-Card Management', 6, 'MenuPermission', '154-542-544', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(839, 'Realtime-Access Control-Door Management', 6, 'MenuPermission', '154-542-545', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(840, 'Realtime-Access Control-Permission Timeslot', 6, 'MenuPermission', '154-542-546', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(841, 'Realtime-Access Control-Card Authorization', 6, 'MenuPermission', '154-542-547', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(842, 'Realtime-Access Control-Control Queue', 6, 'MenuPermission', '154-542-548', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(843, 'Realtime-Data Monitor-Device Monitor', 6, 'MenuPermission', '154-70-71', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(844, 'Realtime-Data Monitor-Focus Signal', 6, 'MenuPermission', '154-70-72', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(845, 'Realtime-Data Monitor-Station State', 6, 'MenuPermission', '154-70-73', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(846, 'Maintain-System Self Diagnosis', 6, 'MenuPermission', '155-266', NULL);
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(847, 'Dashboard', 6, 'MenuPermission', '0-12', NULL);
insert into permission (PermissionId, Name, Category, Caption, Description) values(848,'Configuration-Video',6,'MenuPermission','92-362');
insert into permission (PermissionId, Name, Category, Caption, Description) values(849,'Configuration-Video-Video Group',6,'MenuPermission','92-362-363');
insert into permission (PermissionId, Name, Category, Caption, Description) values(850,'Configuration-Video-Video Config',6,'MenuPermission','92-362-364');

-- AI空调
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(900,'Business-AI Room Energy Saving-AI Room Energy Saving','6','MenuPermission','6-400-401');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(901,'Business-AI Room Energy Saving-AI Cockpit','6','MenuPermission','6-400-402');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(902,'Business-AI Room Energy Saving-AI Model Group Configuration','6','MenuPermission','6-400-403');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(903,'Business-AI Room Energy Saving-AI Model Management','6','MenuPermission','6-400-404');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(904,'Business-AI Room Energy Saving-AI Strategy History','6','MenuPermission','6-400-405');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(905,'Business-AI Room Energy Saving-AI Energy Saving Parameter Settings','6','MenuPermission','6-400-406');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(906,'Business-AI Room Energy Saving-AI Asset Management','6','MenuPermission','6-400-407');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(914,'Business-AI Room Energy Saving-AI energy report','6','MenuPermission','6-400-408');


INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(907,'Business-AI Room Energy Saving-AI ROOM Overview','6','MenuPermission','60-400-401');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(908,'Business-AI Room Energy Saving-AI Cockpit','6','MenuPermission','60-400-402');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(909,'Business-AI Room Energy Saving-AI Model Group Configuration','6','MenuPermission','60-400-403');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(910,'Business-AI Room Energy Saving-AI Model Management','6','MenuPermission','60-400-404');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(911,'Business-AI Room Energy Saving-AI Strategy History','6','MenuPermission','60-400-405');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(912,'Business-AI Room Energy Saving-AI Energy Saving Parameter Settings','6','MenuPermission','60-400-406');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(913,'Business-AI Room Energy Saving-AI Asset Management','6','MenuPermission','60-400-407');
INSERT INTO permission (PermissionId, NAME, Category, Caption, DESCRIPTION) VALUES(915,'Business-AI Room Energy Saving-AI energy report','6','MenuPermission','60-400-408');

INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(920, 'Business-Air Energy Efficiency-Air Board', 6, 'MenuPermission', '60-344-345');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(921, 'Business-Air Energy Efficiency-Template Map', 6, 'MenuPermission', '60-344-346');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(922, 'Business-Air Energy Efficiency-Parameter Config', 6, 'MenuPermission', '60-344-347');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(923, 'Business-Air Energy Efficiency-Air Control Group', 6, 'MenuPermission', '60-344-348');
-- INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(924, 'Business-Air Energy Efficiency-Electricity Analysis', 6, 'MenuPermission', '60-344-349');

INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(950, 'Maintain-Asset Manager', 6, 'MenuPermission', '55-549');

-- idc专家建议
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(960, 'Maintain-Knowledge Management', 6, 'MenuPermission', '8-570', '2023-06-12 13:49:21');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(961, 'Maintain-Knowledge Management-Repository', 6, 'MenuPermission', '8-570-571', '2023-06-12 13:49:21');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description, UpdateTime) VALUES(962, 'Maintain-Knowledge Management-Relvant Case', 6, 'MenuPermission', '8-570-572', '2023-06-12 13:49:21');

-- 告警抓图，idc和网点场景
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(963, 'Alarm Snapshot', 6, 'MenuPermission', '4-361');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(964, 'Alarm Snapshot', 6, 'MenuPermission', '52-361');

-- Energy
-- IDC场景：
insert into permission (PermissionId, Name, Category, Caption, Description) values(2001,'Energy-Energy Dashboard','6','MenuPermission','7-130');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2002,'Energy-Energy Panorama','6','MenuPermission','7-129');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2003,'Energy-Query Analysis-Energy Consumption', 6, 'MenuPermission', '7-148-149');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2004,'Energy-Query Analysis-Energy Efficiency', 6, 'MenuPermission', '7-148-150');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2005,'Energy-Query Analysis-Energy Consumption', 6, 'MenuPermission', '7-148-151');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2006,'Energy-Query Analysis-Other Energy Usage', 6, 'MenuPermission', '7-148-152');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2007,'Energy-Query Analysis-Energy Cost', 6, 'MenuPermission', '7-148-153');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2008,'Energy-Energy Efficiency Assessment-Gradeevaluate', 6, 'MenuPermission', '7-145-146');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2009,'Energy-Energy Efficiency Assessment-Parameterconfig', 6, 'MenuPermission', '7-145-147');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2010,'Energy-Energy-Saving Measure-Measure Info','6','MenuPermission','7-131-132');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2011,'Energy-Energy-Saving Measure-Analysis','6','MenuPermission','7-131-133');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2012,'Energy-Energy-Saving Measure-Analysis-Measures Contrast','6','MenuPermission','7-131-133-141');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2013,'Energy-Energy-Saving Measure-Analysis-Measures Rank','6','MenuPermission','7-131-133-142');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2014,'Energy-Energy-Saving Measure-Analysis-Measures Electricity Consumption','6','MenuPermission','7-131-133-140');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2016,'Energy-Config Management-Energy Dictionary','6','MenuPermission','7-154-134');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2017,'Energy-Config Management-Energy Index','6','MenuPermission','7-154-137');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2018,'Energy-Config Management-Multi-Dimensional-Indicator Config','6','MenuPermission','7-154-144-136');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2019,'Energy-Config Management-Multi-Dimensional-Solution Config','6','MenuPermission','7-154-144-135');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2020,'Energy-Config Management-Peak-valley Price-Price Config','6','MenuPermission','7-154-143-138');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2021,'Energy-Config Management-Peak-valley Price-Operation Log','6','MenuPermission','7-154-143-139');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2042,'Energy-Config Management-CarbonQuota Management','6','MenuPermission','7-154-155');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2043,'Energy-Config Management-CarbonEmission Management','6','MenuPermission','7-154-156');
-- 网点场景
insert into permission (PermissionId, Name, Category, Caption, Description) values(2022,'Energy-Dashboard','6','MenuPermission','61-560');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2046,'Energy-EnergyCarbon Dashboard','6','MenuPermission','61-561');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2023,'Energy-Panorama','6','MenuPermission','61-129');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2024,'Energy-Query Analysis-Energy Consumption', 6, 'MenuPermission', '61-148-149');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2025,'Energy-Query Analysis-Energy Efficiency', 6, 'MenuPermission', '61-148-150');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2026,'Energy-Query Analysis-Energy Consumption', 6, 'MenuPermission', '61-148-151');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2027,'Energy-Query Analysis-Other Energy Usage', 6, 'MenuPermission', '61-148-152');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2028,'Energy-Query Analysis-Energy Cost', 6, 'MenuPermission', '61-148-153');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2029,'Energy-Energy Efficiency Assessment-Gradeevaluate', 6, 'MenuPermission', '61-145-146');
INSERT INTO permission (PermissionId, Name, Category, Caption, Description) VALUES(2030,'Energy-Energy Efficiency Assessment-Parameterconfig', 6, 'MenuPermission', '61-145-147');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2031,'Energy-Energy-Saving Measure-Measure Info','6','MenuPermission','61-131-132');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2032,'Energy-Energy-Saving Measure-Analysis','6','MenuPermission','61-131-133');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2033,'Energy-Energy-Saving Measure-Analysis-Measures Contrast','6','MenuPermission','**************');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2034,'Energy-Energy-Saving Measure-Analysis-Measures Rank','6','MenuPermission','61-131-133-142');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2035,'Energy-Energy-Saving Measure-Analysis-Measures Electricity Consumption','6','MenuPermission','61-131-133-140');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2036,'Energy-Config Management-Energy Dictionary','6','MenuPermission','61-154-134');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2037,'Energy-Config Management-Energy Index','6','MenuPermission','61-154-137');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2038,'Energy-Config Management-Multi-Dimensional-Indicator Config','6','MenuPermission','61-154-144-136');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2039,'Energy-Config Management-Multi-Dimensional-Solution Config','6','MenuPermission','61-154-144-135');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2040,'Energy-Config Management-Peak-valley Price-Price Config','6','MenuPermission','61-154-143-138');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2041,'Energy-Config Management-Peak-valley Price-Operation Log','6','MenuPermission','61-154-143-139');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2044,'Energy-Config Management-CarbonQuota Management','6','MenuPermission','61-154-155');
insert into permission (PermissionId, Name, Category, Caption, Description) values(2045,'Energy-Config Management-CarbonEmission Management','6','MenuPermission','61-154-156');

INSERT INTO RolePermissionMap VALUES (1000, -1, 2, 1);
INSERT INTO RolePermissionMap VALUES (1001, -1, 2, 2);
INSERT INTO RolePermissionMap VALUES (1002, -1, 2, 3);
INSERT INTO RolePermissionMap VALUES (1003, -1, 2, 5);
INSERT INTO RolePermissionMap VALUES (1004, -1, 2, 8);
INSERT INTO RolePermissionMap VALUES (1005, -1, 2, 11);
INSERT INTO RolePermissionMap VALUES (1006, -1, 2, 12);
INSERT INTO RolePermissionMap VALUES (1007, -1, 2, 101);
INSERT INTO RolePermissionMap VALUES (1008, -1, 2, 102);
INSERT INTO RolePermissionMap VALUES (1009, -1, 2, 103);
-- INSERT INTO RolePermissionMap VALUES (1010, 1, 2, 1);
-- INSERT INTO RolePermissionMap VALUES (1011, 1, 2, 2);
-- INSERT INTO RolePermissionMap VALUES (1012, 1, 2, 3);
-- INSERT INTO RolePermissionMap VALUES (1013, 1, 2, 5);
-- INSERT INTO RolePermissionMap VALUES (1014, 1, 2, 8);
-- INSERT INTO RolePermissionMap VALUES (1015, 1, 2, 11);
-- INSERT INTO RolePermissionMap VALUES (1016, 1, 2, 12);
-- INSERT INTO RolePermissionMap VALUES (1017, 1, 2, 101);
-- INSERT INTO RolePermissionMap VALUES (1018, 1, 2, 102);
-- INSERT INTO RolePermissionMap VALUES (1019, 2, 2, 1);
-- INSERT INTO RolePermissionMap VALUES (1020, 2, 2, 2);
-- INSERT INTO RolePermissionMap VALUES (1021, 2, 2, 3);
-- INSERT INTO RolePermissionMap VALUES (1022, 2, 2, 5);
-- INSERT INTO RolePermissionMap VALUES (1023, 2, 2, 8);
-- INSERT INTO RolePermissionMap VALUES (1024, 2, 2, 11);
-- INSERT INTO RolePermissionMap VALUES (1025, 2, 2, 12);
-- INSERT INTO RolePermissionMap VALUES (1026, 2, 2, 101);
-- INSERT INTO RolePermissionMap VALUES (1027, 2, 2, 102);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1010,-1,2,50);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1011,-1,2,51);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1012,-1,2,52);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1013,-1,2,4);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1014,-1,2,7);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1015,-1,2,10);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1016,-1,2,15);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1017,-1,2,49);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1018,-1,2,53);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1019,-1,2,54);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1020,-1,2,55);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1021,-1,2,56);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1022,-1,2,57);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1023,-1,2,58);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1024,-1,2,59);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1025,-1,2,60);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1026,-1,2,65);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1027,-1,2,66);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1028,-1,2,67);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1029,-1,2,80);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1030,-1,2,81);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1031,-1,2,82);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1032,-1,2,83);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1033,-1,2,84);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1034,-1,2,85);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1035,-1,2,86);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1036,-1,2,87);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1037,-1,2,88);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1038,-1,2,104);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1039,-1,2,105);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1040,-1,2,106);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1041,-1,2,107);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1042,-1,2,108);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1043,-1,2,109);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1044,-1,2,110);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1045,-1,2,111);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1046,-1,2,112);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1047,-1,2,113);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1048,-1,2,114);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1049,-1,2,115);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(1050,-1,2,116);


INSERT INTO RolePermissionMap VALUES (1500, -1, 5, 600);
INSERT INTO RolePermissionMap VALUES (1501, -1, 5, 601);
INSERT INTO RolePermissionMap VALUES (1502, -1, 5, 602);
INSERT INTO RolePermissionMap VALUES (1503, -1, 5, 603);
INSERT INTO RolePermissionMap VALUES (1504, -1, 5, 604);
INSERT INTO RolePermissionMap VALUES (1505, -1, 5, 605);
INSERT INTO RolePermissionMap VALUES (1506, -1, 5, 606);
INSERT INTO RolePermissionMap VALUES (1507, -1, 5, 607);
INSERT INTO RolePermissionMap VALUES (1509, -1, 5, 608);
INSERT INTO RolePermissionMap VALUES (1600, -1, 8, -1);
INSERT INTO RolePermissionMap VALUES (1508, -1, 10, -1);
-- INSERT INTO RolePermissionMap VALUES (1701, -1, 5, 609);

-- 区域权限
insert into region (RegionId, RegionName, Description) values(-1, 'All Region Permission Group', 'Has All Region And Equipment Permission');


-- 菜单权限
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(201,'-1','6','201');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(202,'-1','6','202');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(203,'-1','6','203');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(204,'-1','6','204');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(205,'-1','6','205');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(206,'-1','6','206');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(207,'-1','6','207');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(208,'-1','6','208');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(209,'-1','6','209');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(210,'-1','6','210');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(211,'-1','6','211');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(212,'-1','6','212');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(213,'-1','6','213');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(214,'-1','6','214');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(215,'-1','6','215');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(216,'-1','6','216');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(217,'-1','6','217');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(218,'-1','6','218');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(219,'-1','6','219');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(220,'-1','6','220');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(221,'-1','6','221');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(222,'-1','6','222');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(223,'-1','6','223');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(224,'-1','6','224');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(225,'-1','6','225');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(226,'-1','6','226');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(227,'-1','6','227');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(228,'-1','6','228');
-- insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(229,'-1','6','229');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(230,'-1','6','230');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(231,'-1','6','231');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(232,'-1','6','232');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(233,'-1','6','233');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(234,'-1','6','234');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(235,'-1','6','235');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(236,'-1','6','236');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(237,'-1','6','237');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(238,'-1','6','238');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(239,'-1','6','239');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(240,'-1','6','240');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(241,'-1','6','241');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(242,'-1','6','242');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(243,'-1','6','243');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(244,'-1','6','244');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(245,'-1','6','245');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(247,'-1','6','247');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(248,'-1','6','248');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(249,'-1','6','249');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(250,'-1','6','250');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(251,'-1','6','251');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(252,'-1','6','252');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(253,'-1','6','253');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(254,'-1','6','254');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(255,'-1','6','255');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(256,'-1','6','256');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(257,'-1','6','257');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(258,'-1','6','258');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(259,'-1','6','259');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(260,'-1','6','260');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(261,'-1','6','261');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(262,'-1','6','262');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(263,'-1','6','263');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(264,'-1','6','264');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(265,'-1','6','265');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(266,'-1','6','266');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(267,'-1','6','267');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(268,'-1','6','268');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(269,'-1','6','269');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(270,'-1','6','270');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(271,'-1','6','271');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(272,'-1','6','272');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(273,'-1','6','273');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(274,'-1','6','274');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(275,'-1','6','275');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(276,'-1','6','276');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(277,'-1','6','277');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(278,'-1','6','278');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(279,'-1','6','279');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(280,'-1','6','280');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(281,'-1','6','281');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(282,'-1','6','282');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(283,'-1','6','283');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(284,'-1','6','284');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(285,'-1','6','285');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(286,'-1','6','286');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(287,'-1','6','287');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(288,'-1','6','288');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(289,'-1','6','289');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(290,'-1','6','290');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(291,'-1','6','291');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(292,'-1','6','292');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(293,'-1','6','293');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(294,'-1','6','294');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(296,'-1','6','296');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(297,'-1','6','297');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(298,'-1','6','298');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(299,'-1','6','299');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(300,'-1','6','300');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(301,'-1','6','301');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(302,'-1','6','302');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(303,'-1','6','303');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(304,'-1','6','304');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(305,'-1','6','305');
-- insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(306,'-1','6','306');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(307,'-1','6','307');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(308,'-1','6','308');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(309,'-1','6','309');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(310,'-1','6','310');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(311,'-1','6','311');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(312,'-1','6','312');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(313,'-1','6','313');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(314,'-1','6','314');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(315,'-1','6','315');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(316,'-1','6','316');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(317,'-1','6','317');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(318,'-1','6','318');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(319,'-1','6','319');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(320,'-1','6','320');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(321,'-1','6','321');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(322,'-1','6','322');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(323,'-1','6','323');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(324,'-1','6','324');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(325,'-1','6','325');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(326,'-1','6','326');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(327,'-1','6','327');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(328,'-1','6','328');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(329,'-1','6','329');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(330,'-1','6','330');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(331,'-1','6','331');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(332,'-1','6','332');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(333,'-1','6','333');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(334,'-1','6','334');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(335,'-1','6','335');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(336,'-1','6','336');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(337,'-1','6','337');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(338,'-1','6','338');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(339,'-1','6','339');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(340,'-1','6','340');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(341,'-1','6','341');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(342,'-1','6','342');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(343,'-1','6','343');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(344,'-1','6','344');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(345,'-1','6','345');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(347,'-1','6','347');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(348,'-1','6','348');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(349,'-1','6','349');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(350,'-1','6','350');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(351,'-1','6','351');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(353,'-1','6','353');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(354,'-1','6','354');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(355,'-1','6','355');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(356,'-1','6','356');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(357,'-1','6','357');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(358,'-1','6','358');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(359,'-1','6','359');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(360,'-1','6','360');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(361,'-1','6','361');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(362,'-1','6','362');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(363,'-1','6','363');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(364,'-1','6','364');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(365,'-1','6','365');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(366,'-1','6','366');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(367,'-1','6','367');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(368,'-1','6','368');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(369,'-1','6','369');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(370,'-1','6','370');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(371,'-1','6','371');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(372,'-1','6','372');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(373,'-1','6','373');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(374,'-1','6','374');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(375,'-1','6','375');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(376,'-1','6','376');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(377,'-1','6','377');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(378,'-1','6','378');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(379,'-1','6','396');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(380,'-1','6','397');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(381,'-1','6','363');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(382,'-1','6','364');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(383,'-1','6','365');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(384,'-1','6','366');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(385,'-1','6','367');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(386,'-1','6','368');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(387,'-1','6','369');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(388,'-1','6','370');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(389,'-1','6','371');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(390,'-1','6','372');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(391,'-1','6','398');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(392,'-1','6','399');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(393,'-1','6','400');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(394,'-1','6','401');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(395,'-1','6','402');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(396,'-1','6','403');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(397,'-1','6','404');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(398,'-1','6','405');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(399,'-1','6','406');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(400,'-1','6','407');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(401,'-1','6','408');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(402,'-1','6','409');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(403,'-1','6','410');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(404,'-1','6','411');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(405,'-1','6','412');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(406,'-1','6','413');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(407,'-1','6','414');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(408,'-1','6','415');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(409,'-1','6','416');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(410,'-1','6','417');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(411,'-1','6','418');
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(412,'-1','6','419');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(413,'-1','6','425');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(414,'-1','6','426');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(415,'-1','6','427');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(416,'-1','6','428');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(417,'-1','6','429');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(418,'-1','6','430');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(419,'-1','6','431');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(420,'-1','6','381');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(421,'-1','6','384');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(422,'-1','6','387');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(423,'-1','6','388');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(424,'-1','6','389');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(425,'-1','6','391');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(426,'-1','6','393');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(427,'-1','6','432');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(428,'-1','6','434');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(429,'-1','6','435');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(430,'-1','6','436');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(431,'-1','6','437');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(432,'-1','6','438');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(433,'-1','6','439');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(435,'-1','6','444');
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(436,'-1','6','445');

-- AI空调
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(900, -1, 6, 900);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(901, -1, 6, 901);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(902, -1, 6, 902);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(903, -1, 6, 903);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(904, -1, 6, 904);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(905, -1, 6, 905);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(906, -1, 6, 906);

-- AI空调
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(907, -1, 6, 907);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(908, -1, 6, 908);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(909, -1, 6, 909);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(910, -1, 6, 910);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(911, -1, 6, 911);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(912, -1, 6, 912);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(913, -1, 6, 913);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(914, -1, 6, 914);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(915, -1, 6, 915);


INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(800, -1, 6, 800);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(801, -1, 6, 801);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(802, -1, 6, 802);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(803, -1, 6, 803);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(804, -1, 6, 804);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(805, -1, 6, 805);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(806, -1, 6, 806);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(809, -1, 6, 809);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(810, -1, 6, 810);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(811, -1, 6, 811);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(812, -1, 6, 812);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(813, -1, 6, 813);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(814, -1, 6, 814);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(815, -1, 6, 815);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(816, -1, 6, 816);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(817, -1, 6, 817);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(818, -1, 6, 818);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(819, -1, 6, 819);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(820, -1, 6, 820);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(821, -1, 6, 821);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(822, -1, 6, 822);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(823, -1, 6, 823);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(824, -1, 6, 824);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(825, -1, 6, 825);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(826, -1, 6, 826);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(827, -1, 6, 827);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(828, -1, 6, 828);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(829, -1, 6, 829);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(830, -1, 6, 830);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(831, -1, 6, 831);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(832, -1, 6, 832);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(833, -1, 6, 833);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(834, -1, 6, 834);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(835, -1, 6, 835);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(836, -1, 6, 836);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(837, -1, 6, 837);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(838, -1, 6, 838);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(839, -1, 6, 839);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(840, -1, 6, 840);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(841, -1, 6, 841);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(842, -1, 6, 842);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(843, -1, 6, 843);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(844, -1, 6, 844);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(845, -1, 6, 845);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(846, -1, 6, 846);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(847, -1, 6, 847);
insert into rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) values(870, -1, 6, 950);

INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(1205, -1, 6, 441);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(1206, -1, 6, 442);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(1207, -1, 6, 443);

INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(1208, -1, 6, 848);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(1209, -1, 6, 849);
INSERT INTO rolepermissionmap (RolePermissionMapId, RoleId, PermissionCategoryId, PermissionId) VALUES(1210, -1, 6, 850);