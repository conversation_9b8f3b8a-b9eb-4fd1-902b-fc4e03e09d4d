package com.siteweb.computerrack.manager;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.computerrack.dto.ComputerRackOpenCountDTO;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ComputerRackSignalRecord;
import com.siteweb.computerrack.enumeration.DateTypeEnum;
import com.siteweb.computerrack.mapper.ComputerRackMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

/**
 * 机架信号记录InfluxDB服务实现类
 */
@Service
@Slf4j
public class ComputerRackSignalRecordManage {
    @Autowired
    InfluxDBManager influxDBManager;
    @Autowired
    ComputerRackMapper computerRackMapper;

    /**
     * 获取机架开通数量
     *
     * @param resourceStructureIdList 层级ID列表
     * @param startTime               开始时间
     * @param endTime                 结束时间
     * @return 机架开通数量DTO列表
     */
    public List<ComputerRackOpenCountDTO> getRackOpenCount(List<Integer> resourceStructureIdList, Date startTime, Date endTime) {
        // 结果列表
        List<ComputerRackOpenCountDTO> result = new ArrayList<>();
        // 获取指定层级下的所有机架
        Map<Integer, List<ComputerRack>> racksByResourceStructureMap = computerRackMapper.selectList(Wrappers.lambdaQuery(ComputerRack.class)
                        .in(ComputerRack::getResourceStructureId, resourceStructureIdList)).stream()
                .collect(Collectors.groupingBy(ComputerRack::getResourceStructureId));
        // 如果没有机架，直接返回空结果
        if (racksByResourceStructureMap.isEmpty()) {
            return result;
        }
        for (Map.Entry<Integer, List<ComputerRack>> entry : racksByResourceStructureMap.entrySet()) {
            Integer resourceStructureId = entry.getKey();
            List<ComputerRack> racks = entry.getValue();
            ComputerRackOpenCountDTO dto = new ComputerRackOpenCountDTO();
            dto.setResourceStructureId(resourceStructureId);
            // 机架总数
            dto.setRackCount(racks.size());
            // 获取开通机架，获取每个机架最新的记录
            String influxql = """
                    SELECT last(openValue) as openValue, computerRackId, resourceStructureId
                    FROM computerracksignalrecord
                    WHERE time >= $startTime AND time <= $endTime
                    AND resourceStructureId = $resourceStructureId
                    GROUP BY computerRackId
                    """;
            // 设置查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("startTime", DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN));
            params.put("endTime", DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN));
            params.put("resourceStructureId", resourceStructureId.toString());
            List<ComputerRackSignalRecord> computerRackSignalRecords = influxDBManager.list(influxql, ComputerRackSignalRecord.class, params);
            dto.setOpenCount(computerRackSignalRecords.stream().filter(r -> Boolean.TRUE.equals(r.getOpenValue())).count());
            result.add(dto);
        }
        return result;
    }

    /**
     * 根据机架ID和时间范围获取机架信号记录
     *
     * @param rackId    机架ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 机架信号记录列表
     */
    public List<ComputerRackSignalRecord> getComputerRackSignalRecords(Integer rackId, Date startTime, Date endTime) {
        String influxql = """
                SELECT *
                FROM computerracksignalrecord
                WHERE time >= $startTime AND time <= $endTime
                AND computerRackId = $computerRackId
                """;
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN));
        params.put("endTime", DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN));
        params.put("computerRackId", rackId.toString());
        return influxDBManager.list(influxql, ComputerRackSignalRecord.class, params);
    }

    /**
     * 根据日期类型获取机架功率趋势数据
     *
     * @param rackId    机架ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param dateType  日期类型
     * @return 按日期类型分组的机架信号记录Map
     */
    public Map<Object, ComputerRackSignalRecord> getComputerRackPowerTrendByDateType(Integer rackId, Date startTime, Date endTime, DateTypeEnum dateType) {
        List<ComputerRackSignalRecord> records = getComputerRackSignalRecords(rackId, startTime, endTime);
        switch (dateType) {
            case QUARTERLY:
                // 按季度分组，获取每季度最新的数据
                return records.stream()
                        .collect(Collectors.toMap(
                                k -> getQuarter(k.getTime()),
                                v -> v,
                                BinaryOperator.maxBy(Comparator.comparing(ComputerRackSignalRecord::getTime)))
                        );
            case QUARTER_DAY:
                // 按日期分组，获取每天最新的数据
                return records.stream()
                        .collect(Collectors.toMap(
                                k -> getLocalDateFromTimeString(k.getTime()),
                                v -> v,
                                BinaryOperator.maxBy(Comparator.comparing(ComputerRackSignalRecord::getTime)))
                        );
            default:
                int monthDifference = getMonthDifference(startTime, endTime);
                if (monthDifference == 0) {
                    // 在同一个月内，按天分组
                    return records.stream()
                            .collect(Collectors.toMap(
                                    k -> getDayOfMonth(k.getTime()),
                                    v -> v,
                                    BinaryOperator.maxBy(Comparator.comparing(ComputerRackSignalRecord::getTime)))
                            );
                } else {
                    // 跨月，按月分组
                    return records.stream()
                            .collect(Collectors.toMap(
                                    k -> getMonth(k.getTime()) + 1,
                                    v -> v,
                                    BinaryOperator.maxBy(Comparator.comparing(ComputerRackSignalRecord::getTime)))
                            );
                }
        }
    }

    /**
     * 从时间字符串中获取季度
     */
    private int getQuarter(String timeString) {
        Date date = parseTimeString(timeString);
        return DateUtil.quarter(date);
    }

    /**
     * 从时间字符串中获取月份
     */
    private int getMonth(String timeString) {
        Date date = parseTimeString(timeString);
        return DateUtil.month(date);
    }

    /**
     * 从时间字符串中获取日
     */
    private int getDayOfMonth(String timeString) {
        Date date = parseTimeString(timeString);
        return DateUtil.dayOfMonth(date);
    }

    /**
     * 从时间字符串中获取LocalDate
     */
    private LocalDate getLocalDateFromTimeString(String timeString) {
        Date date = parseTimeString(timeString);
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 解析InfluxDB时间字符串为Date对象
     */
    private Date parseTimeString(String timeString) {
        try {
            // InfluxDB时间格式通常为ISO 8601格式，例如：2023-01-01T12:00:00Z
            return DateUtil.parse(timeString);
        } catch (Exception e) {
            log.error("Error parsing time string: {}", timeString, e);
            return new Date();
        }
    }

    /**
     * 比较两个时间字符串的先后顺序
     *
     * @return 正数表示time1晚于time2，负数表示time1早于time2，0表示相等
     */
    private int compareTime(String time1, String time2) {
        Date date1 = parseTimeString(time1);
        Date date2 = parseTimeString(time2);
        return date1.compareTo(date2);
    }

    /**
     * 计算两个日期之间的月份差
     */
    private int getMonthDifference(Date date1, Date date2) {
        return DateUtil.month(date2) - DateUtil.month(date1);
    }
}
