package com.siteweb.as.controller.expert.advice;

import com.siteweb.as.manager.StandardDicEventASManager;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.entity.StandardDicEvent;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.StandardVerService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * /api/expertAdvice/BaseEquipment 替换成 /api/equipmentbasetypes
 * @Author: lzy
 * @Date: 2023/5/22 17:22
 */

@RestController
@RequestMapping({"/api/expertadvice"})
public class StationCategoryController {

    @Autowired
    StandardDicEventASManager standardDicEventASManager;
    @Autowired
    StandardVerService standardVerService;
    @Autowired
    private DataItemService dataItemService;
    @ApiOperation("获取基类站点")
    @GetMapping("/stationcategroy")
    public ResponseEntity<ResponseResult> getStationCategroy() {
        // 获取所有标准字典事件
        List<StandardDicEvent> list = standardDicEventASManager.getAll();
        // 获取当前标准版本
        int standardVer = standardVerService.getStandardVer();
        // 过滤出符合当前标准版本的站点类别，并去重
        Set<Integer> stationCategorySet = list.stream()
                                              .filter(o -> Objects.equals(o.getStandardType(), standardVer))
                                              .map(StandardDicEvent::getStationCategory)
                                              .collect(Collectors.toSet());
        // 获取站点类别数据项
        List<DataItem> stationCategoryList = dataItemService.findByEntryId(DataEntryEnum.STATION_CATEGORY.getValue());
        // 将数据项转换为Map，便于快速查找
        Map<Integer, String> itemMap = stationCategoryList.stream()
                                                          .collect(Collectors.toMap(DataItem::getItemId, DataItem::getItemValue));
        // 构建响应结果
        List<IdValueDTO<Integer, String>> result = stationCategorySet.stream()
                                                                     .filter(itemMap::containsKey)
                                                                     .map(id -> new IdValueDTO<>(id, itemMap.get(id)))
                                                                     .toList();
        // 返回成功响应
        return ResponseHelper.successful(result);
    }
}
