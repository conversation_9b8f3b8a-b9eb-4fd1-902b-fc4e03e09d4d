package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.MenuItemDTO;
import com.siteweb.admin.dto.RouterRedirectDTO;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.entity.MenuItemStructureMap;
import com.siteweb.admin.entity.SceneMenuProfileMap;
import com.siteweb.admin.mapper.MenuItemMapper;
import com.siteweb.admin.service.*;
import com.siteweb.admin.vo.MenuItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class MenuItemServiceImpl implements MenuItemService {

    /**
     * 嵌套踢路由的固定跳转前缀
     */
    private static final String ENTRY_EMBED_IFRAME_TARGET = "entry/embediframe?target=";
    @Autowired
    MenuItemMapper menuItemMapper;
    @Lazy
    @Autowired
    MenuStructureService menuStructureService;
    @Autowired
    private MenuItemStructureMapService menuItemStructureMapService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    SceneMenuProfileMapService sceneMenuProfileMapService;
    @Override
    public List<MenuItem> findMenuItemsWithChildren() {
        return menuItemMapper.findAllMenuItems();
    }

    @Override
    public List<MenuItem> findMenuItemList() {
        return menuItemMapper.selectList(null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int createMenuItem(MenuItemVO menuItemVO) {
        MenuItem menuItem = BeanUtil.toBean(menuItemVO, MenuItem.class);
        if (ObjectUtil.isNotNull(menuItemVO.getMenuItemId())) {
            //删除老权限点 当前端传ParentId字段为1时代表移除当前节点
            if (ObjectUtil.equals(menuItemVO.getParentId(), 1)) {
                this.deletePermission(menuItemVO);
            }
            return  menuItemMapper.updateById(menuItem);
        }
        SceneMenuProfileMap sceneMenuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuItemVO.getMenuProfileId());
        menuItem.setFeatureId(1);
        menuItem.setDescription(sceneMenuProfileMap.getSceneId().toString());
        return menuItemMapper.insert(menuItem);
    }
    @Override
    public int deleteById(Integer menuItemId) {
        return menuItemMapper.deleteById(menuItemId);
    }

    @Override
    public int updateMenuItem(MenuItem menuItem) {
        return menuItemMapper.updateById(menuItem);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateMenuItem(List<MenuItemVO> menuItemVOList) {
        for (MenuItemVO menuItemVO : menuItemVOList) {
            //更新菜单
            MenuItem menuItem = BeanUtil.toBean(menuItemVO, MenuItem.class);
            this.updateMenuItem(menuItem);
            //添加新权限点
            if (ObjectUtil.isNotNull(menuItemVO.getMenuProfileId())) {
                this.createPermission(menuItemVO);
            }
        }
        return menuItemVOList.size();
    }

    private void createPermission(MenuItemVO menuItemVO) {
        String fullId = menuStructureService.findFullId(menuItemVO.getMenuProfileId(), menuItemVO.getMenuItemId());
        String fullTitle = menuStructureService.findFullTitle(menuItemVO.getMenuProfileId(), menuItemVO.getMenuItemId());
        this.deletePermission(menuItemVO);
        permissionService.createMenuPermission(menuItemVO.getMenuProfileId(),fullId, fullTitle);
    }

    private void deletePermission(MenuItemVO menuItemVO) {
        String fullId = menuStructureService.findFullId(menuItemVO.getMenuProfileId(), menuItemVO.getMenuItemId());
        permissionService.deleteMenuPermissionByFullId(menuItemVO.getMenuProfileId(), fullId);
    }

    @Override
    public MenuItem findById(Integer menuItemId) {
        return menuItemMapper.findMenuItemById(menuItemId);
    }

    @Override
    public List<MenuItem> findByParentId(Integer parentId) {
        return menuItemMapper.selectList(Wrappers.lambdaQuery(MenuItem.class)
                                                 .eq(MenuItem::getParentId, parentId));
    }

    @Override
    public String findTopMenuItemPath() {
        return menuItemMapper.findTopMenuItemPath();
    }

    @Override
    public List<MenuItemDTO> getAllFirstLevelMenuItems(Integer menuProfileId) {
        //获取所有一级菜单
        SceneMenuProfileMap sceneMenuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
        //场景是电信全查,场景是IDC只查IDC
        List<MenuItem> menuItemList = menuItemMapper.findBySceneIdAndParentId(sceneMenuProfileMap.getSceneId(), 1);
        List<MenuItemStructureMap> menuItemStructureMaps = menuItemStructureMapService.findByMenuProfileId(menuProfileId);
        boolean menuItemStructureMapEmptyFlag = CollUtil.isEmpty(menuItemStructureMaps);
        if (menuItemStructureMapEmptyFlag) {
            return BeanUtil.copyToList(menuItemList, MenuItemDTO.class);
        }
        List<MenuItemDTO> menuItemDTOList = new ArrayList<>();
        for (MenuItem menuItem : menuItemList) {
            boolean exist = menuItemStructureMaps.stream()
                                                 .anyMatch(oc -> ObjectUtil.equals(oc.getMenuItemId(), menuItem.getMenuItemId()));
            if (!exist) {
                MenuItemDTO menuItemDTO = BeanUtil.toBean(menuItem, MenuItemDTO.class);
                menuItemDTOList.add(menuItemDTO);
            }
        }
        return menuItemDTOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateMenuItemSort(List<MenuItemVO> menuItemVOList) {
        for (MenuItemVO menuItemVO : menuItemVOList) {
            menuItemMapper.update(null, Wrappers.lambdaUpdate(MenuItem.class)
                                                     .set(MenuItem::getLayoutPosition, menuItemVO.getLayoutPosition())
                                                     .eq(MenuItem::getMenuItemId, menuItemVO.getMenuItemId()));
        }
        return menuItemVOList.size();
    }

    @Override
    public RouterRedirectDTO findMenuItemAllPath(Integer id) {
        MenuItem menuItem = this.findById(id);
        RouterRedirectDTO redirectDTO = new RouterRedirectDTO(menuItem.getIsEmbed(), menuItem.getIsExternalWeb());
        String redirectPath = getRedirectPath(menuItem);
        redirectDTO.setPath(redirectPath);
        return redirectDTO;
    }


    /**
     * 获取路由跳转的path
     * @param menuItem 菜单信息
     * @return {@link StringBuilder }
     */
    private String getRedirectPath(MenuItem menuItem) {
        //嵌入的话逻辑不同
        if (Boolean.TRUE.equals(menuItem.getIsEmbed())) {
            return ENTRY_EMBED_IFRAME_TARGET + menuItem.getPath();
        }
        //其他菜单
        StringBuilder sb = new StringBuilder(menuItem.getPath());
        while (ObjectUtil.notEqual(menuItem.getParentId(), 0)) {
            menuItem = this.findById(menuItem.getParentId());
            if (CharSequenceUtil.isBlank(menuItem.getPath())) {
                continue;
            }
            sb.insert(0, menuItem.getPath() + "/");
        }
        return sb.toString();
    }
}
