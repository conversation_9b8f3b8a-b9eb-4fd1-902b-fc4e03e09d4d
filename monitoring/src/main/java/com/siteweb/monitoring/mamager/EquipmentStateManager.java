package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.RedisOnlineStateEnum;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.EquipmentMaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 设备状态管理类
 */
@Component
public class EquipmentStateManager {
    /**
     * 自诊断设备的类型
     */
    @Value("${self-diagnosis.equipment-category:99}")
    private Integer SELF_DIAGNOSTICS;
    private final Logger log = LoggerFactory.getLogger(EquipmentStateManager.class);

    @Autowired
    EquipmentMapper equipmentMapper;

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    EquipmentMaskService equipmentMaskService;
    @Autowired
    ConnectStateManager connectStateManager;

    /**
     * 设备状态对象队列
     */
    private   ConcurrentHashMap<Integer, EquipmentState> EQUIPMENTSTATE_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    private boolean initialized = false;

    @PostConstruct
    public void init() {
        List<EquipmentState> equipmentList = loadEquipmentsFromDB();
        initialized = true;
        log.info("Init equipment state from database, total {}", equipmentList.size());
    }

    /**
     * 从数据库获取设备状态
     * @return
     */
    @Scheduled(fixedDelay = 30 * 1000)
    private List<EquipmentState> loadEquipmentsFromDB() {
        List<EquipmentState> equipmentList = equipmentMapper.getEquipmentState();
        Map<Integer, OnlineState> equipmentConnectStateByIds = connectStateManager.findConnectStateByIds(equipmentList.stream()
                                                                                                           .map(EquipmentState::getEquipmentId)
                                                                                                           .toList(), RedisOnlineStateEnum.EQUIPMENT);
        Set<Integer> intervalMaskEquipmentIds = equipmentMaskService.findIntervalMaskEquipmentIds();
        for (EquipmentState equipment : equipmentList) {
            //是否被分时段屏蔽
            if (intervalMaskEquipmentIds.contains(equipment.getEquipmentId())) {
                equipment.setMasked(Boolean.TRUE);
            }
            //redis中从在值才从redis中取，否则依旧从数据库中取，避免与dataHub版本不一致
            //equipment.getEquipmentCategory() = 99 代表是自诊断设备，需要从数据库中获取在线状态
            if (equipmentConnectStateByIds.containsKey(equipment.getEquipmentId()) && !Objects.equals(equipment.getEquipmentCategory(), SELF_DIAGNOSTICS)) {
                equipment.setOnlineState(equipmentConnectStateByIds.get(equipment.getEquipmentId()));
            }
            EQUIPMENTSTATE_CONCURRENT_HASH_MAP.put(equipment.getEquipmentId(), equipment);
        }
        clearEquipmentState(equipmentList.stream().map(EquipmentState::getEquipmentId).collect(Collectors.toSet()));
        return equipmentList;
    }

    /**
     * 清理被删除的设备
     * 避免被删除的设备一直存在内存中
     * @param allEquipmentIds 所有设备的id
     */
    private void clearEquipmentState(Set<Integer> allEquipmentIds) {
        if (!initialized) {
            return;
        }
        //获取差集，移除被删除的设备的缓存
        Collection<Integer> disjunction = CollUtil.subtract(new HashSet<>(EQUIPMENTSTATE_CONCURRENT_HASH_MAP.keySet()), allEquipmentIds);
        disjunction.forEach(equipmentId-> EQUIPMENTSTATE_CONCURRENT_HASH_MAP.remove(equipmentId));
    }

    /**
     * 根据设备Id获取设备状态
     * @param equipmentId
     * @return
     */
    public EquipmentState getEquipmentStateById(Integer equipmentId){
        return EQUIPMENTSTATE_CONCURRENT_HASH_MAP.get(equipmentId);
    }

    /**
     * 批量获取设备状态下信息
     * @param equipmentIds 设备Id列表
     * @return 状态列表
     */
    public HashMap<Integer, EquipmentState> getEquipmentStateByIds(List<Integer> equipmentIds){
        HashMap<Integer, EquipmentState> result = new HashMap<>();
        for(Integer equipmentId:equipmentIds){
            EquipmentState equipmentState = getEquipmentStateById(equipmentId);
            if(equipmentState != null){
                result.put(equipmentId, equipmentState);
            }
        }
        return  result;
    }

    /**
     * 获取设备的屏蔽状态
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean getEquipmentMaskStatesById(Integer equipmentId){
        EquipmentState equipmentState = getEquipmentStateById(equipmentId);
        if (equipmentState != null) {
            return equipmentState.getMasked();
        }
        return false;
    }
    /**
     * 获取设备的在线状态-后期改用从redis获取
     * @param equipmentId
     * @return
     */
    public OnlineState getEquipmentOnlineStateById(Integer equipmentId){
        EquipmentState equipmentState = getEquipmentStateById(equipmentId);
        if(equipmentState != null){
            return  equipmentState.getOnlineState();
        }
        return  OnlineState.ONLINE;
    }

    /**
     * 获取设备的告警状态 后期可考虑从ActiveEventManger获取
     * @param equipmentId
     * @return
     */
    public AlarmState getEquipmentAlarmStateById(Integer equipmentId){
        return  activeEventManager.existsActiveByEquipmentId(equipmentId);
    }

    /**
     * 获取所有存在告警的设备id
     * @return {@link Set}<{@link Integer}> 设备idSet
     */
    public Set<Integer> getAllEquipmentAlarmState() {
        return activeEventManager.queryAllActiveEvents()
                                 .stream()
                                 .filter(activeEvent -> ObjectUtil.isNull(activeEvent.getEndTime()))
                                 .map(ActiveEvent::getEquipmentId)
                                 .collect(Collectors.toSet());
    }

    public int getEquipmentCount(){
        return EQUIPMENTSTATE_CONCURRENT_HASH_MAP.size();
    }

    public int getMaskedEquipmentCount(){
        return (int)EQUIPMENTSTATE_CONCURRENT_HASH_MAP.values().stream().filter(e->e.getMasked()).count();
    }

    public int getProjectEquipmentCount(){
        return (int)EQUIPMENTSTATE_CONCURRENT_HASH_MAP.values().stream().filter(e->e.getOnProject()).count();
    }

    /**
     * 获取设备中断数量
     * @return int 中断的设备数
     */
    public int getInterruptEquipmentCount() {
        return (int) EQUIPMENTSTATE_CONCURRENT_HASH_MAP.values()
                                                       .stream()
                                                       .filter(e -> ObjectUtil.notEqual(e.getOnlineState(), OnlineState.ONLINE))
                                                       .count();
    }
    /**
     * 获取设备的最大告警等级
     */
    public Map<Integer,Integer> getMaxEventLevelByEquipmentIds(Collection<Integer> equipments) {
        if (ObjectUtil.isNull(equipments)){
            return MapUtil.empty();
        }
        return activeEventManager.queryAllActiveEvents().stream()
                .filter(active -> equipments.contains(active.getEquipmentId()) && ObjectUtil.isNull(active.getEndTime()) && ObjectUtil.isNotNull(active.getEventLevel()))
                .collect(Collectors.toMap(ActiveEvent::getEquipmentId, ActiveEvent::getEventLevel, Math::min));
    }
}
