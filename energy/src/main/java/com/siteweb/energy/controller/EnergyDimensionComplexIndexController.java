package com.siteweb.energy.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.energy.dto.ComplexIndexDTO;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.service.EnergyDimensionComplexIndexService;
import com.siteweb.energy.service.impl.EnergyDimensionComplexIndexServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 能耗多维度 指标 相关接口
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyDimensionComplexIndexController",tags = "能耗多维度指标相关接口")
public class EnergyDimensionComplexIndexController {
    private final Logger log = LoggerFactory.getLogger(EnergyDimensionComplexIndexController.class);
    private static final String ENTITY_NAME = "ComplexIndex";

    @Autowired
    private EnergyDimensionComplexIndexService dimensionComplexIndexService;
    @Autowired
    private ComplexIndexService complexIndexService;

    @ApiOperation(value = "获得能耗多维度指标相关业务分类")
    @GetMapping(value = "/energybusinesstree",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergybusinesstree() {
        ResultObject<ComplexIndexBusinessType> result = dimensionComplexIndexService.getEnergybusinesstree();
        return responseOK(result);
    }

    @ApiOperation(value = "获得碳元素能耗类型")
    @GetMapping(value = "/energybusinesstreenotc",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergybusinesstreenotc() {
        ResultObject<ComplexIndexBusinessType> result = dimensionComplexIndexService.getEnergybusinesstreenotc();
        return responseOK(result);
    }

    @ApiOperation(value = "获得能耗多维度指标相关指标定义")
    @GetMapping(value = "/complexindexdefinition",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexindexdefinition() {
        ResultObject<Map<Integer, List<ComplexIndexDefinition>>> result = dimensionComplexIndexService.getComplexindexdefinition();
        return responseOK(result);
    }

    @ApiOperation(value = "通过objectId和objectTypeId得到对应的所有能耗相关指标")
    @GetMapping(value = "/complexindexs",
            params = {"objectId", "objectTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ComplexIndex> findEnergyComplexindexByResourceStructureIdAndType(@RequestParam(value = "objectId",required =true) Integer objectId,
                                                                  @RequestParam(value = "objectTypeId",required =true) Integer objectTypeId) {
        return  dimensionComplexIndexService.findEnergyComplexindexByResourceStructureIdAndType(objectId, objectTypeId);
    }

    @ApiOperation(value = "批量保存能耗相关指标")
    @PutMapping(value = "/complexindexs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateComplexindex(@Valid @RequestBody ComplexIndexDTO complexIndexDTO) {
        //ComplexIndexDTO result0 = complexIndexService.batchComplexIndexs(complexIndexDTO);
        if(complexIndexDTO == null) {
            return responseOK(new ResultObject<String>("Param is null", -1));
        }
        if(complexIndexDTO.getDimensionTypeId() == null) {
            return responseOK(new ResultObject<String>("DimensionTypeId is null", -1));
        }
        if(complexIndexDTO.getObjectId() == null) {
            return responseOK(new ResultObject<String>("ObjectId is null", -1));
        }
        if(complexIndexDTO.getObjectTypeId() == null) {
            return responseOK(new ResultObject<String>("ObjectTypeId is null", -1));
        }
        if(complexIndexDTO.getObjectIdType() == null) {
            return responseOK(new ResultObject<String>("ObjectIdType is null", -1));
        }
        ResultObject<String> result = dimensionComplexIndexService.batchComplexIndexs(complexIndexDTO);
        return responseOK(result);
    }

    @ApiOperation(value = "获取所有指标记录")
    @GetMapping(value = "/complexindexlist",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findAllComplexindexByObjectTypeId(@RequestParam(value = "objectTypeId",required=false) Integer objectTypeId) {
        List<ComplexIndex> list = dimensionComplexIndexService.findAllComplexindexByObjectTypeId(objectTypeId);
        return  ResponseHelper.successful(list);
    }

    @ApiOperation(value = "根据能源类型和指标分类获取指标定义")
    @GetMapping(value = "/complexindexdefinition",
            params = {"businessTypeId", "complexIndexDefinitionTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexIndexDefinition(Integer businessTypeId,String complexIndexDefinitionTypeId ) {
        return ResponseHelper.successful(dimensionComplexIndexService.getComplexIndexDefinition(businessTypeId,complexIndexDefinitionTypeId));
    }
    @Operation(summary = "根据用户权限获取能源类型和指标分类获取指标定义")
    @GetMapping(value = "/complexindexdefinitionByUser",
            params = {"businessTypeId", "complexIndexDefinitionTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexIndexDefinitionByUser(Integer businessTypeId,String complexIndexDefinitionTypeId ) {
        return ResponseHelper.successful(dimensionComplexIndexService.getComplexIndexDefinitionByUser(businessTypeId,complexIndexDefinitionTypeId));
    }

    private ResponseEntity<ResponseResult> responseOK(ResultObject resultObject) {
        ResponseResult result = new ResponseResult();
        result.setState(true);
        result.setData(resultObject);
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(String.valueOf(ErrorCode.NORMAL.value()));
        return new ResponseEntity(result, HttpStatus.OK);
    }

}
