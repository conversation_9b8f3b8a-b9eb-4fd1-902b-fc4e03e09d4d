package com.siteweb.eventnotification.textnotify;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TextNotifyManager {
    private static final int HEALTH_INTERVAL = 180;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ListableBeanFactory beanFactory;

    // 缓存通知服务Map，避免重复获取
    private volatile Map<NotifyTypeEnum, TextNotifyService> notifyServiceCache;
    private final Object lock = new Object();

    // 使用双重检查锁模式获取通知服务
    private Map<NotifyTypeEnum, TextNotifyService> getNotifyServices() {
        if (notifyServiceCache == null) {
            synchronized (lock) {
                if (notifyServiceCache == null) {
                    notifyServiceCache = initializeNotifyServices();
                }
            }
        }
        return notifyServiceCache;
    }

    private Map<NotifyTypeEnum, TextNotifyService> initializeNotifyServices() {
        return beanFactory.getBeansOfType(TextNotifyService.class)
                          .values()
                          .stream()
                          .sorted(Comparator.comparingInt(e -> e.getNotifyType().getPriority()))
                          .collect(Collectors.toMap(
                                  TextNotifyService::getNotifyType,
                                  Function.identity(),
                                  (existing, replacement) -> {
                                      throw new IllegalStateException("Duplicate key found: " + existing.getNotifyType());
                                  },
                                  LinkedHashMap::new
                          ));
    }

    @Scheduled(fixedDelay = 60 * 1000)
    public void healthCheck() {
        //180秒没有再次请求则清理
        long expiryTime = System.currentTimeMillis() - (HEALTH_INTERVAL * 1000);
        Set<String> expiredUsers = redisTemplate.opsForZSet()
                                                .rangeByScore(GlobalConstants.REQUEST_MESSAGE_TIME, 0, expiryTime);

        if (CollUtil.isEmpty(expiredUsers)) {
            return;
        }
        List<String> expiredUsersList = new ArrayList<>(expiredUsers);
        getNotifyServices().values()
                           .parallelStream()
                           .forEach(service -> service.processAfterDisconnection(expiredUsersList));

        redisTemplate.opsForZSet()
                     .remove(GlobalConstants.REQUEST_MESSAGE_TIME, expiredUsersList.toArray());

        log.info("已清理断开连接的用户缓存信息, uniqueIds: {}", expiredUsersList);
    }

    public Object getCurrentTtsMsg(TtsMessageRequestDTO request) {
        putLastRequestTime(request.getUserId(), request.getSessionId());
        Object currentTtsMsg = null;
        for (TextNotifyService stringTextNotifyServiceEntry : getNotifyServices().values()) {
            currentTtsMsg = stringTextNotifyServiceEntry.getCurrentTtsMsg(request);
            if (currentTtsMsg != null) {
                break;
            }
        }
        return currentTtsMsg;
    }

    private void putLastRequestTime(Integer userId, String sessionId) {
        String uniqueId = getUniqueId(userId, sessionId);
        long currentTime = System.currentTimeMillis();

        Boolean isFirst = redisTemplate.opsForZSet()
                                       .addIfAbsent(GlobalConstants.REQUEST_MESSAGE_TIME, uniqueId, currentTime);

        if (Boolean.TRUE.equals(isFirst)) {
            log.info("新用户首次连接TTS语音, uniqueId: {}", uniqueId);
            getNotifyServices().values().parallelStream().forEach(service -> service.processAfterConnection(userId, sessionId));
        }

        redisTemplate.opsForZSet()
                     .add(GlobalConstants.REQUEST_MESSAGE_TIME, uniqueId, currentTime);
    }

    public boolean continueBroadcast(TtsContinueBroadcastRequestDTO request) {
        return findTextNotifyServiceByNotifyType(request.getNotifyType()).continueBroadcast(request);
    }

    public List<String> getUserTtsMsgByType(Integer notifyType, Integer userId, String sessionId) {
        return findTextNotifyServiceByNotifyType(notifyType).getUserTtsMsg(userId, sessionId);
    }

    public String getUniqueId(Integer userId, String sessionId) {
        return userId + ":" + sessionId;
    }

    public TextNotifyService findTextNotifyServiceByNotifyType(Integer notifyType) {
        NotifyTypeEnum notifyTypeEnum = NotifyTypeEnum.getNotifyTypeEnum(notifyType);
        return getNotifyServices().get(notifyTypeEnum);
    }

    public boolean clearUserTtsMsgByType(Integer notifyType, Integer userId, String sessionId) {
        TextNotifyService textNotifyServiceByNotifyType = findTextNotifyServiceByNotifyType(notifyType);
        return textNotifyServiceByNotifyType.clearMsg(userId, sessionId);
    }
}

