package com.siteweb.monitoring.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.HexUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.ActiveEventMapper;
import com.siteweb.monitoring.mapper.AlarmMaskLogMapper;
import com.siteweb.monitoring.mapper.EventMaskMapper;
import com.siteweb.monitoring.mapper.TimeGroupSpanMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.util.MaskUtil;
import com.siteweb.monitoring.vo.BatchEventMaskVO;
import com.siteweb.monitoring.vo.EventMaskVO;
import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("eventMaskService")
@Slf4j
public class EventMaskServiceImpl implements EventMaskService {

    static final String STATION_ID = "StationId";
    static final String USER_ID = "UserId";
    static final String REASON = "Reason";
    static final String EQUIPMENT_ID = "EquipmentId";
    static final String EVENT_ID = "EventId";
    static final String START_TIME = "StartTime";
    static final String END_TIME = "EndTime";
    public static final int MAX_CREATE_EVENT_MASK = 1500;

    @Autowired
    EventMaskMapper eventMaskMapper;

    @Autowired
    AlarmMaskLogMapper alarmMaskLogMapper;

    @Autowired
    TimeGroupSpanMapper timeGroupSpanMapper;
    @Autowired
    TimeGroupSpanService timeGroupSpanService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    AlarmChangeService alarmChangeService;

    @Autowired
    EventMaskHistoryService eventMaskHistoryService;
    @Lazy
    @Autowired
    ActiveEventService activeEventService;
    @Lazy
    @Autowired
    HistoryEventService historyEventService;
    @Autowired
    ActiveEventMapper activeEventMapper;
    @Autowired
    AlarmMaskLogService alarmMaskLogService;
    @Autowired
    DataItemService dataItemService;

    @Override
    public Page<EventMaskDTO> findEventMaskByKeywords(EventMaskFilterDTO eventMaskFilterDTO, Page<EventMaskDTO> page) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return Page.of(page.getCurrent(),page.getSize());
        }
        eventMaskFilterDTO.setEquipmentIdList(equipmentIds);
        page.setSearchCount(false);
        Page<EventMaskDTO> maskDTOPage = eventMaskMapper.findEventMaskByKeywordsPage(page, eventMaskFilterDTO);
        long total = eventMaskMapper.findEventMaskByKeywordsPageCount(eventMaskFilterDTO);
        maskDTOPage.setTotal(total);
        return page;
    }

    @Override
    @Transactional
    public void saveEventMask(EventMaskVO eventMaskVO, Integer userId) {
        doSaveEventMask(eventMaskVO, userId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void doSaveEventMask(EventMaskVO eventMaskVO, Integer userId) {
        EventMask eventMask = BeanUtil.copyProperties(eventMaskVO, EventMask.class);
        eventMask.setUserId(userId);
        delete(eventMask);
        Integer maxTimeGroupId = this.getCurrentTimeGroupId();
        eventMask.setTimeGroupId(maxTimeGroupId);
        if (Integer.valueOf(1).equals(eventMaskVO.getTimeGroupCategory())) {//全时段屏蔽
            eventMaskMapper.insert(eventMask);
            //operationType为1是指新增屏蔽
            insertAlarmMaskLog(eventMaskVO, 1, eventMaskVO.getTimeGroupCategory(), null, userId);
            eventMaskPostHandler(eventMask);
            return;
        }
        //分时段屏蔽
        StringBuilder timeGroupChars = new StringBuilder();
        eventMask.setStartTime(null);
        eventMask.setEndTime(null);
        eventMaskMapper.insert(eventMask);
        for (TimeGroupSpanVO timeGroupSpanVO : eventMaskVO.getTimeGroupSpans()) {
            String timeGroupChar = HexUtil.booleanListToHexString(timeGroupSpanVO.getTimeSpanBool());
            //分时段屏蔽
            this.saveSeparateTimeMask(eventMask, timeGroupChar, timeGroupSpanVO.getWeek(), maxTimeGroupId);
            timeGroupChars.append(timeGroupSpanVO.getWeek());
            timeGroupChars.append(":");
            timeGroupChars.append(timeGroupChar);
            timeGroupChars.append(",");
        }
        eventMaskPostHandler(eventMask);
        //operationType为1是指新增屏蔽
        insertAlarmMaskLog(eventMaskVO, 1, eventMaskVO.getTimeGroupCategory(), timeGroupChars.toString(), userId);
    }


    public void saveSeparateTimeMask(EventMask eventMask, String timeGroupChar, Integer week,Integer timeGroupId) {
        timeGroupSpanService.saveOrUpdateTimeGroupSpan(timeGroupId, timeGroupChar, week);
        eventMaskPostHandler(eventMask);
    }

    public void delete(EventMask eventMask) {
        eventMaskMapper.deleteEventMask(eventMask.getStationId(), eventMask.getEquipmentId(), eventMask.getEventId());
    }


    public void eventMaskPostHandler(EventMask eventMask) {
        List<ActiveEvent> dbActiveEventByCondition = activeEventService.getDBActiveEventByCondition(eventMask.getStationId(), eventMask.getEquipmentId(), eventMask.getEventId());
        Date currentTime = new Date();
        for (ActiveEvent activeEvent : dbActiveEventByCondition) {
            saveEndEventByMask(activeEvent.getStationId(), activeEvent.getEquipmentId(), activeEvent.getEventId(), activeEvent.getSequenceId(), currentTime);
        }
    }

    /**
     * 由于表设计原因，告警屏蔽的TimeGroupId为当前tbl_eventmask表中的最大值+1
     * @return {@link Integer}
     */
    private Integer getCurrentTimeGroupId() {
        Integer maxTimeGroupId = eventMaskMapper.findMaxTimeGroupId();
        if (Objects.isNull(maxTimeGroupId)) {
            return 1;
        }
        return ++maxTimeGroupId;
    }

    @Override
    @Transactional
    public void batchCreateEventMasks(BatchEventMaskVO vo, Integer userId) {
        List<String> ids = new ArrayList<>();
        for (int i = 0; i < vo.getStationIds().size(); i++) {
            ids.add(String.join(",",String.valueOf(vo.getStationIds().get(i)),String.valueOf(vo.getEquipmentIds().get(i)),String.valueOf(vo.getEventIds().get(i))));
        }
        BatchCreateEventMaskDTO batchCreateEventMaskDTO = BatchCreateEventMaskDTO.builder()
                                                                                 .ids(ids)
                                                                                 .startTime(vo.getStartTime())
                                                                                 .endTime(vo.getEndTime())
                                                                                 .reason(vo.getReason())
                                                                                 .timeGroupCategory(vo.getTimeGroupCategory())
                                                                                 .timeGroupSpans(vo.getTimeGroupSpans())
                                                                                 .build();
        batchSaveMask(userId, batchCreateEventMaskDTO);
    }

    private void insertAlarmMaskLog(EventMask eventMask, int operationType, Integer timeGroupCategory, String timeGroupChars, int userId) {
        AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
        alarmMaskLog.setStationId(eventMask.getStationId());
        alarmMaskLog.setEquipmentId(eventMask.getEquipmentId());
        alarmMaskLog.setEventId(eventMask.getEventId());
        alarmMaskLog.setResourceStructureId(Optional.ofNullable(equipmentService.findById(eventMask.getEquipmentId())).orElse(new Equipment()).getResourceStructureId());
        alarmMaskLog.setUserId(userId);
        alarmMaskLog.setOperationType(operationType);
        alarmMaskLog.setOperationTime(new Date());
        alarmMaskLog.setTimeGroupCategory(timeGroupCategory);
        alarmMaskLog.setStartTime(eventMask.getStartTime());
        alarmMaskLog.setEndTime(eventMask.getEndTime());
        if (operationType == 1 && Integer.valueOf(2).equals(timeGroupCategory)) {//新增分时段屏蔽
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
        }
        alarmMaskLog.setComment(eventMask.getReason());
        alarmMaskLogMapper.insertEntity(alarmMaskLog);
    }

    private void insertAlarmMaskLog(EventMaskVO eventMaskVO, int operationType, Integer timeGroupCategory, String timeGroupChars, int userId) {
        AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
        alarmMaskLog.setStationId(eventMaskVO.getStationId());
        alarmMaskLog.setEquipmentId(eventMaskVO.getEquipmentId());
        alarmMaskLog.setEventId(eventMaskVO.getEventId());
        alarmMaskLog.setUserId(userId);
        alarmMaskLog.setOperationType(operationType);
        alarmMaskLog.setOperationTime(new Date());
        alarmMaskLog.setTimeGroupCategory(timeGroupCategory);
        alarmMaskLog.setStartTime(eventMaskVO.getStartTime());
        alarmMaskLog.setEndTime(eventMaskVO.getEndTime());
        Equipment equipment = Optional.ofNullable(equipmentService.findById(eventMaskVO.getEquipmentId()))
                                      .orElse(new Equipment());
        alarmMaskLog.setResourceStructureId(equipment.getResourceStructureId());
        if (operationType == 1 && Integer.valueOf(2).equals(timeGroupCategory)) {//新增分时段屏蔽
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
        }
        alarmMaskLog.setComment(eventMaskVO.getReason());
        alarmMaskLogMapper.insertEntity(alarmMaskLog);
    }

    @Override
    @Transactional
    public void deleteEventMask(EventMask eventMask, Integer userId) {
        eventMaskMapper.deleteEventMask(eventMask.getStationId(), eventMask.getEquipmentId(), eventMask.getEventId());
        //operationType为2是指解除屏蔽
        insertAlarmMaskLog(eventMask, 2, null, null, userId);
    }

    @Override
    @Transactional
    public void batchDeleteEventMasks(List<String> eventIds, Integer userId) {
        if (CollUtil.isEmpty(eventIds)) {
            return;
        }
        List<EventIdentityDTO> eventIdentityDTOList = EventIdentityDTO.from(eventIds);
        List<EventMask> eventMaskList =  eventMaskMapper.findByEventMaksIdentities(eventIdentityDTOList);
        if (CollUtil.isEmpty(eventMaskList)) {
            return;
        }
        timeGroupSpanService.deleteByTimeGroupIds(eventMaskList.stream().map(EventMask::getTimeGroupId).toList());
        eventMaskMapper.deleteByEventMaksIdentities(eventIdentityDTOList);
        //记录屏蔽解除日志
        alarmMaskLogService.eventMaskCancelLog(eventMaskList);
    }

    @Override
    @Transactional
    public void batchDeleteEventMasksByEquipmentId(Integer stationId, Integer equipmentId, Integer userId) {
        List<EventMask> eventMasks = eventMaskMapper.findEventMaskByEquipmentId(stationId, equipmentId);
        if (CollUtil.isEmpty(eventMasks)) {
            return;
        }
        List<String> eventIds = eventMasks.stream()
                                          .map(e -> e.getStationId() + "," + e.getEquipmentId() + "," + e.getEventId())
                                          .toList();
        batchDeleteEventMasks(eventIds,userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllEventMasks(Integer userId) {
        List<EventMask> eventMasks = eventMaskMapper.selectList(null);
        List<String> eventIds = eventMasks.stream()
                                      .map(e -> e.getStationId() + "," + e.getEquipmentId() + "," + e.getEventId())
                                      .toList();
        batchDeleteEventMasks(eventIds, userId);
    }

    @Override
    public EventMask findById(Integer stationId, Integer equipmentId, int eventId) {
        return eventMaskMapper.selectOne(new QueryWrapper<EventMask>().eq(STATION_ID, stationId)
                .eq(EQUIPMENT_ID, equipmentId).eq(EVENT_ID, eventId));
    }

    @Override
    public EventMaskDTO findEventMaskDTOById(Integer stationId, Integer equipmentId, int eventId) {
        EventMaskDTO eventMaskDTO = eventMaskMapper.findEventMaskDTOById(stationId, equipmentId, eventId);
        if (null != eventMaskDTO) {
            List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.findByTimeGroupId(eventMaskDTO.getTimeGroupId());
            if (!timeGroupSpans.isEmpty()) {
                eventMaskDTO.setTimeGroupCategory(2);//分时段屏蔽
            } else {
                eventMaskDTO.setTimeGroupCategory(1);//全时段屏蔽
            }
            List<TimeGroupSpanDTO> timeGroupSpanDTOList = new ArrayList<>();
            for (TimeGroupSpan timeGroupSpan : timeGroupSpans) {
                TimeGroupSpanDTO timeGroupSpanDTO = new TimeGroupSpanDTO();
                timeGroupSpanDTO.setTimeGroupId(timeGroupSpan.getTimeGroupId());
                timeGroupSpanDTO.setWeek(timeGroupSpan.getWeek());
                timeGroupSpanDTO.setTimeSpanBool(HexUtil.hexStringToBooleanList(timeGroupSpan.getTimeSpanChar()));
                timeGroupSpanDTOList.add(timeGroupSpanDTO);
            }
            eventMaskDTO.setTimeGroupSpans(timeGroupSpanDTOList);
        }
        return eventMaskDTO;
    }

    @Override
    public Set<Integer> findMaskEffective(Integer equipmentId) {
        List<SimpleEventMaskDTO> simpleEventMaskDTOsByEquipmentId = this.findSimpleEventMaskDTOsByEquipmentId(equipmentId);
        return simpleEventMaskDTOsByEquipmentId.stream()
                                               .filter(SimpleEventMaskDTO::getEffective)
                                               .map(SimpleEventMaskDTO::getEventId)
                                               .collect(Collectors.toSet());
    }

    @Override
    public List<SimpleEventMaskDTO> findSimpleEventMaskDTOsByEquipmentId(Integer equipmentId) {
        List<SimpleEventMaskDTO> simpleEventMaskList = eventMaskMapper.findSimpleEventMaskDTOsByEquipmentId(equipmentId);
        this.setMaskState(simpleEventMaskList);
        return simpleEventMaskList;
    }

    @Override
    public Page<ConditionEventMaskDTO> findSimpleEventMaskByBaseTypeIdsPage(EventMaskFilterDTO eventMaskFilterDTO, Page<ConditionEventMaskDTO> page) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        eventMaskFilterDTO.setEquipmentIdList(equipmentIds);
        eventMaskFilterDTO.setBaseTypeIdList(StringUtils.splitToIntegerList(eventMaskFilterDTO.getBaseTypeIds()));
        Set<Integer> childrenIds = resourceStructureManager.getAllChildrenId(StringUtils.splitToIntegerList(eventMaskFilterDTO.getResourceStructureIds()));
        eventMaskFilterDTO.setResourceStructureIdList(childrenIds);
        eventMaskFilterDTO.setEquipmentBaseTypeIdList(StringUtils.splitToIntegerList(eventMaskFilterDTO.getEquipmentBaseTypeIds()));
        eventMaskFilterDTO.setEquipmentCategoryIdList(StringUtils.splitToIntegerList(eventMaskFilterDTO.getEquipmentCategories()));
        page.setSearchCount(false);
        Page<ConditionEventMaskDTO> maskDTOPage = eventMaskMapper.findSimpleEventMaskByBaseTypeIdsPage(page, eventMaskFilterDTO);
        Integer eventSeverityEntryId = 23;
        Map<Integer, String> eventLevelMap = dataItemService.findByEntryId(eventSeverityEntryId).stream().collect(Collectors.toMap(DataItem::getItemId, DataItem::getExtendField4));
        maskDTOPage.getRecords().forEach(record -> {
            Integer eventSeverity = record.getEventSeverity();
            if (eventSeverity != null && eventLevelMap.containsKey(eventSeverity)) {
                String eventLevelStr = eventLevelMap.get(eventSeverity);
                if (eventLevelStr != null) {
                    try {
                        record.setEventLevel(Integer.parseInt(eventLevelStr));
                    } catch (NumberFormatException e) {
                        record.setEventLevel(null);
                    }
                } else {
                    record.setEventLevel(null);
                }
            }
        });

        long total = eventMaskMapper.findEventMaskByBaseTypeIdsPageCount(eventMaskFilterDTO);
        maskDTOPage.setTotal(total);
        this.setMaskState(maskDTOPage.getRecords());
        return maskDTOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEventMaskByBaseTypeIds(EventMaskFilterDTO eventMaskFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        eventMaskFilterDTO.setEquipmentIdList(equipmentIds);
        eventMaskFilterDTO.setBaseTypeIdList(StringUtils.splitToIntegerList(eventMaskFilterDTO.getBaseTypeIds()));
        Set<Integer> childrenIds = resourceStructureManager.getAllChildrenId(StringUtils.splitToIntegerList(eventMaskFilterDTO.getResourceStructureIds()));
        eventMaskFilterDTO.setResourceStructureIdList(childrenIds);
        eventMaskFilterDTO.setEquipmentBaseTypeIdList(StringUtils.splitToIntegerList(eventMaskFilterDTO.getEquipmentBaseTypeIds()));
        eventMaskFilterDTO.setEquipmentCategoryIdList(StringUtils.splitToIntegerList(eventMaskFilterDTO.getEquipmentCategories()));
        List<EventMask> eventMaskList = eventMaskMapper.findEventMaskByBaseTypeIds(eventMaskFilterDTO);
        List<String> eventIds = eventMaskList.stream().map(e -> e.getStationId() + "," + e.getEquipmentId() + "," + e.getEventId()).toList();
        batchDeleteEventMasks(eventIds, userId);
    }

    @Override
    public void createEventMaskByFilter(EventMaskFilterCreateDTO eventMaskFilterCreateDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        eventMaskFilterCreateDTO.setEquipmentIdList(equipmentIds);
        EventMaskFilterDTO eventMaskFilterDTO = BeanUtil.copyProperties(eventMaskFilterCreateDTO, EventMaskFilterDTO.class);
        List<EventMask> eventMaskByBaseTypeIds = eventMaskMapper.findEventMaskCreateByBaseTypeIds(eventMaskFilterDTO);
        if (CollUtil.size(eventMaskByBaseTypeIds) > MAX_CREATE_EVENT_MASK) {
            log.error("告警屏蔽数据量超过1500条，请缩小范围");
        }
        Set<String> ids = new HashSet<>(eventMaskByBaseTypeIds.size());
        for (EventMask eventMaskByBaseTypeId : eventMaskByBaseTypeIds) {
            String id = String.join(",", String.valueOf(eventMaskByBaseTypeId.getStationId()), String.valueOf(eventMaskByBaseTypeId.getEquipmentId()), String.valueOf(eventMaskByBaseTypeId.getEventId()));
            ids.add(id);
        }
        BatchCreateEventMaskDTO batchCreateEventMaskDTO = new BatchCreateEventMaskDTO();
        batchCreateEventMaskDTO.setStartTime(eventMaskFilterCreateDTO.getStartTime());
        batchCreateEventMaskDTO.setEndTime(eventMaskFilterCreateDTO.getEndTime());
        batchCreateEventMaskDTO.setReason(eventMaskFilterCreateDTO.getReason());
        batchCreateEventMaskDTO.setTimeGroupCategory(eventMaskFilterCreateDTO.getTimeGroupCategory());
        batchCreateEventMaskDTO.setTimeGroupSpans(eventMaskFilterCreateDTO.getTimeGroupSpans());
        batchCreateEventMaskDTO.setIds(new ArrayList<>(ids));
        batchSaveMask(userId,batchCreateEventMaskDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveEndEventByEquipmentIds(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        List<ActiveEvent> activeEvents = activeEventMapper.selectList(Wrappers.lambdaQuery(ActiveEvent.class).select(ActiveEvent::getSequenceId).in(ActiveEvent::getEquipmentId, equipmentIds));
        List<String> sequenceIds = activeEvents.stream().map(ActiveEvent::getSequenceId).toList();
        saveEndEventByMask(sequenceIds, new Date());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveEndEventByStationIds(List<Integer> stationIdList) {
        if (CollUtil.isEmpty(stationIdList)) {
            return;
        }
        List<ActiveEvent> activeEvents = activeEventMapper.selectList(Wrappers.lambdaQuery(ActiveEvent.class).select(ActiveEvent::getSequenceId).in(ActiveEvent::getStationId, stationIdList));
        List<String> sequenceIds = activeEvents.stream().map(ActiveEvent::getSequenceId).toList();
        saveEndEventByMask(sequenceIds, new Date());
    }

    @Override
    public void batchSaveMask(Integer userId, BatchCreateEventMaskDTO batchCreateEventMaskDTO) {
        Integer currentTimeGroupId = getCurrentTimeGroupId();
        batchHandlerFullTimeMask(batchCreateEventMaskDTO, userId, currentTimeGroupId);
        if (Objects.equals(batchCreateEventMaskDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue())) {
            //与全时段相比 多了一个批量处理分时段屏蔽字符串的表的逻辑
            timeGroupSpanService.batchInsertEventTimeGroupSpan(currentTimeGroupId, batchCreateEventMaskDTO);
        }
        //添加屏蔽日志
        alarmMaskLogService.batchInsertEventMaskLog(batchCreateEventMaskDTO, userId);
    }
    private void batchHandlerFullTimeMask(BatchCreateEventMaskDTO dto, Integer userId, Integer currentTimeGroupId) {
        // 先删除
        List<EventIdentityDTO> eventIdentityDTOList = EventIdentityDTO.from(dto.getIds());
        timeGroupSpanMapper.deleteByEventMaksIdentities(eventIdentityDTOList);
        eventMaskMapper.deleteByEventMaksIdentities(eventIdentityDTOList);
        // 再新增
        List<EventMask> insertList = new ArrayList<>();
        for (EventIdentityDTO eventIdentityDTO : eventIdentityDTOList) {
            EventMask eventMask = eventMaskBuilder(dto, eventIdentityDTO, currentTimeGroupId++, userId);
            insertList.add(eventMask);
        }
        //结束系统中当前的活动告警
        saveEndEventByEventIds(eventIdentityDTOList);
        //批量添加与插入
        batchInsert(insertList);
    }

    private void saveEndEventByEventIds(List<EventIdentityDTO> eventIdentityDTOList) {
        if (CollUtil.isEmpty(eventIdentityDTOList)) {
            return;
        }
        List<String> sequenceIds = activeEventMapper.findSequenceIdsByIdentities(eventIdentityDTOList);
        saveEndEventByMask(sequenceIds, new Date());
    }

    private void batchInsert(List<EventMask> insertList) {
        if (CollUtil.isEmpty(insertList)) {
            return;
        }
        eventMaskMapper.insert(insertList);
    }

    private EventMask eventMaskBuilder(BatchCreateEventMaskDTO dto, EventIdentityDTO eventIdentityDTO, Integer timeGroupId, Integer userId) {
        //是否是分时段屏蔽
        boolean isTimeGroupSpan = Objects.equals(dto.getTimeGroupCategory(),
                TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue());
        return EventMask.builder()
                        .equipmentId(eventIdentityDTO.getEquipmentId())
                        .stationId(eventIdentityDTO.getStationId())
                        .eventId(eventIdentityDTO.getEventId())
                        .timeGroupId(timeGroupId)
                        .startTime(isTimeGroupSpan ? null : dto.getStartTime())
                        .endTime(isTimeGroupSpan ? null : dto.getEndTime())
                        .reason(dto.getReason())
                        .userId(userId)
                        .build();
    }

    private <T extends SimpleEventMaskDTO> void setMaskState(List<T> simpleEventMaskDTOList) {
        List<EquipmentEventIdDTO> list = simpleEventMaskDTOList.stream()
                                                               .filter(dto -> Boolean.FALSE.equals(dto.getEffective()))
                                                               .map(EquipmentEventIdDTO::new)
                                                               .toList();
        Set<String> intervalEventIds = this.findIntervalEventIds(list);
        for (SimpleEventMaskDTO simpleEventMaskDTO : simpleEventMaskDTOList) {
            if (intervalEventIds.contains(simpleEventMaskDTO.getUniqueId())) {
                simpleEventMaskDTO.setEffective(true);
            }
        }
    }
    /**
     * 查询分时段屏蔽的事件
     * @return {@link Set}<{@link String}>
     */
    public Set<String> findIntervalEventIds(List<EquipmentEventIdDTO> equipmentEventIdList){
        if (CollUtil.isEmpty(equipmentEventIdList)) {
            return Collections.emptySet();
        }
        List<EventMask> eventMaskList = eventMaskMapper.findEventMaskByEquipmentEventIdIdList(equipmentEventIdList);
        if (CollUtil.isEmpty(eventMaskList)) {
            return Collections.emptySet();
        }
        Set<Integer> timeGroupIds = eventMaskList.stream()
                                                 .map(EventMask::getTimeGroupId)
                                                 .collect(Collectors.toSet());
        Map<Integer, Set<String>> timeGroupMap = eventMaskList.stream()
                                                         .collect(Collectors.groupingBy(EventMask::getTimeGroupId,
                                                                 Collectors.mapping(mask -> mask.getEquipmentId() + "." + mask.getEventId(), Collectors.toSet())));
        List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanMapper.findByWeekAndTimeGroupIds(DateUtil.thisDayOfWeek(), timeGroupIds);
        Set<String> result = new HashSet<>(timeGroupSpanList.size());
        for (TimeGroupSpan timeGroupSpan : timeGroupSpanList) {
            if (MaskUtil.isIntervalMask(timeGroupSpan.getTimeSpanChar())) {
                result.addAll(timeGroupMap.get(timeGroupSpan.getTimeGroupId()));
            }
        }
        return result;
    }

    @Override
    public boolean isMaskEffective(Integer stationId, Integer equipmentId, Integer eventId) {
        EventMask eventMask = eventMaskMapper.findEventMaskByEventId(stationId, equipmentId, eventId);
        if (ObjectUtil.isNull(eventMask)) {
            return false;
        }
        List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.findByTimeGroupId(eventMask.getTimeGroupId());
        //处于全时段屏蔽
        if (CollUtil.isEmpty(timeGroupSpans) && ObjectUtil.isNotNull(eventMask.getStartTime()) && ObjectUtil.isNotNull(eventMask.getEndTime()) && DateUtil.isIn(new Date(), eventMask.getStartTime(), eventMask.getEndTime())) {
            return true;
        }
        //处于分时段屏蔽
        TimeGroupSpan timeGroupSpan = timeGroupSpanMapper.findByWeekAndTimeGroupId(DateUtil.thisDayOfWeek(), eventMask.getTimeGroupId());
        return ObjectUtil.isNotNull(timeGroupSpan) && MaskUtil.isIntervalMask(timeGroupSpan.getTimeSpanChar());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEndEventByMask(Integer stationId, Integer equipmentId, Integer eventId, String sequenceId, Date endTime) {
        // 结束告警，修改告警结束时间
        activeEventService.updateEndTimeBySequenceId(endTime, sequenceId);
        ActiveEvent activeEvent = activeEventService.getDBActiveEventBySequenceId(sequenceId);

        if (ObjectUtil.isNotEmpty(activeEvent)) {
            // 插入告警变更
            AlarmChange alarmChange = BeanUtil.toBean(activeEvent, AlarmChange.class);
            alarmChange.setOperationType(AlarmOperationTypeEnum.END.getValue()); // 发送告警状态变化到告警变化表
            alarmChangeService.createAlarmChange(alarmChange);
        }


        // 事件屏蔽历史是否存在，不存在则添加
        if (!eventMaskHistoryService.existsBySequenceId(sequenceId)) {
            EventMaskHistory eventMaskHistory = BeanUtil.toBean(activeEvent, EventMaskHistory.class);
            eventMaskHistoryService.create(eventMaskHistory);
        }

        // 已结束且确认告警送历史告警表
        if (ObjectUtil.isNotEmpty(endTime) && ObjectUtil.isNotEmpty(activeEvent)) {
            Date confirmTime = activeEvent.getConfirmTime();
            if (ObjectUtil.isNotEmpty(confirmTime)) {
                historyEventService.insertMidHistoryEvent(sequenceId, endTime, confirmTime, activeEvent.getConfirmerId(), activeEvent.getConfirmerName(),null);
            }
        }
    }

    public void saveEndEventByMask(List<String> sequenceIds, Date endTime){
        if (CollUtil.isEmpty(sequenceIds)) {
            return;
        }
        List<ActiveEvent> unEndBySequenceIdsForUpdate = activeEventMapper.findUnEndBySequenceIdsForUpdate(sequenceIds);
        if (CollUtil.isEmpty(unEndBySequenceIdsForUpdate)) {
            return;
        }
        //批量修改结束时间
        activeEventService.updateEndTimeBySequenceIds(endTime, sequenceIds);
        //批量插入告警变更
        Set<String> notInBySequenceIds = eventMaskHistoryService.findNotInBySequenceIds(sequenceIds);
        List<AlarmChange> alarmChangeInsertList = new ArrayList<>();
        List<EventMaskHistory> eventMaskHistoryInsertList = new ArrayList<>();
        List<HistoryEvent> historyEventList = new ArrayList<>();
        for (ActiveEvent activeEvent : unEndBySequenceIdsForUpdate) {
            //被屏蔽的告警全部结束处理
            activeEvent.setEndTime(endTime);
            AlarmChange alarmChange = BeanUtil.toBean(activeEvent, AlarmChange.class);
            alarmChange.setOperationType(AlarmOperationTypeEnum.END.getValue());
            alarmChange.setInsertTime(endTime);
            alarmChangeInsertList.add(alarmChange);
            //没有在告警屏蔽历史中则添加告警屏蔽历史记录
            if (notInBySequenceIds.contains(activeEvent.getSequenceId())) {
                eventMaskHistoryInsertList.add(BeanUtil.toBean(activeEvent, EventMaskHistory.class));
            }
            if (ObjectUtil.isNotEmpty(activeEvent.getConfirmTime())) {
                //已经确认了还操作结束了，需要删除活动告警并且移动到历史告警中
                HistoryEvent historyEvent = BeanUtil.toBean(activeEvent, HistoryEvent.class);
                historyEventList.add(historyEvent);
            }
        }
        alarmChangeService.batchInsert(alarmChangeInsertList);
        eventMaskHistoryService.batchInsert(eventMaskHistoryInsertList);
        historyEventService.batchInsert(historyEventList);
        activeEventService.removeBySequenceIds(historyEventList.stream().map(HistoryEvent::getSequenceId).toList());
    }
}
