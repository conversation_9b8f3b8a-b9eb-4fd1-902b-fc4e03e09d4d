CREATE TABLE tbl_account
(
    UserId            INTEGER PRIMARY KEY,
    UserName          VARCHAR(256) NOT NULL,
    LogonId           VARCHAR(40)  NOT NULL,
    Password          VARCHAR(256),
    Enable            integer      NOT NULL DEFAULT 0,
    <PERSON><PERSON>rror          INTEGER,
    Locked            integer      NOT NULL DEFAULT 0,
    ValidTime         TIMESTAMP,
    Description       VARCHAR(510),
    IsRemote          integer      NOT NULL DEFAULT 0,
    CenterId          INTEGER,
    PasswordValidTime TIMESTAMP,
    Avatar            VARCHAR(512),
    ThemeName         VARCHAR(256),
    NeedResetPwd      integer               DEFAULT 0
);

CREATE TABLE tbl_activecontrol
(
    StationId             integer                NOT NULL,
    StationName           character varying(510) NOT NULL,
    EquipmentId           integer                NOT NULL,
    EquipmentName         character varying(256) NOT NULL,
    ControlId             integer                NOT NULL,
    ControlName           character varying(256) NOT NULL,
    SerialNo              serial                 NOT NULL,
    ControlSeverity       integer                NOT NULL,
    CmdToken              text                   NOT NULL,
    ControlPhase          integer                NOT NULL,
    StartTime             timestamp NOT NULL,
    EndTime               timestamp,
    ConfirmTime           timestamp,
    ConfirmerId           integer,
    ConfirmerName         character varying(510),
    ControlResultType     integer,
    ControlResult         character varying(510),
    ControlExecuterId     integer,
    ControlExecuterIdName character varying(510),
    ControlType           integer,
    ActionId              integer,
    Description           character varying(510),
    Retry                 integer,
    BaseTypeId            numeric(12, 0),
    BaseTypeName          character varying(256),
    ParameterValues       text                   NOT NULL,
    BaseCondId            numeric(12, 0)
);
ALTER TABLE ONLY tbl_activecontrol ADD CONSTRAINT idx_22619_primary PRIMARY KEY (ControlId, StationName, SerialNo, StartTime, StationId);
CREATE INDEX idx_22619_idx_acticecontrol_2 ON tbl_activecontrol USING BTREE (StationId, EquipmentId, ControlId, StartTime);
CREATE INDEX idx_22619_tbl_activecontrol_idx1 ON tbl_activecontrol USING BTREE (SerialNo);

CREATE TABLE tbl_activeevent
(
    SequenceId            CHARACTER VARYING(256) NOT NULL PRIMARY KEY,
    StationId             integer                NOT NULL,
    StationName           character varying(510) NOT NULL,
    EquipmentId           integer                NOT NULL,
    EquipmentName         character varying(256) NOT NULL,
    EventId               integer                NOT NULL,
    EventName             character varying(256) NOT NULL,
    EventConditionId      integer                NOT NULL,
    EventSeverityId       integer                NOT NULL,
    EventSeverity         character varying(256) NOT NULL,
    EventLevel            integer,
    StartTime             timestamp NOT NULL,
    EndTime               timestamp,
    CancelTime            timestamp,
    CancelUserId          integer,
    CancelUserName        character varying(256),
    ConfirmTime           timestamp,
    ConfirmerId           integer,
    ConfirmerName         character varying(256),
    EventValue            double precision,
    EndValue              double precision,
    ReversalNum           integer,
    Meanings              character varying(510),
    EventFilePath         character varying(510),
    Description           character varying(510),
    SourceHostId          integer,
    InstructionId         character varying(510),
    InstructionStatus     integer,
    StandardAlarmNameId   integer,
    StandardAlarmName     character varying(256),
    BaseTypeId            numeric(12, 0),
    BaseTypeName          character varying(256),
    EquipmentCategory     integer                NOT NULL,
    EquipmentCategoryName character varying(256),
    MaintainState         integer DEFAULT 0      NOT NULL,
    SignalId              integer,
    RelateSequenceId      character varying(256),
    EventCategoryId       integer                NOT NULL,
    EventStateId          integer                NOT NULL,
    CenterId              integer,
    CenterName            character varying(256),
    StructureName         character varying(256),
    MonitorUnitName       character varying(256),
    StructureId           integer,
    StationCategoryId     integer,
    EquipmentVendor       character varying(256),
    ConvergenceEventId    bigint  DEFAULT '0'::BIGINT,
    ResourceStructureId   integer DEFAULT 0,
    BaseEquipmentId       integer DEFAULT 0
);
CREATE INDEX idx_22637_idx_equipmentid_convergenceeventid_starttime ON tbl_activeevent USING BTREE (equipmentid, convergenceeventid, starttime);
CREATE INDEX idx_22637_idx_eventseverityid_endtime ON tbl_activeevent USING BTREE (eventseverityid, endtime);
CREATE UNIQUE INDEX idx_22637_tbl_activeevent_id1 ON tbl_activeevent USING BTREE (sequenceid);

CREATE TABLE tbl_activesignal
(
    stationid         integer NOT NULL,
    stationname       character varying(510),
    equipmentid       integer NOT NULL,
    equipmentname     character varying(256),
    signalid          integer NOT NULL,
    signalname        character varying(256),
    signalcategory    integer,
    datatype          integer NOT NULL,
    floatvalue        double precision,
    stringvalue       character varying(256),
    datetimevalue     timestamp,
    sampletime        timestamp NOT NULL,
    showprecision     character varying(40),
    unit              character varying(128),
    basetypeid        numeric(12, 0),
    basetypename      character varying(256),
    eventseverity     integer,
    eventseverityname character varying(256),
    meanings          character varying(510),
    flag              integer,
    lastupdate        timestamp NOT NULL
);

ALTER TABLE ONLY tbl_activesignal ADD CONSTRAINT idx_22656_primary PRIMARY KEY (equipmentid, lastupdate, signalid, stationid);

CREATE TABLE tbl_alarmchange
(
    SequenceId            VARCHAR(256) NOT NULL,
    SerialNo BIGSERIAL NOT NULL PRIMARY KEY,
    OperationType         INTEGER      NOT NULL,
    StationId             INTEGER      NOT NULL,
    StationName           VARCHAR(510) NOT NULL,
    EquipmentId           INTEGER      NOT NULL,
    EquipmentName         VARCHAR(256) NOT NULL,
    EventId               INTEGER      NOT NULL,
    EventName             VARCHAR(256) NOT NULL,
    EventConditionId      INTEGER      NOT NULL,
    EventSeverityId       INTEGER      NOT NULL,
    EventSeverity         VARCHAR(256),
    EventLevel            INTEGER,
    StartTime             TIMESTAMP    NOT NULL,
    EndTime               TIMESTAMP,
    CancelTime            TIMESTAMP,
    CancelUserId          INTEGER,
    CancelUserName        VARCHAR(256),
    ConfirmTime           TIMESTAMP,
    ConfirmerId           INTEGER,
    ConfirmerName         VARCHAR(256),
    EventValue            DOUBLE PRECISION,
    EndValue              DOUBLE PRECISION,
    ReversalNum           INTEGER,
    Meanings              VARCHAR(510),
    EventFilePath         VARCHAR(510),
    Description           VARCHAR(510),
    SourceHostId          INTEGER,
    InstructionId         VARCHAR(510),
    InstructionStatus     INTEGER,
    StandardAlarmNameId   INTEGER,
    StandardAlarmName     VARCHAR(256),
    BaseTypeId            NUMERIC(12, 0),
    BaseTypeName          VARCHAR(256),
    EquipmentCategory     INTEGER      NOT NULL,
    EquipmentCategoryName VARCHAR(256),
    MaintainState         INTEGER      NOT NULL DEFAULT 0,
    SignalId              INTEGER,
    RelateSequenceId      VARCHAR(256),
    EventCategoryId       INTEGER      NOT NULL,
    EventStateId          INTEGER      NOT NULL,
    CenterId              INTEGER,
    CenterName            VARCHAR(256),
    StructureName         VARCHAR(256),
    MonitorUnitName       VARCHAR(256),
    StructureId           INTEGER,
    StationCategoryId     INTEGER,
    EquipmentVendor       VARCHAR(256),
    ConvergenceEventId    BIGINT                DEFAULT 0,
    ResourceStructureId   INTEGER               DEFAULT 0,
    BaseEquipmentId       INTEGER               DEFAULT 0,
    InsertTime            TIMESTAMP             DEFAULT localtimestamp
);

CREATE INDEX tbl_alarmchange_sequenceid_operationtype_idx ON tbl_alarmchange (SequenceId, OperationType);

CREATE TABLE tbl_area
(
    AreaId      integer                NOT NULL PRIMARY KEY,
    AreaName    character varying(256) NOT NULL,
    Description character varying(510)
);

CREATE TABLE tbl_areamap
(
    StationId integer NOT NULL,
    AreaId    integer NOT NULL
);
ALTER TABLE ONLY tbl_areamap ADD CONSTRAINT idx_22691_primary PRIMARY KEY (AreaId, StationId);

CREATE TABLE tbl_backupandcleartime
(
    DataType  integer,
    StartTime timestamp,
    EndTime   timestamp
);

CREATE TABLE tbl_configchangedefine
(
    ConfigId   integer                NOT NULL,
    EntityName character varying(510) NOT NULL,
    TableName  character varying(510) NOT NULL,
    IdDefine   character varying(510)
);

CREATE TABLE tbl_configchangemacrolog
(
    ObjectId   character varying(510) NOT NULL,
    ConfigId   integer                NOT NULL,
    EditType   integer                NOT NULL,
    UpdateTime timestamp NOT NULL
);
CREATE INDEX idx_22796_idx_configchangemacrolog_1 ON tbl_configchangemacrolog USING BTREE (UpdateTime, ConfigId);
CREATE INDEX idx_22796_idx_configchangemacrolog_2 ON tbl_configchangemacrolog USING BTREE (objectId, configId, editType);

CREATE TABLE tbl_configchangemap
(
    MicroConfigId integer NOT NULL,
    MicroEditType integer NOT NULL,
    MacroConfigId integer NOT NULL,
    MacroEditType integer NOT NULL,
    IdConvertRule character varying(510)
);


CREATE TABLE tbl_configchangemicrolog
(
    ObjectId   character varying(510) NOT NULL,
    ConfigId   integer                NOT NULL,
    EditType   integer                NOT NULL,
    UpdateTime timestamp NOT NULL
);
CREATE INDEX idx_22796_idx_configchangemicrolog_1 ON tbl_configchangemicrolog USING BTREE (objectId, configId, editType);

CREATE TABLE tbl_configchecktask
(
    ObjectType  character varying(128)  NOT NULL,
    ObjectId    character varying(256) NOT NULL,
    Priority    integer                NOT NULL,
    CheckTime   timestamp NOT NULL,
    BugType     integer                NOT NULL,
    Description character varying(510)
);
ALTER TABLE ONLY tbl_configchecktask ADD CONSTRAINT idx_22805_primary PRIMARY KEY (bugtype, objectid);

CREATE TABLE tbl_control
(
    id                  serial                 NOT NULL PRIMARY KEY,
    equipmenttemplateid integer                NOT NULL,
    controlid           integer                NOT NULL,
    controlname         character varying(256) NOT NULL,
    controlcategory     integer                NOT NULL,
    cmdtoken            text,
    basetypeid          numeric(12, 0),
    controlseverity     integer                NOT NULL,
    signalid            integer,
    timeout             double precision,
    retry               integer,
    description         character varying(510),
    enable              integer                NOT NULL,
    visible             integer                NOT NULL,
    displayindex        integer                NOT NULL,
    commandtype         integer                NOT NULL,
    controltype         smallint,
    datatype            smallint,
    "maxvalue"            double precision       NOT NULL,
    "minvalue"            double precision       NOT NULL,
    defaultvalue        double precision,
    moduleno            integer DEFAULT 0      NOT NULL
);
CREATE INDEX idx_22809_idx_tblcontrol_1 ON tbl_control USING BTREE (equipmenttemplateid);
CREATE INDEX idx_22809_idx_tblcontrol_2 ON tbl_control USING BTREE (equipmenttemplateid, controlid);

CREATE TABLE tbl_controllogaction
(
    logactionid integer NOT NULL,
    actionid    integer NOT NULL,
    actionname  character varying(100),
    equipmentid integer,
    controlid   integer,
    actionvalue character varying(510)
);

CREATE TABLE tbl_controlmeanings
(
    id                  serial   NOT NULL PRIMARY KEY,
    equipmenttemplateid integer  NOT NULL,
    controlid           integer  NOT NULL,
    parametervalue      smallint NOT NULL,
    meanings            character varying(510),
    basecondid          numeric(12, 0)
);
CREATE INDEX idx_22823_idx_tblcontrolmeanings_1 ON tbl_controlmeanings USING BTREE (equipmenttemplateid, controlid);


CREATE TABLE tbl_dataentry
(
    entryid       integer           NOT NULL PRIMARY KEY,
    entrycategory integer,
    entryname     character varying(256),
    entrytitle    character varying(256),
    entryalias    character varying(510),
    enable        integer DEFAULT 1 NOT NULL,
    description   character varying(510)
);

CREATE TABLE tbl_dataitem
(
    entryitemid   integer                NOT NULL PRIMARY KEY,
    parententryid integer DEFAULT 0      NOT NULL,
    parentitemid  integer DEFAULT 0      NOT NULL,
    entryid       integer                NOT NULL,
    itemid        integer                NOT NULL,
    itemvalue     character varying(256) NOT NULL,
    itemalias     character varying(510),
    enable        integer DEFAULT 1      NOT NULL,
    issystem      integer DEFAULT 1      NOT NULL,
    isdefault     integer DEFAULT 0      NOT NULL,
    description   character varying(510),
    extendfield1  character varying(510),
    extendfield2  character varying(510),
    extendfield3  character varying(510),
    extendfield4  character varying(510),
    extendfield5  character varying(510)
);
CREATE UNIQUE INDEX idx_22838_tbl_dataitem_idx1 ON tbl_dataitem USING BTREE (entryid, itemid);

CREATE TABLE tbl_datastruct
(
    entryid           integer NOT NULL PRIMARY KEY,
    structtype        integer,
    structname        character varying(256),
    structdescription character varying(512),
    attributeid       integer,
    attributename     character varying(256),
    attributetype     character varying(256),
    typedefine        character varying(512),
    description       character varying(512),
    extendfield1      character varying(512)
);

CREATE TABLE tbl_dbversionrecord
(
    serialno       serial                NOT NULL PRIMARY KEY,
    updatetime     timestamp NOT NULL,
    version        character varying(60) NOT NULL,
    module         character varying(510),
    lastmodifytime timestamp NOT NULL,
    feature        character varying(510)
);

CREATE TABLE tbl_department
(
    departmentid       integer                NOT NULL PRIMARY KEY,
    departmentname     character varying(256) NOT NULL,
    departmentlevel    character varying(40),
    departmentfunction character varying(80),
    parentdeprtid      integer,
    description        character varying(510),
    lastupdatedate     timestamp DEFAULT localtimestamp
);


CREATE TABLE tbl_dynamicconfig
(
    id         serial NOT NULL PRIMARY KEY,
    userid     integer NOT NULL,
    configtime timestamp NOT NULL,
    stationid  integer NOT NULL,
    hostid     integer NOT NULL,
    syncflag   integer DEFAULT '-2'::INTEGER NOT NULL,
    syncxml    text    NOT NULL,
    synctime   timestamp
);
CREATE INDEX idx_22948_idx_dynamicconfig_1 ON tbl_dynamicconfig USING BTREE (configtime, hostid);


CREATE TABLE tbl_employee
(
    employeeid    integer                NOT NULL PRIMARY KEY,
    departmentid  integer,
    employeename  character varying(256) NOT NULL,
    employeetype  integer,
    employeetitle integer,
    jobnumber     character varying(40),
    gender        integer,
    mobile        character varying(100),
    phone         character varying(100),
    email         character varying(256),
    address       character varying(510),
    postaddress   character varying(510),
    enable        integer                NOT NULL,
    description   character varying(510),
    isaddtempuser integer                NOT NULL,
    uservalidtime integer DEFAULT 172800
);
CREATE INDEX idx_22955_departmentid ON tbl_employee USING BTREE (departmentid);


CREATE TABLE tbl_enumdata
(
    entryid              integer NOT NULL PRIMARY KEY,
    attributetype        integer,
    attributename        character varying(256),
    attributedescription character varying(512),
    enumid               integer,
    enumtype             character varying(256),
    enumvalue            integer,
    enumdefine           character varying(512),
    description          character varying(512),
    extendfield1         character varying(512)
);


CREATE TABLE tbl_equipment
(
    stationid           integer                NOT NULL,
    equipmentid         integer                NOT NULL PRIMARY KEY,
    equipmentname       character varying(256) NOT NULL,
    equipmentno         character varying(256),
    equipmentmodule     character varying(256),
    equipmentstyle      character varying(256),
    assetstate          integer,
    price               double precision,
    usedlimit           double precision,
    useddate            timestamp,
    buydate             timestamp,
    vendor              character varying(510),
    unit                character varying(510),
    equipmentcategory   integer                NOT NULL,
    equipmenttype       integer                NOT NULL,
    equipmentclass      integer,
    equipmentstate      integer                NOT NULL,
    eventexpression     character varying(510),
    startdelay          double precision,
    enddelay            double precision,
    property            character varying(510),
    description         character varying(510),
    equipmenttemplateid integer,
    houseid             integer,
    monitorunitid       integer                NOT NULL,
    workstationid       integer,
    samplerunitid       integer                NOT NULL,
    displayindex        integer                NOT NULL,
    connectstate        integer                NOT NULL,
    updatetime          timestamp NOT NULL,
    parentequipmentid   character varying(510),
    ratedcapacity       character varying(510),
    installedmodule     text,
    projectname         character varying(510),
    contractno          character varying(510),
    installtime         timestamp,
    equipmentsn         character varying(510),
    so                  character varying(510),
    resourcestructureid integer DEFAULT 0,
    extvalue            json,
    photo               character varying(510)
);
CREATE INDEX idx_22971_idx_equipmentid_1 ON tbl_equipment USING BTREE (monitorunitid, samplerunitid);
CREATE INDEX idx_22971_idx_equipment_resourcestructureid ON tbl_equipment USING BTREE (resourcestructureid);
CREATE INDEX idx_22971_idx_equipment_stationid ON tbl_equipment USING BTREE (stationid, houseid);
CREATE INDEX idx_22971_idx_equipmenttemplateid ON tbl_equipment USING BTREE (equipmenttemplateid);

CREATE TABLE tbl_equipmentidmap
(
    oldstationid    integer,
    oldequipmentid  integer,
    kolostationid   integer,
    koloequipmentid integer,
    opostcode       integer
);

CREATE TABLE tbl_equipmentkeyvalue
(
    equipmenttype     integer NOT NULL,
    equipmentcategory integer NOT NULL,
    "minvalue"          integer,
    currentvalue      integer
);

ALTER TABLE ONLY tbl_equipmentkeyvalue
    ADD CONSTRAINT idx_23018_primary PRIMARY KEY (equipmenttype, equipmentcategory);

CREATE TABLE tbl_equipmentmaintain
(
    stationid      integer NOT NULL,
    equipmentid    integer NOT NULL,
    equipmentstate integer,
    starttime      timestamp,
    endtime        timestamp,
    userid         integer,
    description    character varying(510),
    extendfiled1   character varying(510)
);
ALTER TABLE ONLY tbl_equipmentmaintain ADD CONSTRAINT idx_23026_primary PRIMARY KEY (equipmentid, stationid);
CREATE INDEX idx_23026_idx_equipmentmaintain_id ON tbl_equipmentmaintain USING BTREE (stationid, equipmentid);

CREATE TABLE tbl_equipmentmask
(
    equipmentid integer NOT NULL,
    stationid   integer NOT NULL,
    timegroupid integer,
    reason      character varying(510),
    starttime   timestamp,
    endtime     timestamp,
    userid      integer
);
ALTER TABLE ONLY tbl_equipmentmask ADD CONSTRAINT idx_23031_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_equipmentprojectinfo
(
    stationid     integer NOT NULL,
    monitorunitid integer NOT NULL,
    equipmentid   integer NOT NULL,
    projectname   character varying(510),
    contractno    character varying(510),
    installtime   timestamp,
    equipmentsn   character varying(510),
    so            character varying(510)
);
CREATE UNIQUE INDEX idx_23034_tbl_equipmentprojectinfo_idx1 ON tbl_equipmentprojectinfo USING BTREE (stationid, equipmentid);

CREATE TABLE tbl_equipmenttemplate
(
    equipmenttemplateid   integer                NOT NULL PRIMARY KEY,
    equipmenttemplatename character varying(256) NOT NULL,
    parenttemplateid      integer                NOT NULL,
    memo                  character varying(510) NOT NULL,
    protocolcode          character varying(64),
    equipmentcategory     integer                NOT NULL,
    equipmenttype         integer                NOT NULL,
    property              character varying(510),
    description           character varying(510),
    equipmentstyle        character varying(256),
    unit                  character varying(510),
    vendor                character varying(510),
    photo                 character varying(510),
    equipmentbasetype     integer,
    stationcategory       integer,
    extendfield1          character varying(510),
    SecondaryCategory     integer
);

CREATE TABLE tbl_equipmenttemplateidmap
(
    oldequipmenttemplateid  integer,
    koloequipmenttemplateid integer
);

CREATE TABLE tbl_equiptemplatebaseconfirm
(
    equipmenttemplateid integer NOT NULL PRIMARY KEY,
    confirmtime         timestamp NOT NULL,
    confirmuser         integer NOT NULL,
    reason              text
);

CREATE TABLE tbl_event
(
    id                  serial                 NOT NULL PRIMARY KEY,
    equipmenttemplateid integer                NOT NULL,
    eventid             integer                NOT NULL,
    eventname           character varying(256) NOT NULL,
    starttype           integer                NOT NULL,
    endtype             integer                NOT NULL,
    startexpression     text,
    suppressexpression  text,
    eventcategory       integer                NOT NULL,
    signalid            integer,
    enable              integer                NOT NULL,
    visible             integer                NOT NULL,
    description         character varying(510),
    displayindex        integer,
    moduleno            integer DEFAULT 0      NOT NULL
);
CREATE INDEX idx_23100_idx_event_signal_1 ON tbl_event USING BTREE (equipmenttemplateid, signalid);
CREATE INDEX idx_23100_idx_tblevent_2 ON tbl_event USING BTREE (equipmenttemplateid, eventid);

CREATE TABLE tbl_eventcondition
(
    id                  serial               NOT NULL PRIMARY KEY,
    eventconditionid    integer              NOT NULL,
    equipmenttemplateid integer              NOT NULL,
    eventid             integer              NOT NULL,
    startoperation      character varying(8) NOT NULL,
    startcomparevalue   double precision     NOT NULL,
    startdelay          integer              NOT NULL,
    endoperation        character varying(8),
    endcomparevalue     double precision,
    enddelay            integer,
    frequency           integer,
    frequencythreshold  integer,
    meanings            character varying(510),
    equipmentstate      integer,
    basetypeid          numeric(12, 0),
    eventseverity       integer              NOT NULL,
    standardname        integer
);
CREATE INDEX idx_23120_idx_tbleventcondition_1 ON tbl_eventcondition USING BTREE (equipmenttemplateid, eventid);

CREATE TABLE tbl_eventex
(
    id                  serial  NOT NULL PRIMARY KEY,
    equipmenttemplateid integer NOT NULL,
    eventid             integer NOT NULL,
    turnover            integer,
    extendfield1        character varying(40)
);
CREATE INDEX idx_23128_idx_tbleventex_2 ON tbl_eventex USING BTREE (equipmenttemplateid, eventid);

CREATE TABLE tbl_eventlogaction
(
    logactionid        integer                NOT NULL,
    actionname         character varying(510) NOT NULL,
    stationid          integer                NOT NULL,
    monitorunitid      integer                NOT NULL,
    triggertype        integer                NOT NULL,
    startexpression    character varying(510),
    suppressexpression character varying(510),
    informmsg          character varying(510),
    description        character varying(510)
);

CREATE TABLE tbl_eventmask
(
    equipmentid integer NOT NULL,
    stationid   integer NOT NULL,
    eventid     integer NOT NULL,
    timegroupid integer,
    reason      character varying(510),
    starttime   timestamp,
    endtime     timestamp,
    userid      integer
);
ALTER TABLE ONLY tbl_eventmask ADD CONSTRAINT idx_23137_primary PRIMARY KEY (equipmentid, eventid, stationid);

CREATE TABLE tbl_eventmaskhistory
(
    sequenceid       character varying(256) NOT NULL,
    stationid        integer                NOT NULL,
    equipmentid      integer                NOT NULL,
    eventid          integer                NOT NULL,
    eventconditionid integer                NOT NULL,
    eventvalue       double precision,
    endvalue         double precision,
    meanings         character varying(510),
    basetypeid       numeric(12, 0),
    starttime        timestamp NOT NULL,
    endtime          timestamp
);

CREATE TABLE tbl_historycontrol
(
    stationid             integer                NOT NULL,
    stationname           character varying(510) NOT NULL,
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256) NOT NULL,
    controlid             integer                NOT NULL,
    controlname           character varying(256) NOT NULL,
    serialno              integer                NOT NULL,
    controlseverity       integer                NOT NULL,
    cmdtoken              text                   NOT NULL,
    controlphase          integer                NOT NULL,
    starttime             timestamp NOT NULL,
    endtime               timestamp,
    confirmtime           timestamp,
    confirmerid           integer,
    confirmername         character varying(510),
    controlresulttype     integer,
    controlresult         character varying(510),
    controlexecuterid     integer,
    controlexecuteridname character varying(510),
    controltype           integer,
    actionid              integer,
    description           character varying(510),
    retry                 integer,
    basetypeid            numeric(12, 0),
    basetypename          character varying(256),
    parametervalues       text                   NOT NULL,
    basecondid            numeric(12, 0)
);
CREATE UNIQUE INDEX idx_23307_tbl_historycontrol_idx1 ON tbl_historycontrol USING BTREE (starttime, stationid, equipmentid, controlid, serialno);


CREATE TABLE tbl_historycontrolmid
(
    stationid             integer                NOT NULL,
    stationname           character varying(510) NOT NULL,
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256) NOT NULL,
    controlid             integer                NOT NULL,
    controlname           character varying(256) NOT NULL,
    serialno              integer                NOT NULL,
    controlseverity       integer                NOT NULL,
    cmdtoken              text                   NOT NULL,
    controlphase          integer                NOT NULL,
    starttime             timestamp NOT NULL,
    endtime               timestamp,
    confirmtime           timestamp,
    confirmerid           integer,
    confirmername         character varying(510),
    controlresulttype     integer,
    controlresult         character varying(510),
    controlexecuterid     integer,
    controlexecuteridname character varying(510),
    controltype           integer,
    actionid              integer,
    description           character varying(510),
    retry                 integer,
    basetypeid            numeric(12, 0),
    basetypename          character varying(256),
    parametervalues       text                   NOT NULL,
    basecondid            numeric(12, 0)
);


CREATE TABLE tbl_historyevent
(
    sequenceid            character varying(256) NOT NULL,
    stationid             integer                NOT NULL,
    stationname           character varying(510) NOT NULL,
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256) NOT NULL,
    eventid               integer                NOT NULL,
    eventname             character varying(256) NOT NULL,
    eventconditionid      integer                NOT NULL,
    eventseverityid       integer                NOT NULL,
    eventseverity         character varying(256) NOT NULL,
    eventlevel            integer,
    starttime             timestamp NOT NULL,
    endtime               timestamp NOT NULL,
    canceltime            timestamp,
    canceluserid          integer,
    cancelusername        character varying(256),
    confirmtime           timestamp,
    confirmerid           integer,
    confirmername         character varying(256),
    eventvalue            double precision,
    endvalue              double precision,
    reversalnum           integer,
    meanings              character varying(510),
    eventfilepath         character varying(510),
    description           character varying(510),
    sourcehostid          integer,
    instructionid         character varying(510),
    instructionstatus     integer,
    standardalarmnameid   integer,
    standardalarmname     character varying(256),
    basetypeid            numeric(12, 0),
    basetypename          character varying(256),
    equipmentcategory     integer,
    equipmentcategoryname character varying(256),
    maintainstate         integer DEFAULT 0      NOT NULL,
    signalid              integer,
    relatesequenceid      character varying(256),
    eventcategoryid       integer,
    eventstateid          integer,
    centerid              integer,
    centername            character varying(256),
    structurename         character varying(256),
    monitorunitname       character varying(256),
    structureid           integer,
    stationcategoryid     integer,
    equipmentvendor       character varying(256),
    convergenceeventid    bigint  DEFAULT '0'::BIGINT,
    resourcestructureid   integer DEFAULT 0,
    baseequipmentid       integer DEFAULT 0
);
CREATE UNIQUE INDEX idx_23317_tbl_historyevent_idx1 ON tbl_historyevent USING BTREE (starttime, stationid, equipmentid, eventid, eventconditionid);
CREATE INDEX idx_23317_tbl_historyevent_idx2 ON tbl_historyevent USING BTREE (structureid, stationcategoryid, equipmentcategory, stationid, eventstateid, eventcategoryid, eventconditionid, starttime);
CREATE INDEX idx_23317_tbl_historyevent_idx3 ON tbl_historyevent USING BTREE (starttime, basetypeid, stationid, equipmentid, eventseverityid, signalid);

CREATE TABLE tbl_historyeventmask
(
    sequenceid       character varying(256) NOT NULL,
    stationid        integer                NOT NULL,
    equipmentid      integer                NOT NULL,
    eventid          integer                NOT NULL,
    eventconditionid integer                NOT NULL,
    eventvalue       double precision,
    endvalue         double precision,
    meanings         character varying(510),
    basetypeid       numeric(12, 0),
    starttime        timestamp NOT NULL,
    endtime          timestamp NOT NULL
);


CREATE TABLE tbl_historyeventmaskmid
(
    sequenceid       character varying(256) NOT NULL,
    stationid        integer                NOT NULL,
    equipmentid      integer                NOT NULL,
    eventid          integer                NOT NULL,
    eventconditionid integer                NOT NULL,
    eventvalue       double precision,
    endvalue         double precision,
    meanings         character varying(510),
    basetypeid       numeric(12, 0),
    starttime        timestamp NOT NULL,
    endtime          timestamp NOT NULL
);


CREATE TABLE tbl_historyeventmid
(
    sequenceid            character varying(256) NOT NULL,
    stationid             integer                NOT NULL,
    stationname           character varying(510) NOT NULL,
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256) NOT NULL,
    eventid               integer                NOT NULL,
    eventname             character varying(256) NOT NULL,
    eventconditionid      integer                NOT NULL,
    eventseverityid       integer                NOT NULL,
    eventseverity         character varying(256) NOT NULL,
    eventlevel            integer,
    starttime             timestamp NOT NULL,
    endtime               timestamp NOT NULL,
    canceltime            timestamp,
    canceluserid          integer,
    cancelusername        character varying(256),
    confirmtime           timestamp,
    confirmerid           integer,
    confirmername         character varying(256),
    eventvalue            double precision,
    endvalue              double precision,
    reversalnum           integer,
    meanings              character varying(510),
    eventfilepath         character varying(510),
    description           character varying(510),
    sourcehostid          integer,
    instructionid         character varying(510),
    instructionstatus     integer,
    standardalarmnameid   integer,
    standardalarmname     character varying(256),
    basetypeid            numeric(12, 0),
    basetypename          character varying(256),
    equipmentcategory     integer,
    equipmentcategoryname character varying(256),
    maintainstate         integer DEFAULT 0      NOT NULL,
    signalid              integer,
    relatesequenceid      character varying(256),
    eventcategoryid       integer,
    eventstateid          integer,
    centerid              integer,
    centername            character varying(256),
    structurename         character varying(256),
    monitorunitname       character varying(256),
    structureid           integer,
    stationcategoryid     integer,
    equipmentvendor       character varying(256),
    convergenceeventid    bigint  DEFAULT '0'::BIGINT,
    resourcestructureid   integer DEFAULT 0,
    baseequipmentid       integer DEFAULT 0
);

CREATE TABLE tbl_historypassword
(
    userid     integer NOT NULL,
    password   character varying(256),
    recordtime timestamp NOT NULL
);
ALTER TABLE ONLY tbl_historypassword ADD CONSTRAINT idx_23351_primary PRIMARY KEY (recordtime, userid);


CREATE TABLE tbl_historyselection
(
    historyselectionid serial                 NOT NULL PRIMARY KEY,
    userid             integer                NOT NULL,
    selectiontype      character varying(256) NOT NULL,
    selectionname      character varying(510) NOT NULL,
    selectioncontent   text                   NOT NULL,
    description        character varying(510),
    createtime         timestamp,
    queryinformation   text
);

CREATE TABLE tbl_hourlysignal
(
    stationid        integer           NOT NULL,
    equipmentid      integer           NOT NULL,
    signalid         integer           NOT NULL,
    recordtime       timestamp NOT NULL,
    datatype         integer,
    floatvalue       double precision,
    stringvalue      character varying(510),
    reporttime       timestamp NOT NULL,
    signalpropertyid integer DEFAULT 0 NOT NULL
);

CREATE TABLE tbl_house
(
    houseid        integer                NOT NULL,
    stationid      integer                NOT NULL,
    housename      character varying(256) NOT NULL,
    description    character varying(510),
    lastupdatedate timestamp DEFAULT localtimestamp
);
ALTER TABLE ONLY tbl_house ADD CONSTRAINT idx_23370_primary PRIMARY KEY (houseid, stationid);

CREATE TABLE tbl_logicclassentry
(
    entryid       integer NOT NULL,
    entrycategory integer,
    logicclassid  integer,
    logicclass    character varying(256),
    standardtype  integer NOT NULL,
    description   character varying(510)
);
ALTER TABLE ONLY tbl_logicclassentry ADD CONSTRAINT idx_23466_primary PRIMARY KEY (entryid, standardtype);

CREATE TABLE tbl_loginformlist
(
    logactionid integer NOT NULL,
    informerid  integer NOT NULL,
    userid      integer,
    infotype    integer,
    description character varying(510)
);

CREATE TABLE tbl_logininfo
(
    userid    integer NOT NULL,
    logintype integer NOT NULL,
    logintime timestamp NOT NULL,
    ipaddress character varying(510)
);
ALTER TABLE ONLY tbl_logininfo ADD CONSTRAINT idx_23472_primary PRIMARY KEY (logintime, logintype, userid);

CREATE TABLE tbl_logininfomid
(
    userid    integer NOT NULL,
    logintype integer NOT NULL,
    logintime timestamp NOT NULL,
    ipaddress character varying(510)
);

CREATE TABLE tbl_middletbl
(
    businesstypeid    integer                NOT NULL,
    expressionid      integer                NOT NULL,
    serialid          integer                NOT NULL,
    businesstypename  character varying(510) NOT NULL,
    expressionname    character varying(510) NOT NULL,
    stationid         integer                NOT NULL,
    stationname       character varying(510) NOT NULL,
    equipmentid       integer                NOT NULL,
    equipmentname     character varying(256) NOT NULL,
    signalid          integer                NOT NULL,
    signalname        character varying(256) NOT NULL,
    signalcategory    integer                NOT NULL,
    datatype          integer                NOT NULL,
    floatvalue        double precision,
    stringvalue       character varying(256),
    datetimevalue     timestamp,
    sampletime        timestamp NOT NULL,
    showprecision     character varying(40),
    unit              character varying(128),
    basetypeid        numeric(12, 0),
    basetypename      character varying(256),
    eventseverity     integer,
    eventseverityname character varying(256),
    meanings          character varying(510),
    thresholdtype     integer,
    businessstate     integer,
    basecondid        numeric(12, 0),
    basemeaning       character varying(510)
);
CREATE UNIQUE INDEX idx_23505_tbl_middletbl_idx1 ON tbl_middletbl USING BTREE (expressionid, sampletime, stationid, equipmentid, signalid);
CREATE INDEX idx_23505_tbl_middletbl_idx2 ON tbl_middletbl USING BTREE (businesstypeid, expressionid, stationid, equipmentid, signalid, sampletime, floatvalue);

CREATE TABLE tbl_midxxxxmid
(
    businesstypeid    integer                NOT NULL,
    expressionid      integer                NOT NULL,
    serialid          integer                NOT NULL,
    businesstypename  character varying(510) NOT NULL,
    expressionname    character varying(510) NOT NULL,
    stationid         integer                NOT NULL,
    stationname       character varying(510) NOT NULL,
    equipmentid       integer                NOT NULL,
    equipmentname     character varying(256) NOT NULL,
    signalid          integer                NOT NULL,
    signalname        character varying(256) NOT NULL,
    signalcategory    integer                NOT NULL,
    datatype          integer                NOT NULL,
    floatvalue        double precision,
    stringvalue       character varying(256),
    datetimevalue     timestamp,
    sampletime        timestamp NOT NULL,
    showprecision     character varying(40),
    unit              character varying(128),
    basetypeid        numeric(12, 0),
    basetypename      character varying(256),
    eventseverity     integer,
    eventseverityname character varying(256),
    meanings          character varying(510),
    thresholdtype     integer,
    businessstate     integer,
    basecondid        numeric(12, 0),
    basemeaning       character varying(510),
    comefromtablename character varying(128)  NOT NULL
);

CREATE TABLE tbl_monitorunitprojectinfo
(
    stationid     integer NOT NULL,
    monitorunitid integer NOT NULL,
    projectname   character varying(510),
    contractno    character varying(510),
    installtime   timestamp
);
CREATE UNIQUE INDEX idx_23515_tbl_muprojectinfo_idx1 ON tbl_monitorunitprojectinfo USING BTREE (stationid, monitorunitid);

CREATE TABLE tbl_mufullcfgstate
(
    stationid      integer               NOT NULL,
    monitorunitid  integer               NOT NULL,
    configfilecode character varying(64) NOT NULL,
    updatetime     timestamp NOT NULL,
    state          integer               NOT NULL
);
ALTER TABLE ONLY tbl_mufullcfgstate ADD CONSTRAINT idx_23520_primary PRIMARY KEY (monitorunitid, stationid);

CREATE TABLE tbl_musyncrecord
(
    recordid      serial  NOT NULL,
    stationid     integer NOT NULL,
    taskid        integer,
    monitorunitid integer NOT NULL,
    syncresult    integer NOT NULL,
    synctime      timestamp,
    description   character varying(510)
);
ALTER TABLE ONLY tbl_musyncrecord ADD CONSTRAINT idx_23524_primary PRIMARY KEY (recordid, stationid);

CREATE TABLE tbl_musynctask
(
    taskid        serial  NOT NULL PRIMARY KEY,
    stationid     integer NOT NULL,
    monitorunitid integer NOT NULL,
    syncstate     integer NOT NULL,
    syncrule      integer NOT NULL,
    plantime      timestamp,
    begintime     timestamp,
    endtime       timestamp,
    updatetime    timestamp,
    maxretrycount integer NOT NULL,
    retrycount    integer NOT NULL,
    description   character varying(510)
);

CREATE TABLE tbl_newinstation
(
    ipaddress    character varying(256) NOT NULL,
    updatetime   timestamp NOT NULL,
    extendfield1 character varying(510),
    extendfield2 character varying(510),
    extendfield3 character varying(510),
    id           serial                 NOT NULL PRIMARY KEY
);

CREATE TABLE tbl_newsystemconfigs
(
    systemconfigid    integer NOT NULL PRIMARY KEY,
    systemconfigkey   character varying(510),
    systemconfigvalue character varying(510),
    description       character varying(510)
);

CREATE TABLE tbl_notificationsn
(
    serialno numeric(14, 0)
);

CREATE TABLE tbl_notifycommand
(
    sequenceid        numeric(22, 17) NOT NULL PRIMARY KEY,
    stationid         integer         NOT NULL,
    equipmentid       integer         NOT NULL,
    commandid         integer         NOT NULL,
    commandseverity   integer,
    cmdtoken          text,
    commandphase      integer,
    starttime         timestamp NOT NULL,
    endtime           timestamp,
    confirmtime       timestamp,
    confirmerid       integer,
    commandresulttype integer,
    commandresult     character varying(510),
    commandexecuterid integer,
    description       character varying(510)
);

CREATE TABLE tbl_onlineuser
(
    businessmoduletype integer,
    logonid            character varying(510),
    userid             integer NOT NULL,
    username           character varying(510),
    logintime          timestamp,
    lastupdatetime     timestamp,
    loginip            character varying(510),
    description        character varying(510),
    status             integer,
    token              character varying(510),
    extendfield1       character varying(510),
    extendfield2       character varying(510),
    extendfield3       character varying(510)
);

CREATE TABLE tbl_operation
(
    operationid       integer                NOT NULL PRIMARY KEY,
    operationcategory integer,
    operationname     character varying(256) NOT NULL,
    description       character varying(510),
    menusitemid       integer
);

CREATE TABLE tbl_operationdetail
(
    userid        integer               NOT NULL,
    objectid      character varying(256),
    objecttype    integer               NOT NULL,
    propertyname  character varying(256),
    operationtime timestamp NOT NULL,
    operationtype character varying(128) NOT NULL,
    oldvalue      character varying(8000),
    newvalue      character varying(8000)
);

CREATE TABLE tbl_operationdetailmid
(
    userid        integer               NOT NULL,
    objectid      character varying(256),
    objecttype    integer               NOT NULL,
    propertyname  character varying(256),
    operationtime timestamp NOT NULL,
    operationtype character varying(128) NOT NULL,
    oldvalue      character varying(510),
    newvalue      character varying(510)
);

CREATE TABLE tbl_operationgroup
(
    groupid     integer                NOT NULL PRIMARY KEY,
    groupname   character varying(256) NOT NULL,
    description character varying(510)
);

CREATE TABLE tbl_operationgroupmap
(
    operationid integer NOT NULL,
    groupid     integer NOT NULL
);
ALTER TABLE ONLY tbl_operationgroupmap ADD CONSTRAINT idx_23574_primary PRIMARY KEY (groupid, operationid);

CREATE TABLE tbl_operationrecord
(
    userid           integer                NOT NULL,
    stationid        integer,
    stationname      character varying(510),
    operation        integer                NOT NULL,
    operationtime    timestamp NOT NULL,
    operationtype    integer,
    operationcontent character varying(6000) NOT NULL
);
CREATE INDEX idx_23577_idx_operationrecord ON tbl_operationrecord USING BTREE (operationtime, userid, operation);

CREATE TABLE tbl_operationrecordmid
(
    userid           integer                NOT NULL,
    stationid        integer,
    stationname      character varying(510),
    operation        integer                NOT NULL,
    operationtime    timestamp NOT NULL,
    operationtype    integer,
    operationcontent character varying(510) NOT NULL
);

CREATE TABLE tbl_originbussinesscategorymap
(
    equipmenttemplateid integer NOT NULL PRIMARY KEY,
    origincategory      integer NOT NULL
);

CREATE TABLE tbl_primaryalarm
(
    filterid           integer NOT NULL,
    stationcategory    integer NOT NULL,
    primarystationid   integer NOT NULL,
    primaryequipmentid integer NOT NULL,
    primarybasetypeid  integer NOT NULL
);
CREATE UNIQUE INDEX idx_23720_tbl_primaryalarm_id1 ON tbl_primaryalarm USING BTREE (filterid, stationcategory, primarystationid, primaryequipmentid, primarybasetypeid);

CREATE TABLE tbl_primarykeyidentity
(
    tableid     integer NOT NULL PRIMARY KEY,
    tablename   character varying(60),
    description character varying(510)
);

CREATE TABLE tbl_primarykeyvalue
(
    tableid      integer NOT NULL,
    postalcode   integer NOT NULL,
    "minvalue"     integer,
    currentvalue integer
);
ALTER TABLE ONLY tbl_primarykeyvalue ADD CONSTRAINT idx_23726_primary PRIMARY KEY (postalcode, tableid);

CREATE TABLE tbl_primarysecondmaprule
(
    filterid     integer                NOT NULL PRIMARY KEY,
    rulename     character varying(510) NOT NULL,
    processdelay integer                NOT NULL,
    description  character varying(510)
);

CREATE TABLE tbl_projectstatehouse
(
    houseid        integer                NOT NULL,
    stationid      integer                NOT NULL,
    housename      character varying(256) NOT NULL,
    description    character varying(510),
    reason         character varying(510),
    starttime      timestamp,
    endtime        timestamp,
    userid         integer,
    lastupdatedate timestamp NOT NULL
);
ALTER TABLE ONLY tbl_projectstatehouse ADD CONSTRAINT idx_23734_primary PRIMARY KEY (houseid, stationid);

CREATE TABLE tbl_projectstateoperation
(
    operationid   serial  NOT NULL PRIMARY KEY,
    operationtype integer NOT NULL,
    operation     character varying(200),
    stationid     integer,
    houseid       integer,
    equipmentid   integer,
    reason        character varying(510),
    starttime     timestamp,
    endtime       timestamp,
    userid        integer,
    operationdate timestamp NOT NULL
);

CREATE TABLE tbl_projectstatestation
(
    stationid      integer NOT NULL PRIMARY KEY,
    reason         character varying(510),
    starttime      timestamp,
    endtime        timestamp,
    userid         integer,
    lastupdatedate timestamp NOT NULL
);

CREATE TABLE tbl_replicatelogs
(
    logid      serial NOT NULL PRIMARY KEY,
    logstr     text   NOT NULL,
    inserttime timestamp DEFAULT localtimestamp NOT NULL
);

CREATE TABLE tbl_rmuworkstationservice
(
    workstationid      integer NOT NULL PRIMARY KEY,
    workstationname    character varying(510),
    scloginip          character varying(128),
    scserviceport      character varying(128),
    scloginport        character varying(128),
    suurlsuffix        character varying(128),
    heartperiod        character varying(128),
    username           character varying(128),
    password           character varying(128),
    ftpuser            character varying(128),
    ftppass            character varying(128),
    suserviceip        character varying(128),
    suserviceport      character varying(128),
    suver              character varying(128),
    askconfiginterval  character varying(128),
    lastaskfactorytime timestamp,
    extendfield1       character varying(256),
    extendfield2       character varying(256)
);

CREATE TABLE tbl_sampleridmap
(
    oldsamplerid  integer,
    kolosamplerid integer
);

CREATE TABLE tbl_secondaryalarm
(
    filterid             integer NOT NULL,
    stationcategory      integer NOT NULL,
    secondarystationid   integer NOT NULL,
    secondaryequipmentid integer NOT NULL,
    secondarybasetypeid  integer NOT NULL
);
CREATE UNIQUE INDEX idx_23906_tbl_secondaryalarm_id1 ON tbl_secondaryalarm USING BTREE (filterid, stationcategory, secondarystationid, secondaryequipmentid, secondarybasetypeid);

CREATE TABLE tbl_serialno
(
    tablename character varying(250) NOT NULL,
    serialno  numeric(12, 0)         NOT NULL
);

CREATE TABLE tbl_signal
(
    id                  serial                 NOT NULL PRIMARY KEY,
    equipmenttemplateid integer                NOT NULL,
    signalid            integer                NOT NULL,
    enable              integer                NOT NULL,
    visible             integer                NOT NULL,
    description         character varying(510),
    signalname          character varying(256) NOT NULL,
    signalcategory      integer                NOT NULL,
    signaltype          integer                NOT NULL,
    channelno           integer                NOT NULL,
    channeltype         integer                NOT NULL,
    expression          text,
    datatype            integer,
    showprecision       character varying(40),
    unit                character varying(128),
    storeinterval       double precision,
    absvaluethreshold   double precision,
    percentthreshold    double precision,
    staticsperiod       integer,
    basetypeid          numeric(12, 0),
    chargestoreinterval double precision,
    chargeabsvalue      double precision,
    displayindex        integer                NOT NULL,
    mdbsignalid         integer,
    moduleno            integer DEFAULT 0      NOT NULL
);
CREATE INDEX idx_23913_idx_tblsignal_2 ON tbl_signal USING BTREE (equipmenttemplateid, signalid);
CREATE INDEX idx_23913_idx_tblsignal_3 ON tbl_signal USING BTREE (equipmenttemplateid, basetypeid);
CREATE INDEX idx_23913_idx_basetypeid ON tbl_signal USING BTREE (basetypeid);

CREATE TABLE tbl_signalmeanings
(
    id                  serial   NOT NULL PRIMARY KEY,
    equipmenttemplateid integer  NOT NULL,
    signalid            integer  NOT NULL,
    statevalue          smallint NOT NULL,
    meanings            character varying(510),
    basecondid          numeric(12, 0)
);
CREATE UNIQUE INDEX idx_23933_clustered ON tbl_signalmeanings USING BTREE (equipmenttemplateid, signalid, statevalue);

CREATE TABLE tbl_signalproperty
(
    id                  serial  NOT NULL PRIMARY KEY,
    equipmenttemplateid integer NOT NULL,
    signalid            integer NOT NULL,
    signalpropertyid    integer NOT NULL
);
CREATE UNIQUE INDEX idx_23938_index_27 ON tbl_signalproperty USING BTREE (equipmenttemplateid, signalid, signalpropertyid);

CREATE TABLE tbl_signalstatistics
(
    stationid      integer                NOT NULL,
    stationname    character varying(510) NOT NULL,
    equipmentid    integer                NOT NULL,
    equipmentname  character varying(256) NOT NULL,
    signalid       integer                NOT NULL,
    signalname     character varying(256) NOT NULL,
    statisticstime timestamp NOT NULL,
    "minvalue"       double precision,
    mintime        timestamp,
    "maxvalue"       double precision,
    maxtime        timestamp,
    avgvalue       double precision,
    avgtime        timestamp,
    basetypeid     numeric(12, 0),
    basetypename   character varying(256)
);
CREATE UNIQUE INDEX idx_23942_tbl_signalstatistics_idx1 ON tbl_signalstatistics USING BTREE (statisticstime, stationid, equipmentid, signalid);

CREATE TABLE tbl_signalstatisticsmid
(
    stationid      integer                NOT NULL,
    stationname    character varying(510) NOT NULL,
    equipmentid    integer                NOT NULL,
    equipmentname  character varying(256) NOT NULL,
    signalid       integer                NOT NULL,
    signalname     character varying(256) NOT NULL,
    statisticstime timestamp NOT NULL,
    "minvalue"       double precision,
    mintime        timestamp,
    "maxvalue"       double precision,
    maxtime        timestamp,
    avgvalue       double precision,
    avgtime        timestamp,
    basetypeid     numeric(12, 0),
    basetypename   character varying(256)
);

CREATE TABLE tbl_spaceusedresult
(
    tablename text,
    rows      numeric(17, 0),
    reserved  numeric(17, 0),
    data      numeric(17, 0),
    indexp    numeric(17, 0),
    unused    numeric(17, 0)
);

CREATE TABLE tbl_specialtygroup
(
    specialtygroupid   integer                NOT NULL PRIMARY KEY,
    specialtygroupname character varying(256) NOT NULL,
    description        character varying(510)
);

CREATE TABLE tbl_specialtygroupmap
(
    specialtygroupid integer NOT NULL,
    entryitemid      integer NOT NULL,
    operation        character varying(510)
);
ALTER TABLE ONLY tbl_specialtygroupmap ADD CONSTRAINT idx_23975_primary PRIMARY KEY (entryitemid, specialtygroupid);

CREATE TABLE tbl_station
(
    stationid       integer                NOT NULL PRIMARY KEY,
    stationname     character varying(510) NOT NULL,
    latitude        numeric(22, 17),
    longitude       numeric(22, 17),
    setuptime       timestamp,
    companyid       integer,
    connectstate    integer DEFAULT 2      NOT NULL,
    updatetime      timestamp NOT NULL,
    stationcategory integer                NOT NULL,
    stationgrade    integer                NOT NULL,
    stationstate    integer                NOT NULL,
    contactid       integer,
    supporttime     integer,
    onwaytime       double precision,
    surplustime     double precision,
    floorno         character varying(100),
    proplist        character varying(510),
    acreage         double precision,
    buildingtype    integer,
    containnode     integer DEFAULT 0      NOT NULL,
    description     character varying(510),
    bordnumber      integer,
    centerid        integer                NOT NULL,
    enable          integer DEFAULT 1      NOT NULL,
    starttime       timestamp,
    endtime         timestamp,
    projectname     character varying(510),
    contractno      character varying(510),
    installtime     timestamp
);

CREATE TABLE tbl_stationbasemap
(
    stationbasetype integer NOT NULL,
    stationcategory integer NOT NULL,
    standardtype    integer NOT NULL
);
ALTER TABLE ONLY tbl_stationbasemap ADD CONSTRAINT idx_24036_primary PRIMARY KEY (standardtype, stationbasetype, stationcategory);

CREATE TABLE tbl_stationbasetype
(
    id         integer NOT NULL,
    standardid integer NOT NULL,
    type       character varying(256)
);
ALTER TABLE ONLY tbl_stationbasetype ADD CONSTRAINT idx_24039_primary PRIMARY KEY (id, standardid);

CREATE TABLE tbl_stationidmap
(
    oldstationid  integer,
    kolostationid integer,
    opostcode     integer
);

CREATE TABLE tbl_stationmask
(
    stationid   integer NOT NULL PRIMARY KEY,
    timegroupid integer,
    reason      character varying(510),
    starttime   timestamp,
    endtime     timestamp,
    userid      integer
);

CREATE TABLE tbl_stationprojectinfo
(
    stationid   integer NOT NULL PRIMARY KEY,
    projectname character varying(510),
    contractno  character varying(510),
    installtime timestamp
);

CREATE TABLE tbl_stationstructure
(
    structureid       integer                NOT NULL PRIMARY KEY,
    structuregroupid  integer                NOT NULL,
    parentstructureid integer                NOT NULL,
    structurename     character varying(256) NOT NULL,
    isungroup         integer                NOT NULL,
    structuretype     integer                NOT NULL,
    mapzoom           double precision,
    longitude         numeric(22, 17),
    latitude          numeric(22, 17),
    description       character varying(510),
    levelpath         character varying(400) NOT NULL,
    enable            integer                NOT NULL
);

CREATE TABLE tbl_stationstructureidmap
(
    oldstructureid  integer,
    kolostructureid integer,
    opostcode       integer
);

CREATE TABLE tbl_stationstructuremap
(
    structureid integer NOT NULL,
    stationid   integer NOT NULL
);
ALTER TABLE ONLY tbl_stationstructuremap ADD CONSTRAINT idx_24071_primary PRIMARY KEY (stationid, structureid);

CREATE TABLE tbl_stationswatchmap
(
    swatchstationid integer NOT NULL,
    stationid       integer NOT NULL
);

CREATE TABLE tbl_stationvendormap
(
    stationid  integer               NOT NULL,
    vendorname character varying(100) NOT NULL
);
ALTER TABLE ONLY tbl_stationvendormap ADD CONSTRAINT idx_24077_primary PRIMARY KEY (stationid, vendorname);

CREATE TABLE tbl_stationvip
(
    stationid   integer                NOT NULL,
    stationname character varying(510) NOT NULL,
    starttime   timestamp NOT NULL,
    endtime     timestamp NOT NULL,
    extendfield character varying(510)
);

CREATE TABLE tbl_swatchstation
(
    swatchstationid   serial                NOT NULL PRIMARY KEY,
    swatchstationname character varying(256) NOT NULL,
    stationid         integer                NOT NULL,
    createtime        timestamp NOT NULL,
    description       character varying(510)
);

CREATE TABLE tbl_sysconfig
(
    configkey   character varying(510) NOT NULL PRIMARY KEY,
    configvalue character varying(2048)
);

CREATE TABLE tbl_timegroup
(
    timegroupid        integer                NOT NULL PRIMARY KEY,
    timegroupcategory  integer                NOT NULL,
    timegroupname      character varying(256) NOT NULL,
    timegrouptype      smallint               NOT NULL,
    timegroupexception integer                NOT NULL,
    starttime          timestamp NOT NULL,
    endtime            timestamp NOT NULL,
    lastupdatedate     timestamp
);

CREATE TABLE tbl_timegroupset
(
    timegroupsetid   integer                NOT NULL PRIMARY KEY,
    timegroupsetname character varying(256) NOT NULL
);

CREATE TABLE tbl_timegroupspan
(
    timespanid     integer                NOT NULL PRIMARY KEY,
    timegroupid    integer                NOT NULL,
    starttime      timestamp,
    endtime        timestamp,
    week           smallint,
    timespanchar   character varying(510) NOT NULL,
    lastupdatedate timestamp
);

CREATE TABLE tbl_userrole
(
    roleid      integer                NOT NULL PRIMARY KEY,
    rolename    character varying(256) NOT NULL,
    description character varying(510)
);

CREATE TABLE tbl_userrolemap
(
    userid integer NOT NULL,
    roleid integer NOT NULL
);
ALTER TABLE ONLY tbl_userrolemap ADD CONSTRAINT idx_24174_primary PRIMARY KEY (roleid, userid);

CREATE TABLE tbl_userroleright
(
    roleid        integer NOT NULL,
    operationid   integer NOT NULL,
    operationtype integer NOT NULL
);
ALTER TABLE ONLY tbl_userroleright ADD CONSTRAINT idx_24177_primary PRIMARY KEY (operationid, operationtype, roleid);

CREATE TABLE tbl_workstation
(
    workstationid   integer                NOT NULL PRIMARY KEY,
    workstationname character varying(510) NOT NULL,
    workstationtype integer                NOT NULL,
    ipaddress       character varying(128)  NOT NULL,
    parentid        integer DEFAULT 0      NOT NULL,
    connectstate    integer                NOT NULL,
    updatetime      timestamp NOT NULL,
    isused          integer                NOT NULL,
    cpu             double precision,
    memory          double precision,
    threadcount     integer,
    diskfreespace   double precision,
    dbfreespace     double precision,
    lastcommtime    timestamp
);

CREATE TABLE tbl_workstationex
(
    workstationid integer,
    content       character varying(1000)
);

CREATE TABLE tbl_workstationmap
(
    sitewebworkstationid   integer,
    koloworkstationid      integer,
    sitewebworkstationname character varying(256),
    koloworkstationname    character varying(256)
);

CREATE TABLE tbl_writebackentry
(
    entryid       integer NOT NULL PRIMARY KEY,
    entrycategory integer,
    entryname     character varying(256),
    entrytitle    character varying(256),
    entryalias    character varying(510),
    enable        integer NOT NULL,
    description   character varying(510)
);

CREATE TABLE tsl_acrossmonitorunitsignal
(
    stationid     integer NOT NULL,
    monitorunitid integer NOT NULL,
    equipmentid   integer NOT NULL,
    signalid      integer NOT NULL,
    expression    text
);
ALTER TABLE ONLY tsl_acrossmonitorunitsignal ADD CONSTRAINT idx_24240_primary PRIMARY KEY (equipmentid, signalid, stationid);

CREATE TABLE tsl_activeevent
(
    stationid        integer                NOT NULL,
    equipmentid      integer                NOT NULL,
    eventid          integer                NOT NULL,
    eventconditionid integer                NOT NULL,
    sequenceid       character varying(256) NOT NULL PRIMARY KEY,
    starttime        timestamp NOT NULL,
    endtime          timestamp,
    resetsequenceid  character varying(256),
    eventvalue       double precision,
    endvalue         double precision,
    reversalnum      integer,
    meanings         character varying(510),
    basetypeid       numeric(12, 0)
);
CREATE INDEX idx_24245_tsl_activeevent_id2 ON tsl_activeevent USING BTREE (resetsequenceid);
CREATE INDEX idx_24245_idx_tsl_activeevent_3 ON tsl_activeevent USING BTREE (stationid, equipmentid, eventid, eventconditionid);

CREATE TABLE tsl_channelmap
(
    samplerunitid     integer NOT NULL PRIMARY KEY,
    monitorunitid     integer NOT NULL,
    originalchannelno integer NOT NULL,
    standardchannelno integer NOT NULL
);

CREATE TABLE tsl_dataservercapacity
(
    dataserverid integer NOT NULL PRIMARY KEY,
    capacity     integer,
    description  character varying(510)
);

CREATE TABLE tsl_deviceportmap
(
    serialdeviceid integer NOT NULL,
    serialportid   integer NOT NULL,
    portid         integer NOT NULL
);
ALTER TABLE ONLY tsl_deviceportmap ADD CONSTRAINT idx_24256_primary PRIMARY KEY (serialdeviceid, serialportid, portid);

CREATE TABLE tsl_ipdevice
(
    deviceid     integer                NOT NULL PRIMARY KEY,
    deviceno     character varying(256) NOT NULL,
    devicename   character varying(256) NOT NULL,
    protocoltype integer                NOT NULL,
    ipaddress    character varying(256) NOT NULL
);

CREATE TABLE tsl_monitorunit
(
    monitorunitid        integer                NOT NULL PRIMARY KEY,
    monitorunitname      character varying(256) NOT NULL,
    monitorunitcategory  integer                NOT NULL,
    monitorunitcode      character varying(256) NOT NULL,
    workstationid        integer,
    stationid            integer,
    ipaddress            character varying(256),
    runmode              integer,
    configfilecode       character varying(64),
    configupdatetime     timestamp,
    sampleconfigcode     character varying(64),
    softwareversion      character varying(128),
    description          character varying(510),
    starttime            timestamp,
    heartbeattime        timestamp,
    connectstate         integer DEFAULT 2      NOT NULL,
    updatetime           timestamp NOT NULL,
    issync               integer DEFAULT 1      NOT NULL,
    synctime             timestamp,
    isconfigok           integer DEFAULT 1      NOT NULL,
    configfilecode_old   character varying(64),
    sampleconfigcode_old character varying(64),
    appcongfigid         integer,
    candistribute        integer                NOT NULL,
    enable               integer                NOT NULL,
    projectname          character varying(510),
    contractno           character varying(510),
    installtime          timestamp,
    fsu                  integer
);
CREATE INDEX idx_24262_idx_monitorunit_stationid ON tsl_monitorunit USING BTREE (stationid);
CREATE INDEX idx_24262_idx_monitorunit_workstationid ON tsl_monitorunit USING BTREE (workstationid);

CREATE TABLE tsl_monitorunitconfig
(
    id                       serial  NOT NULL PRIMARY KEY,
    appconfigid              integer NOT NULL,
    sitewebtimeout           integer NOT NULL,
    retrytimes               integer NOT NULL,
    heartbeat                integer NOT NULL,
    equipmenttimeout         integer NOT NULL,
    portinterruptcount       integer NOT NULL,
    portinitializeinternal   integer NOT NULL,
    maxportinitializetimes   integer NOT NULL,
    portquerytimeout         integer NOT NULL,
    datasavetimes            integer NOT NULL,
    historysignalsavedtimes  integer NOT NULL,
    historybatterysavedtimes integer NOT NULL,
    historyeventsavedtimes   integer NOT NULL,
    cardrecordsavedcount     integer NOT NULL,
    controllog               integer NOT NULL,
    ipaddressds              character varying(256)
);

CREATE TABLE tsl_monitorunitcontrol
(
    stationid       integer NOT NULL,
    monitorunitid   integer NOT NULL,
    equipmentid     integer NOT NULL,
    controlid       integer NOT NULL,
    targetcontrol   integer NOT NULL,
    logicexpression text
);
ALTER TABLE ONLY tsl_monitorunitcontrol ADD CONSTRAINT idx_24283_primary PRIMARY KEY (controlid, equipmentid, stationid);

CREATE TABLE tsl_monitorunitevent
(
    stationid          integer NOT NULL,
    monitorunitid      integer NOT NULL,
    equipmentid        integer NOT NULL,
    eventid            integer NOT NULL,
    startexpression    character varying(510),
    suppressexpression text
);
ALTER TABLE ONLY tsl_monitorunitevent ADD CONSTRAINT idx_24299_primary PRIMARY KEY (equipmentid, eventid, stationid);

CREATE TABLE tsl_monitorunitextend
(
    monitorunitid integer NOT NULL PRIMARY KEY,
    description   character varying(510),
    extendfiled1  character varying(510),
    extendfiled2  character varying(510),
    extendfiled3  character varying(510),
    extendfiled4  character varying(510),
    extendfiled5  character varying(510)
);

CREATE TABLE tsl_monitorunitipmap
(
    monitorunitid integer NOT NULL,
    newipaddress  character varying(256),
    oldipaddress  character varying(256),
    recordtime    timestamp,
    issync        integer NOT NULL,
    isconflict    integer NOT NULL,
    description   character varying(510)
);

CREATE TABLE tsl_monitorunitsignal
(
    stationid              integer NOT NULL,
    monitorunitid          integer NOT NULL,
    equipmentid            integer NOT NULL,
    signalid               integer NOT NULL,
    referencesamplerunitid integer,
    referencechannelno     integer,
    expression             text,
    instancetype           integer NOT NULL
);
ALTER TABLE ONLY tsl_monitorunitsignal ADD CONSTRAINT idx_24318_primary PRIMARY KEY (equipmentid, signalid, stationid);

CREATE TABLE tsl_port
(
    id                serial                 NOT NULL PRIMARY KEY,
    portid            integer                NOT NULL,
    monitorunitid     integer                NOT NULL,
    portno            integer                NOT NULL,
    portname          character varying(256) NOT NULL,
    porttype          integer                NOT NULL,
    setting           character varying(510),
    phonenumber       character varying(256),
    linksamplerunitid integer,
    description       character varying(510)
);
CREATE INDEX idx_24324_idx_port_monitorunitid ON tsl_port USING BTREE (monitorunitid, portid);

CREATE TABLE tsl_realtimerouting
(
    dataserverid  integer NOT NULL,
    monitorunitid integer NOT NULL
);
ALTER TABLE ONLY tsl_realtimerouting ADD CONSTRAINT idx_24330_primary PRIMARY KEY (dataserverid, monitorunitid);
CREATE UNIQUE INDEX idx_24330_idx_monitorunitid_routing ON tsl_realtimerouting USING BTREE (monitorunitid);

CREATE TABLE tsl_routedistribution
(
    dataserverid  integer NOT NULL,
    monitorunitid integer NOT NULL,
    description   character varying(510)
);
ALTER TABLE ONLY tsl_routedistribution ADD CONSTRAINT idx_24333_primary PRIMARY KEY (dataserverid, monitorunitid);

CREATE TABLE tsl_sampler
(
    samplerid          serial                 NOT NULL PRIMARY KEY,
    samplername        character varying(256) NOT NULL,
    samplertype        smallint               NOT NULL,
    protocolcode       character varying(64)  NOT NULL,
    dllcode            character varying(510),
    dllversion         character varying(64),
    protocolfilepath   character varying(510),
    dllfilepath        character varying(510),
    dllpath            character varying(510),
    setting            character varying(510),
    description        character varying(510),
    socode             character varying(510),
    sopath             character varying(510),
    uploadprotocolfile integer DEFAULT 0
);
CREATE UNIQUE INDEX idx_24337_uniqueprotocolcode ON tsl_sampler USING BTREE (protocolcode);

CREATE TABLE tsl_samplerunit
(
    id                  serial                NOT NULL PRIMARY KEY,
    samplerunitid       integer                NOT NULL,
    portid              integer                NOT NULL,
    monitorunitid       integer                NOT NULL,
    samplerid           integer                NOT NULL,
    parentsamplerunitid integer                NOT NULL,
    samplertype         integer                NOT NULL,
    samplerunitname     character varying(256) NOT NULL,
    address             integer                NOT NULL,
    spunitinterval      double precision,
    dllpath             character varying(256),
    connectstate        integer                NOT NULL,
    updatetime          timestamp NOT NULL,
    phonenumber         character varying(256),
    description         character varying(510)
);
CREATE INDEX idx_24345_idx_samplerunit_1 ON tsl_samplerunit USING BTREE (monitorunitid, samplerunitid);
CREATE INDEX idx_24345_idx_samplerunit_monitorunitid ON tsl_samplerunit USING BTREE (monitorunitid, portid, samplerunitid);
CREATE INDEX idx_24345_idx_samplerunit_samplerunitid ON tsl_samplerunit USING BTREE (samplerunitid);


CREATE TABLE tsl_subscribesignal
(
    stationid          integer NOT NULL,
    hostid             integer NOT NULL,
    equipmentid        integer NOT NULL,
    signalid           integer NOT NULL,
    subscribetype      integer NOT NULL,
    lastsampledatetime timestamp,
    lastupdatedatetime timestamp,
    subscribedatetime  timestamp
);
ALTER TABLE ONLY tsl_subscribesignal ADD CONSTRAINT idx_24351_primary PRIMARY KEY (equipmentid, hostid, signalid, stationid);
CREATE INDEX idx_24351_idx_subscribesignal_hostid ON tsl_subscribesignal USING BTREE (hostid, lastupdatedatetime);