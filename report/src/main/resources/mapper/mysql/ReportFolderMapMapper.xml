<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportFolderMapMapper">

    <resultMap id="folderWithReportsResultMap" type="com.siteweb.report.dto.FolderWithReportsDTO">
        <id property="folderId" column="folderId"/>
        <collection property="reports" ofType="com.siteweb.report.dto.ReportFolderTreeDTO">
            <id property="id" column="reportId"/>
            <result property="name" column="reportName"/>
            <result property="type" column="type"/>
            <result property="sortIndex" column="sortIndex"/>
        </collection>
    </resultMap>

    <select id="selectReportMappings" resultMap="folderWithReportsResultMap">
        SELECT m.folderId AS folderId, r.ReportId AS reportId, r.ReportName AS reportName, 'report' AS type, m.sortIndex AS sortIndex
        FROM reportfoldermap m
        INNER JOIN report r ON m.reportId = r.ReportId
        WHERE m.folderId IN
        <foreach collection="folderIds" item="folderId" open="(" close=")" separator=",">
            #{folderId}
        </foreach>
        ORDER BY m.sortIndex
    </select>
    <select id="selectUnassignedReports" resultType="com.siteweb.report.dto.ReportFolderTreeDTO">
        SELECT r.ReportId AS id, r.ReportName AS name, 'report' AS type FROM report r
        WHERE NOT EXISTS (
        SELECT 1 FROM reportfoldermap m WHERE m.reportId = r.ReportId
        )
    </select>
</mapper>