package com.siteweb.eventnotification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Getter
@AllArgsConstructor
public enum TtsConfigEnum {
    ALARM_NOTIFICATION_STATUS("notificationhost.alarm.status", "触发告警通知的告警状态（1为告警开始，2为告警结束）"),
    NOTIFICATION_HOST_ALARM_SEVERITY_SYSTEM_CONFIG_KEY("notificationhost.alarm.severity", "触发告警通知的告警等级(多个告警等级之间以半角逗号隔开)"),
    TTS_FIRST_PUSH_ALARM_ENABLE("tts.firstpushalarm.enable", "TTS不播报登录时的活动告警"),
    ALARM_TTS_ENABLE("alarmtts.enable", "是否启用网页版TTS语音告警通知"),
    TTS_SPEAK_REPEAT_TIMES("tts.speakRepeatTimes", "TTS语音消息重复播报次数"),
    TTS_CIRCULAR_BROADCAST_ENABLE("tts.circularbroadcast.enable", "TTS是否循环播报活动告警"),
    TTS_CONFIRM_NO_BROADCAST_ENABLE("tts.confirmnobroadcast.enable", "TTS是否确认不播报告警"),
    TTS_MESSAGE_START_ALARM_CONTENT_TEMPLATE("tts.message.contentTemplate", "TTS语音通知告警消息内容模板"),
    TTS_MESSAGE_END_ALARM_CONTENT_TEMPLATE("tts.message.endAlarmContentTemplate", "TTS语音通知告警结束消息内容模板"),
    TTS_FILTER_POSITION("tts.filter.position", "触发tts语音的层级id（多个层级之间以半角逗号隔开）"),
    TTS_FILTER_BASE_TYPE("tts.filter.baseType", "触发tts语音的告警基类id（多个告警基类之间以半角逗号隔开）"),
    TTS_FILTER_BASE_EQUIPMENT("tts.filter.baseEquipmentId", "触发tts语音的设备基类id（多个设备基类之间以半角逗号隔开）"),
    TTS_FILTER_EVENT("tts.filter.event", "触发tts语音的告警id（多个告警之间以半角逗号隔开）"),
    TTS_FILTER_EQUIPMENT("tts.filter.equipment", "触发tts语音的设备id（多个设备之间以半角逗号隔开）"),
    TTS_END_NO_BROADCAST_ENABLE("tts.endnobroadcast.enable", "TTS是否结束不播报告警"),
    TTS_PROJECT_STATUS_ENABLE("tts.projectstatus.enable", "工程状态"),
    TTS_SPEAK_RATE("tts.speakRate", "TTS语音通知播报语速"),
    TTS_SPEAK_VOLUME("tts.speakVolume", "TTS语音通知播报音量"),
    TTS_FILTER_KEYWORD("tts.filter.keyWord", "TTS语音通知播报关键字过滤"),
    TTS_SORTBY_EVENTLEVEL("tts.sortby.eventlevel", "TTS是否按照告警等级优先播报");

    private final String ttsConfigKey;
    private final String description;
    private static final Set<TtsConfigEnum> filterConditionKeys = Set.of(ALARM_NOTIFICATION_STATUS,
            NOTIFICATION_HOST_ALARM_SEVERITY_SYSTEM_CONFIG_KEY, TTS_FILTER_POSITION, TTS_FILTER_BASE_TYPE,
            TTS_FILTER_BASE_EQUIPMENT, TTS_FILTER_EVENT, TTS_FILTER_EQUIPMENT,TTS_PROJECT_STATUS_ENABLE,
            TTS_FILTER_KEYWORD);

    public static List<String> getFilterConditionKeys() {
        return Arrays.stream(values())
                     .filter(filterConditionKeys::contains)
                     .map(TtsConfigEnum::getTtsConfigKey)
                     .toList();
    }
}
