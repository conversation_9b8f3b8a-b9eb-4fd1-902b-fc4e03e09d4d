<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-122" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(1, 'menuPermission.enable', 'true', 1, '是否启用菜单显示权限验证');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(2, 'max.concurrentLogin.user.count', '0', 1, '最大用户并发数（修改后需重启才生效）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(3, 'password.effective.days', '90', 7, '密码有效时间（天）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(4, 'password.expiring.warn.days', '7', 7, '密码修改提醒天数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(5, 'password.expiring.warn.enable', 'true', 7, '是否启用前端密码修改提醒和后端密码有效期验证');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(6, 'password.history.save.counts', '5', 7, '单用户历史密码保存次数，为0表示不保存历史密码');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(7, 'themeName.global.default', 'blue', 1, '全局默认主题名称（可选值：blue、classic、sky），如果用户主题有设置，则以用户主题为准');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(8, 'batchAlarmWindow.alarm.severity', '1,2,3,4', 9, '批量未确认告警弹出框中的告警等级（多个告警等级之间以半角逗号隔开）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(9, 'system.scene.type', '1', 1, '1-IDC,2-电信');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(11, 'sms.message.batch.apiURL', 'http://127.0.0.1:12300/api/notify/smssend', 21, '短信网关服务发送批量短信URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(12, 'alarmbox.message.apiURL', 'http://127.0.0.1:12300/api/notifyalarmbox/alarmsend', 22, '通知网关服务发送告警箱通知URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(13, 'phoneSms.message.apiURL', 'http://127.0.0.1:12300/api/notify/phonesend', 21, '通知网关服务发送电话语音(短信)通知URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(14, 'cas.user.defaultOrganizationIdAndRoleId', '', 26, 'CAS用户默认部门ID及角色ID（以半角逗号隔开）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(15, 'ad.authenticate.enable', 'false', 26, '是否启用微软AD域认证');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(16, 'ldap.user.defaultDepartmentIdAndRoleId', '', 26, 'AD域用户默认部门ID及角色ID（以半角逗号隔开）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(21, 'global.search.device.alarm', 'true', 1, '全局搜索告警设备');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(22, 'equimentControlShow', 'true', 12, '单设备组态控制列表是否要显示');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(23, 'batchAlarmWindow.show', 'false', 9, '是否显示批量未确认告警弹出框');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(24, 'alarmstatistics.show', 'true', 9, '是否显示全局告警统计条');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(25, 'realtimecapacity.refresh.time', '30', 14, '实时容量页面刷新间隔');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(31, 'notificationhost.alarm.severity', '', 8, '触发告警通知的告警等级（多个告警等级之间以半角逗号隔开）');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(32, 'notificationhost.alarm.status', '', 8, '触发告警通知的告警状态（1为告警开始，2为告警结束）');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(33, 'tts.message.contentTemplate', '', 10, 'TTS语音通知告警消息内容模板');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(34, 'tts.message.endAlarmContentTemplate', '', 10, 'TTS语音通知告警结束消息内容模板');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(35, 'tts.firstpushalarm.enable', 'true', 10, 'TTS不播报登录时的活动告警');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(36, 'alarmtts.enable', 'false', 10, '是否启用网页版TTS语音告警通知');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(37, 'tts.speakRepeatTimes', '1', 10, 'TTS语音单条告警消息播报次数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(38, 'battery.string.environmenthumidity', '{"1101":"1101003001","1103":"","503":""}', 11, '蓄电池组环境湿度');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(39, 'battery.string.ambienttemperature', '{"1101":"1101001001","1103":"","503":""}', 11, '蓄电池组环境温度');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(40, 'battery.string.totalvoltage', '{"1101":"1101170001","1103":"1103172001","503":"503172001"}', 11, '蓄电池组总电压');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(41, 'battery.string.totalcurrent', '{"1101":"1101174001","1103":"1103174001","503":"503174001"}', 11, '蓄电池组总电流');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(42, 'battery.cell.internalresistance', '{"1101":"1101310","1103":"1103310","503":"503310"}', 11, '%02d#单体内阻');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(43, 'battery.cell.temperature', '{"1101":"1101193","1103":"","503":"503181"}', 11, '%02d#单体温度');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(44, 'battery.cell.temperaturelow', '{"1101":"1101320","1103":"1103333","503":"503343"}', 11, '%02d#单体温度过低');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(45, 'battery.cell.temperaturehigh', '{"1101":"1101193","1103":"1103332","503":"503193"}', 11, '%02d#单体温度过高');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(46, 'battery.cell.voltage.12V', '{"1101":"1101191","1103":"1103191","503":"503191"}', 11, '%02d#单体12V电压');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(47, 'battery.cell.voltagelow.12V', '{"1101":"1101191","1103":"1103191","503":"503191"}', 11, '%02d#单体12V电压过低');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(48, 'battery.cell.voltagehigh.12V', '{"1101":"1101352","1103":"1103355","503":"503352"}', 11, '%02d#单体12V电压过高');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(49, 'battery.cell.voltage.6V', '{"1101":"1101309","1103":"1103309","503":"503309"}', 11, '%02d#单体6V电压');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(50, 'battery.cell.voltagelow.6V', '{"1101":"1101354","1103":"1103354","503":"503354"}', 11, '%02d#单体6V电压过低');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(51, 'battery.cell.voltagehigh.6V', '{"1101":"1101353","1103":"1103353","503":"503353"}', 11, '%02d#单体6V电压过高');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(52, 'battery.cell.voltage.2V', '{"1101":"1101192","1103":"1103192","503":"503179"}', 11, '%02d#单体2V电压');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(53, 'battery.cell.voltagelow.2V', '{"1101":"1101192","1103":"1103192","503":"503382"}', 11, '%02d#单体2V电压过低');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(54, 'battery.cell.voltagehigh.2V', '{"1101":"1101355","1103":"1103352","503":"503381"}', 11, '%02d#单体2V电压过高');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(55, 'battery.string.dischargepower', '{"1101":"1101318001","1103":"1103318001","503":"503400001"}', 11, '蓄电池组放电功率');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(56, 'battery.string.totalsoc', '{"1101":"1101176001","1103":"","503":"503176001"}', 11, '蓄电池组总SOC');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(57, 'battery.cell.soc', '{"1101":"1101315","1103":"","503":""}', 11, '%02d#单体SOC');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(58, 'battery.dischargeevent.basetypeid', '{"1101":"1101197001","1103":"","503":""}', 11, '蓄电池放电告警');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(59, 'battery.dischargeevent.fixedDelay', '6', 11, '电池放电周期存储周期(5的倍数)默认：5s × 6 = 30s');
--             insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(60, 'battery.dischargeevent.signalbasetypeid', '1101197001', 11, '电池放电信号基类');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(61, 'navigate.to.battery', 'true', 11, 'true表示跳转到电池放电曲线界面，false则不是');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(62, 'battery.dischargeeven.max.duration', '60', 11, '蓄电池最大放电持续时长（分钟）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(63, 'battery.workstatus.signalbasetypeid', '', 11, '蓄电池工作状态信号基类id');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(70, 'battery.relevance.config', '0', 11, '电池总览柱状图颜色关联设置： 0：关闭关联告警显示效果 1：有告警时显示为红色 2：有告警时关联告警等级显示');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(71, 'bigscreen.roomuindexrate.top', '10', 13, '机房u位使用率topN');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(72, 'bigscreen.rackuindexrate.top', '10', 13, '机架u位使用率topN');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(73, 'bigscreen.rackchangerecord.top', '100', 13, '近期机架变更记录topN');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(74, 'spring.mail.host', 'smtp.test.com', 3, '邮件服务ip');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(75, 'spring.mail.port', '465', 3, '邮件服务端口');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(76, 'spring.mail.username', '<EMAIL>', 3, '默认系统邮箱地址');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(77, 'spring.mail.password', '123456789', 3, '默认系统邮箱密码');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(78, 'spring.mail.properties.mail.smtp.auth', 'true', 3, '是否使用SMTP认证');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(79, 'spring.mail.protocol', 'smtp', 3, '邮件发送协议');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(80, 'spring.mail.defaultEncoding', 'UTF-8', 3, '邮件编码方式');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(81, 'report.timeJob.export.mode', '1', 15, '定时报表导出模式，1：横向，0-纵向');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(82, 'report.excel.export.cell.width.setting', 'false', 15, '报表导出excel单元格是否使用前端的customColumnWidths参数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(83, 'report.excel.export.cell.height.setting', 'false', 15, '报表导出excel单元格是否使用前端传来的customColumnHeights参数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(84, 'report.chart.anchor', 'false', 15, '图例的范围描点');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(85, 'logo.img', ' ', 27, 'logo.png');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(86, 'logo.text', 'SiteWeb6', 27, 'IDC数据中心');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(87, 'website.tab.text', 'SiteWeb基础设施管理系统 V6.0', 27, NULL);
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(88, 'website.tab.icon', '', 27, NULL);
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(89, 'theme', 'default', 27, '默认主题');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(90, 'complexindex.http.url', 'http://siteweb-indicators:8300/api/', 16, '指标服务url');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(91, 'complexindex.syn.enable', 'true', 16, '指标同步配置开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(92, 'batchconfig.batteryandhvc.enable', 'false', 1, '系统开关，是否放开电池和高压直流类型的批量动态配置');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(93, 'connecterror.soundpop.enable', 'true', 1, '系统开关，出现连接错误后，是否发声和弹窗');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(94, 'system.language.type', 'zh_CN', 1, '系统语言类型 en_US:英文，zh_CN:中文');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(95, 'max.try.login.count', '3', 7, '登录最大尝试次数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(96, 'login.freeze.time', '600', 7, '登录冻结时间(秒)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(97, 'batchtool.statesignal.enable', 'false', 17, 'BA拆分工具是否展示设备通讯状态信号');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(98, 'map.online', 'false', 18, '电子地图的在线和离线模式');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(99, 's2.alarm.showAdvice', 'true', 18, '电信场景事件浏览页面的专家建议开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(100, 'report.auditreport.enable', 'true', 15, '是否开启审计报表');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(101, 'report.auditreport.level', '1,2,3,4', 15, '审计报表审计级别 1最小级、2基本级、3详细级、4未规定');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(102, 'report.auditreport.maxcount', '10000', 15, '审计报表最大记录数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(103, 'report.securityreport.enable', 'true', 15, '是否开启安全日志报表');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(104, 'report.securityreport.maxcount', '10000', 15, '安全日志报表最大记录数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(105, 'account.login.timeSpan.enable', 'false', 23, '是否开启登录账号访问时间段功能');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(106, 'login.ip.filterpolicy.enable', 'false', 23, '是否开启登录IP过滤策略');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(108, 'zoom.enable', 'true', 12, '是否开启系统缩放功能');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(109, 'loading.bar.show', 'false', 1, '请求进度条的显示开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(110, 'user.noaction.logout', 'false', 1, '用户不操作是否退出');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(111, 'user.noactionlogout.span', '5', 1, '用户不操作退出时长(单位分钟)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(112, 'cas.login.enable', 'false', 26, '是否启用CAS登录认证');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(113, 'cas.login.redirect.url', '', 26, 'CAS服务器登录后跳转的URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(114, 'cas.logout.redirect.url', '', 26, 'CAS服务器登出后跳转的URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(115, 'menu.collapsible', 'true', 1, '左侧菜单可收缩');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(116, 'system.tag', 'siteweb6', 1, '系统标识：s2-网点场景 siteweb6-其他场景');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(117, 'configure.breadcrumb.state', 'false', 12, '组态导航栏开关状态');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(118, 'ubit.powerelectricity.show', 'false', 13, '机架U位使用率是否显示额定功率和用电量列');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(119, 'important.event.condition', '10:停电,37:水浸,34:高温,47:电池电压低', 18, '电信版停电、水浸、高温、电池电压低事件类型id');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(120, 'user.online.expire.time', '30', 1, '用户在线失效时间（秒）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(121, 'security.file.integrity.enable', 'false', 23, '服务文件完整性自校验开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(122, 'siteweb6.appsidecar.url', 'http://localhost:8000/api/v1/', 1, '服务管理台url');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(123, 'robot.remoteControl.compId', '2062', 19, '机器人远程控制控件id');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(124, 'robot.chart.compId', '2060;2061;2059', 19, '机器人本体统计;环境统计;设备统计的compId');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(125, 'alarm.video.window.show', 'false', 20, '告警视频联动开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(126, 'alarm.refresh.span', '2', 9, '告警页面刷新间隔,单位:秒');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(127, 'realmonitor.refresh.span', '3', 1, '实时监控页面、活动告警、配置告警及信号刷新间隔,单位:秒');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(128, 'graphic.pageTemplateCategory', '2', 12, '设备组态模板的模板分类（1为类似S3风格，2为重构后的风格,3为万国设备组态标准）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(129, 'alarm.history.tab.show', 'false', 9, '是否显示历史告警tab');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(130, 'global.search.device', 'false', 1, '全局搜索设备');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(131, 'backup.id', '1', 23, '系统备份任务ID，服务器管理台的数据备份索引，从1开始');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(132, 'keycloak.user.defaultDepartmentIdAndRoleId', '', 26, 'Keycloak用户默认部门ID及角色ID（以半角逗号隔开）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(133, 'security.file.integrity.interval', '600', 23, '服务文件完整性自校验周期时间（秒）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(134, 'menu.routing.guard.enable', 'false', 1, '是否启用前端菜单路由守卫功能');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(135, 'report.securityreport.sms.enable', 'false', 15, '安全报表是否开启短信发送');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(136, 'comp.devicename.length', '10', 12, '组态设备名的显示长度');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(137, 'report.column.name.format', 'equipmentName-signalName', 15, '报表列名展示：equipmentName:设备名称，signalName:信号名称');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(138, 'security.gateway.heartbeat.url', '', 23, '安全认证通知网关心跳接口url');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(139, 'security.gateway.heartbeat.interval', '0', 23, '安全认证通知网关心跳接口轮询周期（秒）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(140, 'security.violence.login.duration', '300', 23, '安全认证：暴力破解登录持续时间（秒）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(141, 'alarm.video.window.keepAlive', 'false', 20, '告警联动视频弹窗是否保活');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(142, 'report.history.signal.limit', '20000', 15, '报表历史信号限制条数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(143, 'alarm.pagelock.span', '30', 9, '告警锁定页面时默认时长(秒)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(144, 'departmentPermission.enable', 'false', 1, '是否启用部门权限验证');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(145, 'end.alarm.pop.trigger.cron', '', 1, '未结束告警定时弹窗触发时间');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(146, 'notification.getui.allowEquipmentCategory', '11,12,13,17,18,20', 25, '个推-允许推送的设备分类条件');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(147, 'notification.getui.allowEventCategory', '1,2,3,4,5,6,7,8,9,10', 25, '个推-允许推送的事件分类条件');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(148, 'alarmNotification.mobile.getui.enable', 'false', 25, '个推-告警通知推送');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(149, 'tts.circularbroadcast.enable', 'false', 10, 'TTS是否循环播报活动告警');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(150, 'tts.confirmnobroadcast.enable', 'false', 10, 'TTS是否已确认告警不播报');
            -- insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(151, 'baControlCommandWebsocketPushEnable', 'false', 1, 'ba控制命令websocket推送');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(152, 'report.generalCustomParser.activeSignal.ignoreUnit', 'false', 1, '通用定制报表-实时数据是否忽略单位');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(153, 'fiveMinutes.storage.database', 'false', 15, '是否默认使用五分钟存储库查询历史信号数据');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(154, 'system.isSC', '0', 1, '是否为二级模拟三级的模式');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(155, 'report.chart.type', '', 1, '历史数据报表和历史指标报表导出图例，空默认为原来的echart类型，1代表使用echart,2代表使用图例');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(156, 'weCom.apply.apiURL', 'http://127.0.0.1:12300/api/notify/wechatsend', 22, '通知网关企业微信应用通知URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(157, 'dcom.http.url', '', 1, '工作流接口URL，示例：http://*************:3001/api');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(158, 'alarmlight.message.apiURL', 'http://127.0.0.1:12300/api/notifyalarmbox/alarmsend', 22, '通知网关服务发送告警灯通知URL');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(159, 'libattery.safealarm.basetypeids', '', 1, '锂电池安全告警基类ID（多个基类ID之间以半角逗号隔开）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(160, 'libattery.thermal.runaway.temperatureIncrementRateThreshold', '4', 1, '锂电池单体热失控温升速率阈值（单位为摄氏度/每分钟）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(161, 'libattery.thermal.runaway.temperatureDeviationThreshold', '', 1, '锂电池单体热失控同组单体温差阈值（单位为摄氏度）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(162, 'libattery.thermal.runaway.voltageDeviationThreshold', '', 1, '锂电池单体热失控同组单体压差阈值（单位为伏特）');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(163, 'system.token.header', 'Authorization', 1, '系统自定义token的请求头(修改后需重启才生效)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(164, 'report.history.signal.maxcount', '2000', 1, '查询历史信号报表的最大的信号个数');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(165, 'computerrack.airspace.height', '0', 13, '机架管理，换气U位的高度。');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(166, 'alarmlist.static.showName', 'false', 9, '告警管理页面统计条是否显示等级名称');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(167, 'account.terminalDevice.bind.enable','false',23,'是否开启账号终端绑定功能');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(168, 'app.video.type','1',1,'APP视频类型：1-S6V2视频平台');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(169, 's2.graphicPage.standardAlarmName','false',9,'电信场景组态页中的告警相关接口是否只统计标准化告警');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(170, 'device.group.alarm.color', 'false', 1, '设置单独红色和不同的告警颜色');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(171, 'alarm.video.window.type', 1, 20, '视频平台配置项：1-PC客户端,2-SiteWebVideo平台,3-视频平台2.0');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(172, 'login.token.expiration', '', 1, '登录的token过期时间(小时)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(173, 'login.inactive.lock', '', 1, '不活跃账户锁定(小时)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(174, 'ali.cycle.storage.interval.seconds', '10', 1, '定制阿里告警周期存储间隔时间，定义每隔多少秒进行一次存储');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(175, 'ali.cycle.storage.max.period', '1800', 1, '定制阿里告警没有结束数据存储的最长时间，单位秒');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(176, 'ali.cycle.storage.enable', 'false', 1, '定制阿里告警历史存储开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(177, 'tts.http.span', '10000', 1, '定制阿里告警历史存储开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(178, 'spring.mail.properties.mail.smtp.ssl.enable', 'true', 3, '是否启用邮件SSL加密(服务器要求则配置为true)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(179, 'spring.mail.properties.mail.smtp.starttls.enable', 'true', 3, '是否启用邮件STARTTLS加密(服务器要求则配置为true)');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(180, 'external.api.limit', '50000', 28, '外部接口调用限制');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(181, 'battery.equipmentbasetype.config', '1101,1103,503', 11, '电池设备基类类型配置，定于支持的电池类型 如1101,1103,503');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(182, 'preAlarm.notify.enable', 'false', 1, '预警语音通知开关');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(183, 'prealarm.notify.confirmednotify', 'false', 1, '登录时预警语音通知确认预警是否通知');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(184, 'report.exportTips.enable', 'true', 15, '是否在查询时提示输出参数没有配置');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(185, 'app.menuPermission.enable', 'true', 1, '是否启用APP菜单（模块入口）权限验证');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(186, 'utag.bindpercent.hide', 'false', 13, '标签绑定率是否隐藏');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(187, 'tts.queue.show', 'true', 1, '是否显示TTS语音播报队列');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(188, 'spring.mail.subject', '告警通知', 3, '告警通知邮件主题');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(189, 'ali.signal.basetypeid.enable', 'true', 1, '定制阿里是否存储带基类的信号');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(190, 'alarms.end.ignore', 'false', 9, '全局统计条不统计结束告警');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(191, 'report.generalCustomParser.headerTimeFormat.month', 'MM月', 1, '通用定制报表-表头月时间格式');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(192, 'report.generalCustomParser.headerTimeFormat.day', 'MMdd', 1, '通用定制报表-表头日时间格式');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(193, 'lark.apply.apiURL', 'http://127.0.0.1:12300/api/notify/feishusend', 22, '通知网关飞书应用通知URL');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(194, 'global.statistics.alarm.show', 'true', 9, '是否显示全局告警统计');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(195, 'global.statistics.alarm.severity', '1,2,3,4', 9, '告警统计显示的告警等级');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(196, 'deploy.s2.enable', 'false', 9, '是否部署了s2后台');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(197, 'versionManager.fsuBackup.enable', 'false', 1, '是否启用备份fsu的home目录功能');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(198, 'patrol.timerTaskExpiredDaysEx', '31', 1, '清除自动巡检-异常记录的时间 范围在1-62');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(199, 'patrol.timerTaskExpiredDaysAll', '365', 1, '清除自动巡检-全量记录的时间 范围在1-365');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(200, 'bytedance.capacityscreen.top', '5', 14, '字节容量管理展板topN');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(201, 'udevice.restoreDelay.second', '20', 13, '查找it设备还原U位设备指示灯时间(秒)');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(202, 'forget.password.enable', 'false', 7, '是否启用忘记密码');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(203, 'alarmstatistics.s2Alarm.needsound', 'false', 1, '是否启用s2全局告警发声');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(204, 'global.alarm.template.broadcast', 'false', 1, 's2告警发声是否根据选中模板(默认根据默认模板)');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(205, 'graphic.page.edit.users.enable', 'false', 12, '组态页面是否开启在线编辑人数');
            INSERT INTO "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(206, 'zoom.scale.threshold', '0.005', 12, '防止自动跳转组态页面大小导致屏幕闪动，低于此精度就不调整');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(207, 'comtrade.signal.basetypeids', '', 1, 'COMTRADE信号基类ID，多个的话逗号分隔');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(208, 'comtrade.equipment.basetypeids', '', 1, 'COMTRADE设备基类ID，多个的话逗号分隔');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(209, 'comtrade.enable', 'false', 1, '是否启用COMTRADE功能');
            insert into "systemconfig"("SystemConfigId", "SystemConfigKey", "SystemConfigValue", "SystemConfigType", "Description") VALUES(210, 'assetDevice.extField.equipment.LinkageModifyAssetInfo.enable', 'true', 1, '资产扩展字段设备类型是否联动修改资产信息');
        </sql>
    </changeSet>
</databaseChangeLog>