package com.siteweb.report.dto;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

@Data
@FieldNameConstants
public class AllEventDTO {
    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 层级ID
     */
    private Integer resourceStructureId;
    /**
     * 设备位置
     */
    private String stationName;
    /**
     * 设备基类名称
     */
    private String equipmentBaseName;
    /**
     * 设备种类名
     */
    private String equipmentCategoryName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 告警触发值
     */
    private Double eventValue;
    /**
     * 告警含义
     */
    private String meanings;
    /**
     * 翻转次数
     */
    private Integer reversalNum;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 确认人名
     */
    private String confirmerName;
    /**
     * 持续时间
     */
    private String duration;
    /**
     * 注释
     */
    private String description;
}
