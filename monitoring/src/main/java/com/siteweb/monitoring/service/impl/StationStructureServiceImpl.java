package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.entity.StationStructure;
import com.siteweb.monitoring.mapper.StationStructureMapper;
import com.siteweb.monitoring.service.StationStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StationStructureServiceImpl implements StationStructureService {
    @Autowired
    StationStructureMapper stationStructureMapper;
    @Override
    public List<StationStructure> findAll() {
        return stationStructureMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public StationStructure findById(Integer id) {
        return stationStructureMapper.selectById(id);
    }

    @Override
    public StationStructure findByStationId(Integer stationId){
        return stationStructureMapper.findByStationId(stationId);
    }
    @Override
    public Map<Integer, String> findStationStructureMap(List<Integer> stationIds){
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptyMap();
        }
        List<IdValueDTO<Integer, String>> idValueList = stationStructureMapper.findStationStructureIdLabel(stationIds);
        return idValueList.stream()
                          .collect(Collectors.toMap(IdValueDTO::getValue, IdValueDTO::getLabel));
    }

    @Override
    public StationStructure findRoot() {
        return stationStructureMapper.selectOne(Wrappers.lambdaQuery(StationStructure.class)
                                                        .eq(StationStructure::getParentStructureId, 0));
    }

    @Override
    public StationStructure findPostalStructure() {
        return stationStructureMapper.selectOne(Wrappers.lambdaQuery(StationStructure.class)
                                                        .eq(StationStructure::getStructureGroupId, 0)
                                                        .eq(StationStructure::getParentStructureId, 0)
                                                        .eq(StationStructure::getEnable, 1));
    }

    @Override
    public StationStructure findPostalStructureByType() {
        return stationStructureMapper.selectOne(Wrappers.lambdaQuery(StationStructure.class)
                .eq(StationStructure::getStructureType, 2)
                .eq(StationStructure::getParentStructureId, 0));
    }

    @Override
    public List<StationStructure> findByStructureGroupId(Integer structureGroupId) {
        if (Objects.isNull(structureGroupId)) {
            return findAll();
        }
        return stationStructureMapper.selectList(Wrappers.lambdaQuery(StationStructure.class).eq(StationStructure::getStructureGroupId, structureGroupId));
    }
}
