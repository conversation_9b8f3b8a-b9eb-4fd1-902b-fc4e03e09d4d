package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.dto.FolderWithReportsDTO;
import com.siteweb.report.dto.ReportFolderTreeDTO;
import com.siteweb.report.entity.ReportFolderMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportFolderMapMapper extends BaseMapper<ReportFolderMap> {

    /**
     * 获取报表文件夹下挂载的报表
     *
     * @param folderIds 文件夹id
     */
    List<FolderWithReportsDTO> selectReportMappings(@Param("folderIds") List<Integer> folderIds);

    /**
     * 获取未挂载报表文件夹下的报表
     */
    List<ReportFolderTreeDTO> selectUnassignedReports();
}
