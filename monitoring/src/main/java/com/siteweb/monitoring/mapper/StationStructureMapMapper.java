package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.entity.StationStructureMap;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StationStructureMapMapper extends BaseMapper<StationStructureMap> {
    List<IdValueDTO<Integer, String>> findStationStructureNameMap();
}
