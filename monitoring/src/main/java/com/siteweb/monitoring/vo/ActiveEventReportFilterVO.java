package com.siteweb.monitoring.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 活动告警报表过滤vo
 *
 * @Author: lzy
 * @Date: 2022/5/14 15:52
 */
@Data
public class ActiveEventReportFilterVO {
    /**
     * 设备id
     */
    private String equipmentIds;
    /**
     * 设备基类ID
     */
    private String baseEquipmentId;
    /**
     * 设备类型
     */
    private String equipmentCategories;
    /**
     * 告警等级id
     */
    private String eventSeverityIds;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 确认人id
     */
    private List<Integer> confirmerIds;
    /**
     * 事件过滤符合条件【设备id.事件id】多个用|分隔
     */
    private String eventIds;
    /**
     * 告警注释
     */
    private String description;
}
