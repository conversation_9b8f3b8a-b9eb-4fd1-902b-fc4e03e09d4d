<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.FsuMapper">
    <sql id="findFsuSqlcondition">
        <where>
            <if test="filterVo.notMuCategoryList != null and filterVo.notMuCategoryList.size > 0">
                and m.MonitorUnitCategory NOT IN
                <foreach collection="filterVo.notMuCategoryList" open="(" close=")" separator="," item="notMuCategory">
                    #{notMuCategory}
                </foreach>
            </if>
            <if test="stationIdList != null and stationIdList.size > 0">
                and m.StationId in
                <foreach collection="stationIdList" open="(" close=")" separator="," item="stationId">
                    #{stationId}
                </foreach>
            </if>
            <if test="filterVo.keywords != null and filterVo.keywords != ''">
                AND (t.SiteName LIKE CONCAT('%', #{filterVo.keywords}, '%')
                OR t.FsuType like CONCAT('%', #{filterVo.keywords},'%')
                OR t.Hw like CONCAT('%', #{filterVo.keywords},'%')
                OR t.SN like CONCAT('%', #{filterVo.keywords},'%')
                OR t.Mac like CONCAT('%', #{filterVo.keywords},'%')
                OR t.Ip like CONCAT('%', #{filterVo.keywords},'%')
                OR t.MemTotal like CONCAT('%', #{filterVo.keywords},'%')
                OR t.FlashSize like CONCAT('%', #{filterVo.keywords},'%')
                OR t.Linux like CONCAT('%', #{filterVo.keywords},'%')
                OR t.SiteVersion like CONCAT('%', #{filterVo.keywords},'%')
                OR t.CpuUsage like CONCAT('%', #{filterVo.keywords},'%')
                OR t.MemUsage like CONCAT('%', #{filterVo.keywords},'%')
                OR t.FlashUsedRate like CONCAT('%', #{filterVo.keywords},'%')
                OR t.CollectTime like CONCAT('%', #{filterVo.keywords},'%'))
            </if>
            <if test="filterVo.siteName != null and filterVo.siteName != ''">
                AND t.SiteName LIKE CONCAT('%', #{filterVo.siteName}, '%')
            </if>
            <if test="filterVo.fsuType != null and filterVo.fsuType != ''">
                AND t.FsuType like CONCAT('%', #{filterVo.fsuType},'%')
            </if>
            <if test="filterVo.hw != null and filterVo.hw != ''">
                AND t.Hw like CONCAT('%', #{filterVo.hw},'%')
            </if>
            <if test="filterVo.sn != null and filterVo.sn != ''">
                AND t.SN like CONCAT('%', #{filterVo.sn},'%')
            </if>
            <if test="filterVo.mac != null and filterVo.mac != ''">
                AND t.Mac like CONCAT('%', #{filterVo.mac},'%')
            </if>
            <if test="filterVo.ip != null and filterVo.ip != ''">
                AND t.Ip like CONCAT('%', #{filterVo.ip},'%')
            </if>
            <if test="filterVo.linux != null and filterVo.linux != ''">
                AND t.Linux like CONCAT('%', #{filterVo.linux},'%')
            </if>
            <if test="filterVo.siteVersion != null and filterVo.siteVersion != ''">
                AND t.SiteVersion like CONCAT('%', #{filterVo.siteVersion},'%')
            </if>
            <if test="filterVo.cpuUsage != null and filterVo.cpuUsage != ''">
                AND t.CpuUsage like CONCAT('%', #{filterVo.cpuUsage},'%')
            </if>
            <if test="filterVo.memUsage != null and filterVo.memUsage != ''">
                AND t.MemUsage like CONCAT('%', #{filterVo.memUsage},'%')
            </if>
            <if test="filterVo.flashUsedRate != null and filterVo.flashUsedRate != ''">
                AND t.flashUsedRate like CONCAT('%', #{filterVo.flashUsedRate},'%')
            </if>
        </where>
        order by
        <choose>
            <when test="filterVo.field != null and filterVo.field != ''">
                ${filterVo.field}
                <if test="filterVo.order != null and filterVo.order != ''">
                    ${filterVo.order}
                </if>
            </when>
            <otherwise>
                t.CollectTime desc
            </otherwise>
        </choose>
    </sql>
    <select id="findFsuTypeList" resultType="java.util.Map">
        SELECT ItemValue FsuType FROM TBL_DataItem WHERE EntryId = 34 AND ItemId NOT IN (0,1,3)
    </select>
    <select id="findAllFsu" resultType="com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO">
        SELECT DISTINCT t.SiteName, t.FsuType, t.Hw, t.SN, t.Mac, t.Ip, t.MemTotal, t.FlashSize, t.Linux, t.SiteVersion, t.CpuUsage,
        t.MemUsage, t.FlashUsedRate, t.CollectTime
        FROM (
        SELECT aa.SiteName, aa.FsuType, aa.Hw, aa.SN, aa.Mac, aa.Ip,
        TO_CHAR(aa.MemTotal / 1024, '999999999999999.9') || 'M' AS MemTotal,
        aa.FlashSize, aa.Linux, aa.SiteVersion,
        ROUND(aa.CpuUsage, 0) AS CpuUsage,
        ROUND(aa.MemUsage, 0) AS MemUsage,
        TO_NUMBER(REPLACE(aa.FlashUsedRate, SUBSTR(aa.FlashUsedRate, -1), '')) AS FlashUsedRate,
        aa.CollectTime
        FROM TBL_IcsFsuDataNewInfo aa
        ) t
        INNER JOIN TSL_MonitorUnit m ON t.Ip = m.IpAddress
        <include refid="findFsuSqlcondition"/>
    </select>
    <select id="findFsuTypeStatistics" resultType="com.siteweb.utility.dto.IdValueDTO">
        SELECT FsuType AS label, COUNT(FsuType) AS value
        FROM (SELECT CASE
        WHEN FsuType = '' THEN 'unknown'
        ELSE
        CASE
        WHEN LOCATE('-', FsuType) = 0 THEN FsuType
        ELSE SUBSTRING(FsuType, 1, LOCATE('-', FsuType) - 1)
        END
        END AS FsuType
        FROM TBL_IcsFsuDataNewInfo t
        INNER JOIN TSL_MonitorUnit m ON t.Ip = m.IpAddress
        INNER JOIN tbl_station s ON s.StationId = m.StationId
        WHERE s.StationId IN
        <foreach collection="stationIdSet" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ) AS FsuInfo
        GROUP BY FsuType;
    </select>
    <select id="findSiteUnitStatistics" resultType="com.siteweb.utility.dto.IdValueDTO">
        SELECT SiteVersion AS label, COUNT(SiteVersion) AS value
        FROM (SELECT CASE
        WHEN t.SiteVersion = '' THEN 'unknown'
        ELSE t.SiteVersion
        END AS SiteVersion
        FROM TBL_IcsFsuDataNewInfo t
        INNER JOIN TSL_MonitorUnit m ON t.Ip = m.IpAddress
        INNER JOIN tbl_station s ON s.StationId = m.StationId
        WHERE s.StationId IN
        <foreach collection="stationIdSet" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ) AS SiteUnit
        GROUP BY SiteVersion;
    </select>
    <select id="findFsuFlashUsedStatistics" resultType="com.siteweb.utility.dto.IdValueDTO">
        SELECT
        CASE
        WHEN FlashUsedRate &lt; 70 THEN '&lt;70%'
        WHEN FlashUsedRate &gt;= 70 AND FlashUsedRate &lt; 80 THEN '70%-80%'
        WHEN FlashUsedRate &gt;= 80 AND FlashUsedRate &lt; 90 THEN '80%-90%'
        WHEN FlashUsedRate &gt;= 90 THEN '&gt;=90%'
        END AS label,
        COUNT(*) AS value
        FROM (
        SELECT
        CAST(SUBSTR(t.FlashUsedRate, 1, LENGTH(t.FlashUsedRate) - 1) AS INTEGER) AS FlashUsedRate
        FROM TBL_IcsFsuDataNewInfo t
        INNER JOIN TSL_MonitorUnit m ON t.Ip = m.IpAddress
        INNER JOIN tbl_station s ON s.StationId = m.StationId
        WHERE s.StationId IN
        <foreach collection="stationIdSet" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ) Flash
        GROUP BY
        CASE
        WHEN FlashUsedRate &lt; 70 THEN '&lt;70%'
        WHEN FlashUsedRate &gt;= 70 AND FlashUsedRate &lt; 80 THEN '70%-80%'
        WHEN FlashUsedRate &gt;= 80 AND FlashUsedRate &lt; 90 THEN '80%-90%'
        WHEN FlashUsedRate &gt;= 90 THEN '&gt;=90%'
        END;
    </select>
    <select id="findFsuPage" resultType="com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO">
        SELECT DISTINCT m.MonitorUnitName SiteName, t.FsuType, t.Hw, t.SN, t.Mac, m.IpAddress Ip, t.MemTotal, t.FlashSize, t.Linux, t.SiteVersion, t.CpuUsage,
        t.MemUsage, t.FlashUsedRate, t.CollectTime,
        CASE WHEN t.Ip IS NOT NULL THEN 1 ELSE 0  END AS isVersion
        FROM (
        SELECT aa.SiteName, aa.FsuType, aa.Hw, aa.SN, aa.Mac, aa.Ip,
        TO_CHAR(aa.MemTotal / 1024, '999999999999999.9') || 'M' AS MemTotal,
        aa.FlashSize, aa.Linux, aa.SiteVersion,
        ROUND(aa.CpuUsage, 0) AS CpuUsage,
        ROUND(aa.MemUsage, 0) AS MemUsage,
        TO_NUMBER(REPLACE(aa.FlashUsedRate, SUBSTR(aa.FlashUsedRate, -1), '')) AS FlashUsedRate,
        aa.CollectTime
        FROM TBL_IcsFsuDataNewInfo aa
        ) t
        RIGHT JOIN TSL_MonitorUnit m ON t.Ip = m.IpAddress
        <include refid="findFsuSqlcondition"/>
    </select>
    <select id="findFsuVersionStatusStatistics" resultType="com.siteweb.utility.dto.IdValueDTO">
        SELECT
        label,
        COUNT(*) AS value
        FROM
        (
        SELECT
        CASE
        WHEN t.Ip IS NOT NULL THEN '已管理'
        ELSE '未管理'
        END AS label
        FROM
        TBL_IcsFsuDataNewInfo t
        RIGHT JOIN TSL_MonitorUnit m
        ON
        t.Ip = m.IpAddress
        WHERE m.MonitorUnitCategory NOT IN (0, 1, 3, 24)
        AND m.StationId IN
        <foreach collection="stationIdSet" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ) report_status_table
        GROUP BY label
    </select>
</mapper>