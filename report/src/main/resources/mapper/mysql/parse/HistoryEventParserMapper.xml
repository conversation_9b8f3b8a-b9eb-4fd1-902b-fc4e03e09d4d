<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.parser.mapper.HistoryEventParserMapper">
    <sql id="findHistoryEventSql">
        select
        a.eventSeverity,a.equipmentName,fuct_GetDevicePosition(a.ResourceStructureId) as equipmentPosition,a.eventName,a.eventValue,a.meanings,a.startTime,a.confirmTime,a.confirmerName,a.description,a.endTime,a.cancelUserName,
        d.BaseEquipmentName,a.EquipmentCategoryName,a.reversalNum
        from tbl_historyevent a
        left join tbl_equipmentbasetype d on a.BaseEquipmentId = d.BaseEquipmentId
        <where>
            <if test="wrapper.startTime != null">
                and a.startTime >= #{wrapper.startTime}
            </if>
            <if test="wrapper.endDate != null">
                and a.startTime &lt;= #{wrapper.endDate}
            </if>
            <if test="wrapper.baseEquipmentId != null">
                and a.BaseEquipmentId = #{wrapper.baseEquipmentId}
            </if>
            <if test="wrapper.equipmentCategories != null and wrapper.equipmentCategories.size > 0">
                and a.equipmentCategory in
                <foreach collection="wrapper.equipmentCategories" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.eventLevelList != null and wrapper.eventLevelList.size > 0">
                and a.EventLevel in
                <foreach collection="wrapper.eventLevelList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.eventName != null">
                and a.EventName like concat('%%',#{wrapper.eventName},'%%')
            </if>
            <if test="wrapper.keyword != null">
                and (
                a.EventName like concat('%%',#{wrapper.keyword},'%%') or
                a.StationName like concat('%%',#{wrapper.keyword},'%%') or
                a.EquipmentName like concat('%%',#{wrapper.keyword},'%%') or
                a.BaseTypeName like concat('%%',#{wrapper.keyword},'%%') or
                a.EquipmentCategoryName like concat('%%',#{wrapper.keyword},'%%') or
                a.EventSeverity like concat('%%',#{wrapper.keyword},'%%') or
                a.Meanings like concat('%%',#{wrapper.keyword},'%%') or
                a.Description like concat('%%',#{wrapper.keyword},'%%')
                )
            </if>
            <if test="wrapper.description != null">
                and a.description like concat('%%',#{wrapper.description},'%%')
            </if>
            <if test="wrapper.operatorIds != null and wrapper.operatorIds.size > 0">
                and a.ConfirmerId in
                <foreach collection="wrapper.operatorIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.sql != null and wrapper.sql.size > 0">
                <foreach item="item" collection="wrapper.sql">
                    ${item}
                </foreach>
            </if>
        </where>
        order by a.startTime desc
    </sql>
    <select id="findHistoryEventByPage" resultType="com.siteweb.report.dto.HistoryEventReportDTO"
            parameterType="com.siteweb.report.parser.querywrapper.HistoryEventQueryWrapper">
        <include refid="findHistoryEventSql"/>
    </select>
    <select id="findHistoryEvent" resultType="com.siteweb.report.dto.HistoryEventReportDTO" parameterType="com.siteweb.report.parser.querywrapper.HistoryEventQueryWrapper">
        <include refid="findHistoryEventSql"/>
    </select>
    <select id="findHistoryEventCount" resultType="java.lang.Long">
        SELECT COUNT(*) from (<include refid="findHistoryEventSql"/>) as a
    </select>

</mapper>