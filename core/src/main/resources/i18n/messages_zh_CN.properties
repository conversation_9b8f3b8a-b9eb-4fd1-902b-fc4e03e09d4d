common.year=年
common.month=月
common.day=日
common.hour=小时
common.minute=分
common.seconds=秒
utility.operationObjectType.insert=新增
utility.operationObjectType.update=修改
utility.operationObjectType.delete=删除
admin.employeeName=人员名称
common.graphicPage.noDefaultGraphicPage=无默认组态页
common.name=姓名
common.alias=别名
common.jobNumber=工号
common.employeeTitle=职级
common.gender=性别
common.contactOne=联系方式一
common.email=邮件
common.contactTwo=联系方式二
common.address=地址
common.contactAddress=通信地址
common.man=男
common.woman=女
common.generalStaff=普通员工
common.lead=领导
common.other=其他
common.alarm.push=告警推送
common.notFound=找不到
common.nameUnique=名称唯一

eventconvergence.convergencereason.poweroff=市电停电
eventconvergence.convergencereason.poweron=市电正常
eventconvergence.convergencereason.lacka=相电压Ua缺相 
eventconvergence.convergencereason.lackb=相电压Ub缺相 
eventconvergence.convergencereason.lackc=相电压Uc缺相 
eventconvergence.convergencereason.lackab=相电压Ua,Ub缺相 
eventconvergence.convergencereason.lackbc=相电压Ub,Uc缺相 
eventconvergence.convergencereason.lackac=相电压Ua,Uc缺相 
eventconvergence.convergencereason.unkown=原因未知 

eventNotification.activeEvent.equipmentName=设备
eventNotification.activeEvent.eventName=告警
eventNotification.activeEvent.eventSeverity=告警等级
eventNotification.activeEvent.eventValue=开始触发值
eventNotification.activeEvent.startTime=开始时间
eventNotification.activeEvent.endTime=结束时间
eventNotification.activeEvent.meanings=告警含义
eventNotification.activeEvent.reversalNum=翻转次数
eventNotification.activeEvent.fullPosition=完整位置
eventNotification.activeEvent.concisePosition=简洁位置
eventNotification.start=开始
eventNotification.end=结束
eventNotification.getui.pushResponseCode.10001=token错误/失效
eventNotification.getui.pushResponseCode.10002=appId或ip在黑名单中
eventNotification.getui.pushResponseCode.10003=每分钟鉴权频率超限
eventNotification.getui.pushResponseCode.10004=没有查询消息明细的权限
eventNotification.getui.pushResponseCode.10005=每分钟调用频率超限
eventNotification.safeMessage.safeEmail=平安邮件

login.captcha.build.error=生成验证码错误
login.captcha.code.notExist=验证码不存在
login.captcha.code.keyNotExist=验证key不存在
login.captcha.code.error=验证码错误
login.captcha.code.expiredOrNonexistent=验证码过期或不存在

# 报表公共字段
common.report.form.serialNo=序号
common.report.form.eventSeverity=告警等级
common.report.form.equipmentName=设备名称
common.report.form.equipmentState=设备状态
common.report.form.equipmentPosition=设备位置
common.report.form.regionPosition=区域位置
common.report.form.equipmentType=设备类型
common.report.form.baseEquipmentName=设备基类
common.report.form.eventName=事件名
common.report.form.eventValue=告警触发值
common.report.form.eventRemarks=告警备注
common.report.form.Comment=注释
common.report.form.startTime=开始时间
common.report.form.endTime=结束时间
common.report.form.confirmTime=确认时间
common.report.form.confirmerName=确认人
common.report.form.confirmReason=确认原因
common.report.form.duration=持续时长
common.report.form.operationTime=操作时间
common.report.form.operatorName=操作员
common.report.form.operationContent=内容
common.report.form.endPerson=结束人
common.report.form.alarmOperatorName=屏蔽操作人
common.report.form.operationType=操作类型
common.report.form.alarmStartTime=屏蔽开始时间
common.report.form.timeGroupCategory=屏蔽时间段类型
common.report.form.timeGroupChars=分时段屏蔽内容
common.report.form.alarmEndTime=屏蔽结束时间
common.report.form.reason=原因
common.report.form.roomName=房间名
common.report.form.alarmCount=告警总数
common.report.form.current=当前
common.report.form.history=历史
common.report.form.oneLevelAlarm=一级告警
common.report.form.currentOneLevelAlarm=当前一级告警
common.report.form.historyOneLevelAlarm=历史一级告警
common.report.form.twoLevelAlarm=二级告警
common.report.form.currentTwoLevelAlarm=当前二级告警
common.report.form.historyTwoLevelAlarm=历史二级告警
common.report.form.threeLevelAlarm=三级告警
common.report.form.currentThreeLevelAlarm=当前三级告警
common.report.form.historyThreeLevelAlarm=历史三级告警
common.report.form.fourLevelAlarm=四级告警
common.report.form.currentFourLevelAlarm=当前四级告警
common.report.form.historyFourLevelAlarm=历史四级告警
common.report.form.controlName=控制名称
common.report.form.controlTime=控制时间
common.report.form.responseTime=响应时间
common.report.form.state=状态
common.report.form.resourcePosition=资源位置
common.report.form.rackName=机架名称
common.report.form.ITDeviceName=IT设备名称
common.report.form.uIndex=U位
common.report.form.customer=客户
common.report.form.business=业务
common.report.form.purchaseDate=购买时间
common.report.form.changeDate=变更时间
common.report.data.max=最大值
common.report.data.min=最小值
common.report.data.avg=平均值
common.report.data.sum=累加值
common.report.data.first=首值
common.report.data.last=尾值
common.report.form.putInShelf=上架
common.report.form.takeFormShelf=下架
common.report.form.eventMeanings=事件含义
common.report.form.alarmTime=告警时间
common.report.form.sendTime=发送时间
common.report.form.receiver=接收人
common.report.form.sendContent=发送内容
common.report.form.sendType=发送方式
common.report.form.sendResult=发送结果
common.report.form.total=合计
common.field.usedUIndex=已使用U位
common.field.freeUIndex=剩余U位
common.report.csv.position=位置
common.report.csv.collectTime=采集时间
common.report.csv.equipmentName=设备名称
common.report.csv.signalName=信号名称
common.report.csv.signalValue=信号值
common.report.csv.originValue=原始值
common.report.csv.unit=单位
common.report.alarmCount=告警数量
common.report.form.equipmentCount=设备数量
common.report.form.allAlarm=全部告警
common.report.form.abnormalAlarm=异常告警
common.report.form.abnormalHistoryAlarm=异常-历史
common.report.form.abnormalActiveAlarm=异常-活动
common.report.form.constructAlarm=施工告警
common.report.form.constructHistoryAlarm=施工告警-历史
common.report.form.constructActiveAlarm=施工告警-活动
# 告警屏蔽操作类型
alarm.mask.operationType=[{"operationType":"1","desc":"新增屏蔽"},{"operationType":"2","desc":"解除屏蔽"}]
alarm.mask.timeGroupCategory=[{"timeGroupCategory":"1","desc":"全时段屏蔽"},{"timeGroupCategory":"2","desc":"分时段屏蔽"}]


# 蓄电池
battery.cell.signal=%02d#单体
battery.workstatus.discontinue=断连
battery.workstatus.float=浮充
battery.workstatus.discharge=放电
battery.workstatus.equalize=均充

energy.msg.elecfeeconfig.notcover24hours=峰平谷各时间段未覆盖全天24小时
energy.msg.elecfeeconfig.fpghasoverlap=峰平谷各时间段有重叠
energy.msg.elecfeeconfig.structurehasoverlap=以下已选层级与其已有方案存在[适用月份]重叠：
energy.msg.elecfeeconfig.appliedrangenotnull=应用范围不能为空
energy.msg.elecfeeconfig.atleastonestep=需至少保留一条阶梯定价
energy.msg.dimension.complexindexexist=该类指标已存在
energy.msg.dimension.defaultnodecannotoperate=默认节点不可在此操作
energy.msg.dimension.subtreenotexist=子树不存在
energy.msg.dimension.newzero=该类指标已存在或不需新增，实际新增0个
energy.msg.dimension.rootnodeexists=根节点已存在
energy.msg.dimension.parentnodenotexist=父节点不存在
energy.msg.dimension.parentnodegreaterthanone=父节点大于1个
energy.msg.dimension.nodenameexists=节点名已存在
energy.msg.dimension.defaultnodecannotedit=默认层级节点此处不可编辑
energy.msg.dimension.deletenodenotexists=待删除的节点不存在
energy.msg.dimension.nodedupref=节点重复引用
energy.msg.dimension.cannotaddchild=此类型节点不可添加子节点
energy.msg.dimension.computerrackcannotaddracknode=机架节点下不允许再添加机架节点
energy.msg.dimension.computerrackcannotaddstructurenode=机架节点下不允许再添加层级节点
energy.msg.dimension.refequipmentnodecannotedit=引用设备不可编辑
energy.msg.dimension.refcomputerracknodecannotedit=引用机架不可编辑
energy.msg.dimension.refitdevicenodecannotedit=引用IT设备不可编辑
energy.msg.dimension.objectNoFather=此节点没有找到对应的父节点
energy.msg.dimension.fatherValueLowChild=当前额度小于子节点总额;子节点总额为:
energy.msg.dimension.objectOverTotal=当前节点配额已超总额,剩余量为
energy.msg.dimension.InsertFailed=插入失败
energy.msg.dimension.updateFailed=更新失败

energy.common.enable=启用
energy.common.disable=禁用

energy.displaytext.carbon=碳

energy.enum.month.m1=1月
energy.enum.month.m2=2月
energy.enum.month.m3=3月
energy.enum.month.m4=4月
energy.enum.month.m5=5月
energy.enum.month.m6=6月
energy.enum.month.m7=7月
energy.enum.month.m8=8月
energy.enum.month.m9=9月
energy.enum.month.m10=10月
energy.enum.month.m11=11月
energy.enum.month.m12=12月

energy.totalanalysis.total=汇总
energy.totalanalysis.TotalCoal=总标煤


energy.totalanalysis.formatString_1=yy年MM月
energy.totalanalysis.formatString_2=M月
energy.totalanalysis.formatString_3=d日
energy.totalanalysis.formatString_4=H时

# u位标签管理
udevice.utag.valueexists=标签唯一识别码已存在
udevice.itdevice.notxists=IT设备不存在
udevice.utag.bindingrate=标签绑定率
udevice.utag.codeEmpty=标签码不能为空
# license
license.message.licenseerror=license有误,请检查license
license.message.fileEmpty=license有误,空的license文件
license.message.typeError=license有误,请查检文件格式
# 公共提示
common.msg.idIsNotEmpty=id不能为空
common.msg.disallowOperationEmptyRecord=禁止操作不存在的记录
common.msg.equipmentexists=系统中已存在同名设备，请更换名称重试

common.timeJobMsg.addJobException=新建定时任务异常
common.timeJobMsg.updateJobException=修改定时任务异常
common.timeJobMsg.removeJobException=删除定时任务异常
common.field.signal=信号
common.field.complexIndex=指标
common.field.alarmCount=告警总数
common.field.signalNotExist=信号不存在
common.field.complexIndexNotExist=指标不存在
common.field.alreadyExists=已存在
common.field.cannotEmpty=不能为空
common.field.allEquipment=所有设备
common.field.success=成功
common.field.unknownError=未知的错误
common.field.notFindDevice=没有找到IT设备
common.field.notFindRack=没有找到机架
common.field.itDeviceNotInRack=IT设备不在机架上
common.field.uIndexMoreZero=U位必须大于0
common.field.itDeviceFree=IT设备必须是闲置的
common.field.itDeviceOutRange=IT设备位置超出范围
common.field.uIndexEmpty=U位必须是空的
common.field.shiftGroupNameUnique=班组名称已存在
common.field.shiftGroupExists=用户:%s,已有所在班组
common.field.expertAdviceExists=告警基类:%s的专家建议已存在
common.field.site=站点
common.field.quantity=数量
common.field.percentage=百分比
common.field.total=合计
common.field.district=行政区
common.field.powerFailure=停电
common.field.electricityGeneration=发电
common.field.interrupt=中断
common.field.rank=排名
common.field.monitorStatus=监控状态
common.field.underMonitoring=监控中
common.field.blockedState=屏蔽态
common.field.engineeringState=工程态
projectstate.network=联网状态
#历史预警报表
prealarm.report.form.preAlarmSeverity=预警等级
prealarm.report.form.levelOfPath=资源层级
prealarm.report.form.objectName=资源名称
prealarm.report.form.preAlarmName=预警名称
prealarm.report.form.preAlarmCategory=预警分类
prealarm.report.form.triggerValue=触发值
prealarm.report.form.startTime=开始时间
prealarm.report.form.confirmTime=确认时间
prealarm.report.form.endTime=结束时间
prealarm.report.form.confirmName=确认人

userOpLog.report.loginName=登录名称
userOpLog.report.userName=用户名称
userOpLog.report.employee=职位
userOpLog.report.jobNumber=工号
userOpLog.report.operatingContent=操作内容
userOpLog.report.operatingTime=操作时间

utility.sms.code.template = [SiteWeb6]您的验证码是:%s，10分钟内有效，请勿泄漏!
account.login.timeSpan.sms.template = [SiteWeb6]用户:%s, 在非访问时间段访问系统，请注意!
login.ip.filterpolicy.sms.template = [SiteWeb6]IP:%s, 在非访问时间段访问系统，请注意!

api.stationStatus.0=离线
api.stationStatus.1=在线
api.stationStatus.2=未注册

api.stationStatus.offlineCount=离线数
api.stationStatus.onlineCount=在线数
api.stationStatus.activeEventCount=活动告警数量

audit.report.addDepartment=添加部门
audit.report.deleteDepartment=删除部门
audit.report.updateDepartment=修改部门
audit.report.addEmployee=添加员工
audit.report.deleteEmployee=删除员工
audit.report.updateEmployee=修改员工
audit.report.updatePassword=修改密码
audit.report.resetPassword=重置密码
audit.report.setUserTheme=设置用户主题
audit.report.addAccount=添加账户
audit.report.updateAccount=修改账户
audit.report.startStopAccount=启停账户
audit.report.deleteAccount=删除账户
audit.report.deleteRole=删除角色
audit.report.updateRole=修改角色
audit.report.addRole=添加角色
audit.report.addRegionPermission=添加区域权限
audit.report.updateRegionPermission=修改区域权限
audit.report.deleteRegionPermission=删除区域权限
audit.report.login=登录成功
audit.report.logout=退出成功
audit.report.confirmAlarm=确认告警
audit.report.cancelAlarm=结束告警
audit.report.alarmNote=告警备注
audit.report.confirmControlCommand=确认控制命令
audit.report.reSendControlCommand=重发控制命令
audit.report.sendControlCommand=发送控制命令
audit.report.signalSetting=信号配置
audit.report.eventSetting=事件配置
audit.report.addReport=添加报表
audit.report.deleteReport=删除报表
audit.report.updateReport=修改报表
audit.report.addVisitTimeSetting=添加访问时间配置
audit.report.updateVisitTimeSetting=修改访问时间配置
audit.report.deleteVisitTimeSetting=删除访问时间配置
audit.report.operationAccount=操作人
audit.report.auditLevel=审计级别
audit.report.clientIp=客户端ip
audit.report.content=内容
audit.report.eventResult=事件结果
audit.report.eventType=事件类型
audit.report.operationTime=操作时间
audit.report.loginSetting=更新安全配置，登录配置。
audit.report.auditSetting=更新安全配置，审计配置。
audit.report.historyPasswordCount=历史密码不重复次数：
audit.report.infinite=无限
audit.report.one=一个
audit.report.loginFreezeTime=登录冻结时间：%s(s)
audit.report.loginTryDuration=登录连接尝试时长：%s(m)
audit.report.maxConcurrentUserCount=最大并发用户数：
audit.report.userVisitTimespanSetting=用户访问时段配置：
audit.report.active=激活
audit.report.close=关闭
audit.report.ipVisitSetting=IP段访问时段配置：
audit.report.enableAuditReport=是否开启审计报表：
audit.report.open=开启
audit.report.auditReportLevel=审计报表审计级别：
audit.report.minLevel=最小级
audit.report.basicLevel=基本级
audit.report.detailLevel=详细级
audit.report.notStated=未规定
audit.report.passwordError=密码错误
audit.report.operationSuccess=操作成功
audit.report.operationFail=操作失败
audit.report.securitySettingModule=安全配置模块
audit.report.useraccountfreeze=账号已冻结
audit.report.volenthack=暴力破解登录请注意！

security.report.operationAccount=操作账户
security.report.type=类别
security.report.clientIp=客户端ip
security.report.details=描述
security.report.operationTime=操作时间

#工程预约
projectstate.invalidparam=参数设备不对
projectstate.addequipmentsate=新增设备工程预约
projectstate.uptequipmentsate=修改设备工程预约
projectstate.delequipmentsate=删除设备工程预约
projectstate.maskconflict=工程时段与屏蔽时段冲突

projectstate.addstationprojectstate=新增局站工程预约
projectstate.uptstationprojectstate=修改局站工程预约
projectstate.delstationprojectstate=删除局站工程预约
projectstate.success=工程状态设置成功
projectstate.equipment.addOperationLog=%s设置工程态时间为%s到%s
projectstate.equipment.deleteOperationLog=%s删除工程态
projectstate.text=工程状态

nets.operation.group.name=操作权限组
nets.operation.group.Description=的操作权限

security.file.integrity.log=[SiteWeb6]文件完整性自校验列表：%s 校验不成功！

airconditioncontrol.common.manageEquipName=空调管理设备
airconditioncontrol.common.manageEquipTemplateName=空调节能
airconditioncontrol.common.manageGroupName=分组
airconditioncontrol.common.reloadConfigControlName=获取配置
airconditioncontrol.common.stdAirType.common=普通空调
airconditioncontrol.common.stdAirType.special=专用空调
airconditioncontrol.common.stdAirType.common2=一拖二空调
airconditioncontrol.common.new=新增
airconditioncontrol.common.update=修改
airconditioncontrol.common.delete=删除

signature.invalid=[SiteWeb6]防重放攻击:签名无效
signature.Expired=[SiteWeb6]防重放攻击:签名已过期
notification.gateway.access.success=通知网关接入成功
notification.gateway.access.failure=通知网关接入失败



airConditionControl.view.airStateOn=开机
airConditionControl.view.airStateOff=关机
airConditionControl.view.signalValueInfoUnKnow=未知
airConditionControl.view.airStateInfoOff=开关机状态 : 关机
airConditionControl.view.airStateInfoOn=开关机状态 : 开机
airConditionControl.view.airStateInfoUnKnow=开关机状态 : 未知
airConditionControl.view.noSignalMap=该设备没有映射信号
airConditionControl.view.signalNameTemp=温度
airConditionControl.view.signalNameStateOnOrOff=开关机状态
airConditionControl.view.signalNameEnvironmentTemp=环境温度
airConditionControl.view.signalNameAirTemp=专用空调温度
airConditionControl.view.signalNameAir1Temp=空调1温度
airConditionControl.view.signalNameAir2Temp=空调2温度
airConditionControl.view.air=空调
airConditionControl.view.groupStateRunning=运行中
airConditionControl.view.groupStateStop=停止

airConditionControl.autoControl.group=分组
airConditionControl.autoControl.basicParam=基础参数
airConditionControl.autoControl.mapTemperature=温度设备
airConditionControl.autoControl.mapFan=风机设备
airConditionControl.autoControl.mapZoneStrategy=定时策略
airConditionControl.autoControl.mapZoneStrategy.scheme=策略记录
airConditionControl.autoControl.mapZoneStrategy.operation=操作序列
airConditionControl.autoControl.mapEnergyComplexIndex=能耗指标
airConditionControl.batchControl.controlResult.success=成功
airConditionControl.batchControl.controlResult.fail=失败
airConditionControl.batchControl.controlResult.timeOut=超时
airConditionControl.batchControl.controlResult.sending=执行中

airConditionControl.report.common.operator=操作人
airConditionControl.report.common.groupName=分组名称
airConditionControl.report.common.changeType=变更类型
airConditionControl.report.common.detail=变更详情
airConditionControl.report.common.insertTime=记录时间
airConditionControl.report.common.operateTime=操作时间
airConditionControl.report.common.changeTime=变更时间
airConditionControl.report.common.stationName=局站名称
airConditionControl.report.common.houseName=局房名称
airConditionControl.report.common.monitorUnitName=监控单元名称
airConditionControl.report.common.equipmentName=设备名称
airConditionControl.report.common.operateModule=操作模块

airConditionControl.report.batchControlCmdHistory.stdControlCmd=标准控制命令
airConditionControl.report.batchControlCmdHistory.controlCmd=操作命令
airConditionControl.report.batchControlCmdHistory.controlParam=参数
airConditionControl.report.batchControlCmdHistory.stdAirconType=标准空调类型
airConditionControl.report.batchControlCmdHistory.controlResult=执行结果
airConditionControl.report.batchControlCmdHistory.cmd.remoteStart=远程开机
airConditionControl.report.batchControlCmdHistory.cmd.remoteStop=远程关机
airConditionControl.report.batchControlCmdHistory.cmd.temperatureSet=温度设置
airConditionControl.report.batchControlCmdHistory.cmd.heatMode=切换制热模式
airConditionControl.report.batchControlCmdHistory.cmd.coolMode=切换制冷模式

airConditionControl.report.autoControlEquipmentChangeLog.subChangeType=变更子类型
airConditionControl.report.autoControlEquipmentChangeLog.subObjectType=子对象

# 空调设备历史运行状态报表
airConditionControl.report.equipStateForm.station=局站
airConditionControl.report.equipStateForm.monitorUnit=监控单元
airConditionControl.report.equipStateForm.virtualEquipment=群控设备
airConditionControl.report.equipStateForm.equipment=设备
airConditionControl.report.equipStateForm.state=开关状态
airConditionControl.report.equipStateForm.time=时间
airConditionControl.report.equipStateForm.stateNum=状态值

user.account.expired=[SiteWeb6]用户账号%s已过期!
user.password.expired=[SiteWeb6]用户%s密码已过期!
user.account.locked=[SiteWeb6]用户账号%s被锁定!
user.disabled=[SiteWeb6]用户%s未启用!

#工作流
workflow.schedule.exist=该排班已存在

send.type.all=全部
send.type.sms=短信
send.type.mail=邮件
send.type.App=APP通知
send.type.alarmbox=告警箱
send.type.alarmlight=告警灯
send.type.telephonevoice=电话语音(短信)
send.type.weComApply=企业微信应用通知
send.type.lark=飞书通知
send.mail.subject=告警通知
send.result.success=发送成功
send.result.fail=发送失败

asset.assetCode.exist=资产编号不能重复

# Ba控制命令报表
baControlCommand.report.equipmentId=设备id
baControlCommand.report.equipmentName=设备名称
baControlCommand.report.controlValue=控制值

# 配电
powerDistribution.invalidRequestMissingParameter=错误的请求，缺少参数
powerDistribution.noDistributionMapFound=没有找到配电图
powerDistribution.istributionMapNotConfigured=配电图未开启容量或没有配置加电的容量基类
powerDistribution.noElementFound=没有找到元素
powerDistribution.requestedObjectNotDevice=请求的对象不是一个设备对象

# 通知策略
common.field.alarmNotifyGatewayServiceUnique=告警通知网关服务url已存在
common.field.alarmNotifyGatewayServiceInUse=告警通知网关服务url正在使用，无法修改或删除

# 门禁管理
accessControl.authorization.tooMuch=门卡授权数量请小于等于2000
# IT设备
common.field.inShelfItDeviceDelFail=上架的IT设备不能删除

# 批量工具
virtualEquipment.notExist.virtualSignal=虚拟设备信号不存在
virtualEquipment.notExist.originChannel=源设备通道号不存在
virtualEquipment.notExist.virtualEquipmentStation=虚拟设备局站不存在
virtualEquipment.notExist.virtualMonitUnit=虚拟监控单元不存在
virtualEquipment.notExist.sampleUnit=虚拟采集单元不存在
virtualEquipment.notExist.virtualEquipmentHouse=虚拟设备局房不存在
virtualEquipment.notExist.virtualTemplate=虚拟模板不存在
virtualEquipment.notExist.virtualEquipmentCategory=虚拟设备类型不存在
virtualEquipment.notExist.originEquipment=源设备不存在
virtualEquipment.cannotAcross.monitUnit=虚拟设备与源设备不能跨监控单元

# 指标
complexIndex.errorCode.1=请检查信号值是否为数字或除数为零!
complexIndex.errorCode.2=请检查表达式是否正确!

# 报表分类名称
report.category.name.1=告警
report.category.name.2=历史数据
report.category.name.3=指标
report.category.name.4=日志
report.category.name.5=其他
report.category.name.6=电池
report.category.name.7=审计
report.category.name.8=定时报表

# 江苏运维平台
js.floor.MeterCount=楼层电表数量
js.power.DeviceCount=电源设备数量
js.battery.Count=电池设备数量
js.environmental.deviceCount=环境设备数量
js.powerOutage.alarmCount=停电告警
js.lowOutputVoltage.alarmCount=输出低压告警
js.totalVoltage.alarmCount=总电压告警
js.highTemperature.alarmCount=高温告警
js.totalVoltage.tooLow=电池组总电压过低告警
js.battery.failure=电池故障
js.battery.lowVoltageAlarm=该时间段产生过电池低压告警，建议检查电池性能。
js.battery.packTooLowAlarm=电池组总电压过低告警
js.batterySupply.packTooLowAlarm=该时间段产生过电源和电池低压告警，建议检查电源和电池性能是否正常。
js.batterySupply.lostProvide=电源失去供电能力
js.batterySupply.lostPartProvide=电源失去部分供电能力
js.powerSupply.normal=该时间段产生过电源告警，建议检查电源是否正常。
js.powerSupply.abnormalLoadCurrentAlarm=负载总电流异常告警
js.powerSupply.lowOutPutVoltageAlarm=直流输出电压过低告警

#网络拓扑图
workStation.netWorkTopology.cpu=CPU(%%):%s
workStation.netWorkTopology.memory=内存(M):%s
workStation.netWorkTopology.threads=线程数:%s
workStation.netWorkTopology.diskFree=磁盘剩余(GB):%s

#屏蔽
shield.centerName=中心
shield.stationName=局站名称
shield.equipmentName=设备名称
shield.userName=屏蔽人
shield.startTime=开始时间
shield.endTime=结束时间
shield.shieldTypeName=屏蔽方式
shield.description=屏蔽备注
shield.maskMethod.1=全时段屏蔽
shield.maskMethod.2=分时段屏蔽

#控制命令状态
command.status.success=控制成功
command.status.failure=控制失败
command.status.timeout=处理超时
command.status.noResponse=未返回
command.status.addressError=控制单元地址错误
command.status.parameterError=参数错误
command.status.queueTimeout=在控制队列中超时
command.status.recordingEnded=录像结束
command.status.equipmentOffline=设备离线，控制命令发送失败

#版本管理 FSU导出
version.fsu.siteName=站点名称
version.fsu.fsuType=采集器型号
version.fsu.cpuUsage=CPU使用率(%)
version.fsu.memUsage=内存使用率(%)
version.fsu.flashUsedRate=Flash使用率(%)
version.fsu.hw=硬件版本
version.fsu.sn=SN码
version.fsu.mac=MAC地址
version.fsu.ip=IP地址
version.fsu.memTotal=内存配置
version.fsu.flashSize=Flash配置
version.fsu.linux=系统版本
version.fsu.siteVersion=siteUnit版本
version.fsu.collectTime=刷新时间

version.contract.projectName=工程名称
version.contract.contractNo=合同编号
version.contract.installTime=配置日期
version.contract.houseName=机房名称
version.contract.equipmentName=设备名称
version.contract.FsuName=FSU名称
version.contract.FsuIp=FSU IP
version.contract.stationCount=站点数量
version.contract.fsuCount=FSU数量
version.contract.equipmentCount=设备数量
version.contract.primaryDate=初验日期
version.contract.endDate=终验日期
version.contract.qualityStartPoint=质保起点
version.contract.qualityPeriod=质保期(月)
version.contract.qualityTerms=质量条款
version.contract.maintenanceStartDate=维保开始日期
version.contract.maintenanceEndDate=维保结束日期
version.contract.maintenanceTerms=维保条款

#资产导入
asset.import.assettypeid.invalid=资产类型填充无效
asset.import.assetstateid.invalid=资产状态填充无效
asset.import.useddate.invalid=资产启用日期填充无效

# 自动巡检导出
patrol.alarm.centerName=监控名称
patrol.alarm.groupName=行政区划
patrol.alarm.stationName=局站名称
patrol.alarm.equipmentCategoryName=设备分类名称
patrol.alarm.equipmentId=设备ID
patrol.alarm.equipmentName=设备名称
patrol.alarm.signalName=信号名称
patrol.alarm.signalValue=信号值
patrol.alarm.recordTime=采集时间
patrol.alarm.unit=单位
patrol.alarm.byPercentageMeaning=是否按百分比
patrol.alarm.ratedValue=额定值
patrol.alarm.warningMeaning=预警等级
patrol.alarm.isPowerOffAlarm=是否停电引起
patrol.alarm.createTime=创建时间
patrol.alarm.limitMeaning=命中条件

#资产列表导出
assetdevice.assetCode=编号
assetdevice.assetName=名称
assetdevice.assetCategoryName=类型
assetdevice.brand=品牌
assetdevice.model=型号
assetdevice.capacityParameter=容量参数
assetdevice.settingPosition=安装位置
assetdevice.serialNumber=序列号
assetdevice.manufactor=厂家

complexindex.complexIndexId=指标ID
complexindex.complexIndexName=名称
complexindex.objectId=对象ID
complexindex.calcCron=计算周期
complexindex.calcType=是否差值
complexindex.saveCron=保存周期
complexindex.expression=表达式
complexindex.unit=单位
complexindex.accuracy=精度
complexindex.objectName=对象名称
complexindex.complexIndexDefinitionId=定义ID
complexindex.businessTypeId=业务类型ID
complexindex.checkExpression=检查表达式
complexindex.businessTypeName=业务类型名称

common.report.form.position=位置
common.report.form.monitorUnitName=监控单元名称
common.report.form.serialPort=串口名称
common.report.form.serialPortType=串口类型
common.report.form.DeviceProtocolLibrary=设备协议库
common.report.form.equipmentAddress=设备地址
common.report.form.serialPortParameters=串口参数
common.report.form.updateTime=更新时间

common.yesOrNo.1=是
common.yesOrNo.0=否
common.eventLevel.1=一级告警
common.eventLevel.2=二级告警
common.eventLevel.3=三级告警
common.eventLevel.4=四级告警
common.guardian.sendControl=操作员：%s 审核员：%s 控制值：%s
common.guardian.controlMeanings=控制含义：%s

common.areaGroup=片区分组

equipment.manage.status.signalpoint = 点位总数
equipment.manage.status.connectstatus = 通信状态
equipment.manage.status.alarmstatus = 告警状态
equipment.manage.status.alarmstatus.normal = 正常
equipment.manage.status.point.index = 序号
equipment.manage.status.point.name = 测点名称
equipment.manage.status.point.signalid = 参数ID
equipment.manage.status.point.channelno = 参数号
equipment.manage.status.point.loopno = 回路号

search.it.device.title=查找IT设备

# 图表相关文本
report.chartImageGenerator.axis.time=时间
report.chartImageGenerator.axis.value=值
report.chartImageGenerator.date.format=日期: {0}

# 电池图表相关文本
report.chartImageGenerator.battery.voltage=电压
report.chartImageGenerator.battery.current=电流
report.chartImageGenerator.battery.capacity=容量 (mAh)
report.chartImageGenerator.battery.voltage.unit=电压 (V)
report.chartImageGenerator.battery.current.unit=电流 (A)
report.chartImageGenerator.battery.temperature.unit=温度 (°C)

# 图表类型
report.chartImageGenerator.type.voltage=放电电压
report.chartImageGenerator.type.current=放电电流
report.chartImageGenerator.type.voltage.vs.capacity=电压vs容量

# BatteryDischargeSheetCreator相关文本
report.batteryDischargeSheetCreator.data=数据
report.batteryDischargeSheetCreator.dischargeData=放电数据
report.batteryDischargeSheetCreator.timeRange=时间区间
report.batteryDischargeSheetCreator.name=名称
report.batteryDischargeSheetCreator.totalVoltage=总电压
report.batteryDischargeSheetCreator.totalCurrent=总电流
report.batteryDischargeSheetCreator.dischargedCapacity=已放容量
report.batteryDischargeSheetCreator.totalCapacity=总容量
report.batteryDischargeSheetCreator.remainingCapacityRate=剩余容量率
report.batteryDischargeSheetCreator.cell=单体
report.batteryDischargeSheetCreator.voltage=电压
report.batteryDischargeSheetCreator.current=电流
report.batteryDischargeSheetCreator.capacity=容量
report.batteryDischargeSheetCreator.temperature=温度
report.batteryDischargeSheetCreator.time=时间
report.batteryDischargeSheetCreator.dischargeCharts=放电图表
report.batteryDischargeSheetCreator.dischargeVoltage=放电电压
report.batteryDischargeSheetCreator.dischargeCurrent=放电电流
report.batteryDischargeSheetCreator.cellVoltage=单体电压
report.batteryDischargeSheetCreator.cellTemperature=单体温度

common.report.form.resourceStructureName=层级名称
common.report.form.equipmentCategory=设备类型
common.report.form.ipAddress=IP地址
common.report.form.samplerUnitName=采集单元名称
common.report.form.address=采集单元地址
common.report.form.equipmentTemplateName=设备模板名称
common.report.form.resourceStructureId=层级ID
common.report.form.equipmentId=设备ID
common.report.form.stationId=局站ID
common.report.form.stationName=局站名称
common.report.form.monitorUnitId=监控单元ID

# 指标依赖校验
complexIndex.dependency.indexIdCannotEmpty=指标ID不能为空
complexIndex.dependency.invalidFormatMustBeNumber=格式无效，必须为数字
complexIndex.dependency.indexIdValidationPassed=指标计算顺序验证通过