package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.dto.IdValueDTO;
import com.siteweb.monitoring.entity.StationStructure;
import org.apache.ibatis.annotations.MapKey;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface StationStructureMapper extends BaseMapper<StationStructure> {
    StationStructure findByStationId(Integer stationId);
    @MapKey("stationid")
    Map<Integer,StationStructure> findStationStructures(List<Integer> stationids, @Param("groupid") Integer groupid);

    List<IdValueDTO<Integer,String>> findStationStructureIdLabel(@Param("stationIds") List<Integer> stationIds);
}
