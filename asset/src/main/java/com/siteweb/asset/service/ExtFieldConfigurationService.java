package com.siteweb.asset.service;

import com.siteweb.asset.dto.ExtFieldConfigurationDTO;
import com.siteweb.asset.entity.ExtFieldConfiguration;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: lzy
 * @Date: 2023/3/10 13:24
 */
public interface ExtFieldConfigurationService {

    /**
     * 根据扩展表查询扩展字段
     * @param extTable 扩展表名
     */
    List<ExtFieldConfiguration> findByExtTable(String extTable);

    /**
     * 根据扩展表，扩展表主键id查询关联扩展字段
     * @param extTable 扩展表名
     * @param extTablePkId 扩展表主键id
     */
    List<ExtFieldConfigurationDTO> findByExtTableWithExtValue(String extTable, Integer extTablePkId);

    /**
     * 获取单个扩展字段
     * @param extId 扩展字段id
     */
    ExtFieldConfiguration findById(Integer extId);

    /**
     * 查询所有
     */
    List<ExtFieldConfiguration> findAll();

    /**
     * 创建
     * @param extFieldConfiguration 扩展字段对象
     */
    ExtFieldConfiguration create(ExtFieldConfiguration extFieldConfiguration);

    /**
     * 更新
     * @param extFieldConfiguration 扩展字段对象
     */
    ExtFieldConfiguration updateById(ExtFieldConfiguration extFieldConfiguration);

    /**
     * 删除
     * @param id 扩展字段id
     */
    void deleteById(Integer id);

    /**
     * 批量删除
     * @param extFieldIds 扩展字段id集合
     */
    void deleteByIds(Collection<Integer> extFieldIds);

    List<ExtFieldConfiguration> findByExtTableWithExtIds(String extTable, Collection<Integer> extIds);

    /**
     * 查询扩展表数据，
     * reversal:
     *          true: map<扩展字段id, map<扩展表value，扩展表label>>
     *          false: map<扩展字段id, map<扩展表label，扩展表value>>
     * @param extTable 扩展表
     * @param reversal 反转map数据
     */
    Map<Integer, Map<String, String>> findExtFieldTableDataMap(String extTable, boolean reversal);

    /**
     * 加载扩展字段选项数据
     * @param extFieldId 扩展字段
     * @param label 展示值问题
     */
    List<Map<String, Object>> loadExtFieldOptionData(Integer extFieldId, String label, Integer pageNumber, Integer pageSize);

    String getExtFieldValueLabelDataByExtValId(Integer extValId);
}
